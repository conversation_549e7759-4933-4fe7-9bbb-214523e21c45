<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/EthiopiaPayManager.h</key>
		<data>
		QpxiOGmWNAHTEE/va1aI3aH2HkU=
		</data>
		<key>Headers/EthiopiaPaySDK.h</key>
		<data>
		OB2PPQe70+UIopSueGzin54Efts=
		</data>
		<key>Info.plist</key>
		<data>
		bmNrtWssPpD23FfFo+MFnKNC9d0=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		nl5lSAk7pH7RahSacwGg7ocM/sM=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/EthiopiaPayManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			QpxiOGmWNAHTEE/va1aI3aH2HkU=
			</data>
			<key>hash2</key>
			<data>
			XfnT5n2u2HLCCIE2ZSHQdmTTakrFIZh4qWCy1kJhZtQ=
			</data>
		</dict>
		<key>Headers/EthiopiaPaySDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			OB2PPQe70+UIopSueGzin54Efts=
			</data>
			<key>hash2</key>
			<data>
			upZvEqWkjq86G2Gp69TVHdVS7WAF1yH11SKxisq2fOY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			nl5lSAk7pH7RahSacwGg7ocM/sM=
			</data>
			<key>hash2</key>
			<data>
			N8ks+7xWbZjp70h7b6ndhbFrbooaLIzX98ba5xj6MTs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
