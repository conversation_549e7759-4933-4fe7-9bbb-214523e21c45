// ignore_for_file: no_logic_in_create_state, must_be_immutable

import 'package:ekub/exports/screens.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

import '../../../models/user.dart';
import '../../../repository/user_repos.dart';
import '../../account/login_screen.dart';
import '../../themes/ThemeProvider.dart';
import '../admin/actions/user_info.dart';
import '../admin/member_equbs.dart';
import '../admin/members.dart';
import '../ekubs/equbs.dart';
import 'cubit/botttom_nav_cubit.dart';

class HomeScreen extends StatefulWidget {
  static const routeName = "/home_screen";
  HomeScreenArgs args;

  HomeScreen({super.key, required this.args});

  @override
  State<HomeScreen> createState() => _HomeScreenState(args);
}

class _HomeScreenState extends State<HomeScreen> {
  final HomeScreenArgs args;
  _HomeScreenState(this.args);

  var proLoaded = false;
  late User user;

  // final _cPageNavigation = [
  //   const HomePage(),
  //   MembersScreen(),
  //   const NotificationPage(),
  //   const ProfilePage(),
  // ];
  final _aPageNavigation = [
    const HomePage(),
    MembersScreen(),
    const EqubsScreen(),
  ];
  late ThemeProvider themeProvider;
  var auth = AuthDataProvider(httpClient: http.Client());

  @override
  void initState() {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.white,
      statusBarIconBrightness: Brightness.light,
    ));
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _loadProfile();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("Navigated to HomeScreen: ${args.isAdmin} ${proLoaded}");
    return BlocBuilder<BottomNavCubit, int>(
      builder: (context, state) {
        return Scaffold(
          body: args.isAdmin
              ? _buildBody(state)
              : proLoaded
                  ? _prepareScreen()
                  : Container(),
          bottomNavigationBar: args.isAdmin ? _buildBottomNav() : null,
        );
      },
    );
  }

  _loadProfile() {
    auth.getUserData().then((value) => {
          setState(() {
            user = value;
            proLoaded = true;
          })
        });
  }

  Widget _prepareScreen() {
    if (args.role == "equb_collector") {
      return MembersScreen(
          args: MemberEqubsArgs(
        member: Member(
            id: int.parse(user.id ?? "0"),
            fullName: user.fullName,
            phone: user.phoneNumber,
            gender: user.gender),
        isOnline: args.isAdmin,
        isAdmin: false,
        // role: args.role,
      ));
    } else if (args.role == "member") {
      return MemberEqubs(
        args: MemberEqubsArgs(
            // role: args.role,
            member: Member(
                id: user.memberId == "null" ? 0 : int.parse(user.memberId!),
                // id: 2,

                fullName: user.fullName,
                phone: user.phoneNumber,
                gender: user.gender),
            isAdmin: false,
            isOnline: args.isAdmin),
      );
    } else {
      return UserInfo(
          args: UserInfoArgs(
              logout: true,
              message: "Unable to identify user role!",
              button: "Try again",
              auth: auth));
    }
  }

  Widget _buildBody(int index) {
    return
        // args.isAdmin
        //     ?
        _aPageNavigation.elementAt(index);
    // : _cPageNavigation.elementAt(index);
  }

  Widget _buildBottomNav() {
    return BottomNavigationBar(
        currentIndex: context.read<BottomNavCubit>().state,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: themeProvider.getLightColor,
        unselectedItemColor: themeProvider.getColor,
        //backgroundColor: ColorProvider.primaryDarkColor,
        elevation: 8,
        onTap: _getChangeBottomNav,
        items: args.isAdmin ? _adminMenus() : _collectorMenus());
  }

  _adminMenus() {
    return [
      BottomNavigationBarItem(
          icon: const Icon(Icons.home_outlined),
          label: EkubLocalization.of(context)!.translate("home")),
      BottomNavigationBarItem(
          icon: const Icon(Icons.group_outlined),
          label: EkubLocalization.of(context)!.translate("members")),
      BottomNavigationBarItem(
          icon: const Icon(Icons.payment_outlined),
          label: EkubLocalization.of(context)!.translate("ekubs")),
    ];
  }

  _collectorMenus() {
    return [
      const BottomNavigationBarItem(icon: Icon(Icons.group), label: "Members"),
      const BottomNavigationBarItem(
          icon: Icon(Icons.notification_important), label: 'Ekubs'),
      const BottomNavigationBarItem(icon: Icon(Icons.person), label: "Profile"),
    ];
  }

  void _getChangeBottomNav(int index) {
    context.read<BottomNavCubit>().updateIndex(index);
  }
}

void gotoSignIn(BuildContext context) {
  var auth = AuthDataProvider(httpClient: http.Client());
  auth.logOut();
  LoginScreenArgs argument = LoginScreenArgs(isOnline: true);
  Navigator.pushNamedAndRemoveUntil(
      context,
      kIsWeb ? WelcomeScreen.routeName : LoginScreen.routeName,
      (Route<dynamic> route) => false,
      arguments: argument);
}
