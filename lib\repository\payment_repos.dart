import 'package:ekub/service/headers.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class PaymentRepository {
  final String _baseUrl = RequestHeader.baseApp;

  Future<Map<String, dynamic>?> payWithCBEBirr({
    required String amount,
    required String equbId,
    required String memberId,
  }) async {
    final uri = Uri.parse('$_baseUrl/cbegateway');

    try {
      final response = await http
          .post(
            uri,
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              'amount': amount,
              'equb_id': equbId,
              'member_id': memberId,
            }),
          )
          .timeout(const Duration(minutes: 2));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        debugPrint('Error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Exception: $e');
      return null;
    }
  }
}
