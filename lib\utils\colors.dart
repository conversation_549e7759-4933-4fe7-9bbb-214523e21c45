import 'package:flutter/material.dart';

class ColorProvider {
  var primaryColor = Colors.yellow;
  var primaryDarkColor = Colors.amber;

  var secondaryColor = Colors.orange;
  var secondaryDarkColor = Colors.deepOrange;

  var primaryDeepOrange = const Color(0xFF1B1F4F);
  static Color backgroundColor = const Color(0xFFF0F3FF);
  static Color primary = const Color(0xFF1B1F4F);
  var primaryOrange = const Color(0xFF45B978);
  var primaryDeepGreen = Colors.green;
  var primaryDeepPurple = Colors.deepPurpleAccent;
  var primaryDeepRed = Colors.red;
  var primaryRed = Colors.redAccent;
  var primaryDeepBlue = Colors.blue;
  var primaryBlue = Colors.lightBlueAccent;
  var primaryDeepBlack = Colors.black54;
  var primaryDeepTeal = Colors.teal;
  var primaryTeal = Colors.green;
  var primaryDeepCheetah = const Color(0xFF00cd34);

  var primaryTextColor = Colors.black54;
  var primaryDarkTextColor = Colors.black;

  var secondaryTextColor = Colors.white;
  var secondaryDarkTextColor = Colors.grey;

  var errorColor = Colors.red;
  var successColor = Colors.green;

  var blue = Colors.blue;
  var green = Colors.green;
  var yellow = Colors.yellow;
  var orange = Colors.orange;
  var white = Colors.white;
}
