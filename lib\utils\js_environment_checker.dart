import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// A utility class to check the JavaScript environment
class JSEnvironmentChecker {
  /// Check if window.consumerapp is available in the JavaScript environment
  static Future<void> checkConsumerApp(BuildContext context) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Checking JavaScript environment...'),
            ],
          ),
        );
      },
    );

    try {
      // Create a WebView to check the JavaScript environment
      final webView = InAppWebView(
        initialOptions: InAppWebViewGroupOptions(
          crossPlatform: InAppWebViewOptions(
            javaScriptEnabled: true,
            useShouldOverrideUrlLoading: true,
            useOnLoadResource: true,
          ),
        ),
        onWebViewCreated: (controller) async {
          // Load a blank page
          await controller.loadData(data: '''
            <!DOCTYPE html>
            <html>
            <head>
              <title>JavaScript Environment Check</title>
            </head>
            <body>
              <h1>Checking JavaScript Environment</h1>
              <script>
                // Check if window.consumerapp is available
                if (window.consumerapp) {
                  document.write('<p>window.consumerapp is available</p>');
                  document.write('<p>Type: ' + typeof window.consumerapp + '</p>');
                  
                  // Check if window.consumerapp.evaluate is available
                  if (typeof window.consumerapp.evaluate === 'function') {
                    document.write('<p>window.consumerapp.evaluate is a function</p>');
                  } else {
                    document.write('<p>window.consumerapp.evaluate is NOT a function</p>');
                    document.write('<p>Type: ' + typeof window.consumerapp.evaluate + '</p>');
                  }
                } else {
                  document.write('<p>window.consumerapp is NOT available</p>');
                }
                
                // Log to console for debugging
                console.log('JavaScript environment check complete');
                if (window.consumerapp) {
                  console.log('window.consumerapp is available');
                  console.log('Type: ' + typeof window.consumerapp);
                  
                  if (typeof window.consumerapp.evaluate === 'function') {
                    console.log('window.consumerapp.evaluate is a function');
                  } else {
                    console.log('window.consumerapp.evaluate is NOT a function');
                    console.log('Type: ' + typeof window.consumerapp.evaluate);
                  }
                } else {
                  console.log('window.consumerapp is NOT available');
                }
              </script>
            </body>
            </html>
          ''');
        },
        onLoadStop: (controller, url) async {
          // Get the HTML content
          final html = await controller.getHtml();
          print('\n\n==== JAVASCRIPT ENVIRONMENT CHECK ====');
          print('HTML content: $html');
          print('======================================\n');
          
          // Execute JavaScript to check if window.consumerapp is available
          final jsResult = await controller.evaluateJavascript(source: '''
            (function() {
              var result = {
                consumerappAvailable: false,
                evaluateAvailable: false,
                consumerappType: 'undefined',
                evaluateType: 'undefined'
              };
              
              if (window.consumerapp) {
                result.consumerappAvailable = true;
                result.consumerappType = typeof window.consumerapp;
                
                if (typeof window.consumerapp.evaluate === 'function') {
                  result.evaluateAvailable = true;
                  result.evaluateType = 'function';
                } else if (window.consumerapp.evaluate !== undefined) {
                  result.evaluateType = typeof window.consumerapp.evaluate;
                }
              }
              
              return JSON.stringify(result);
            })();
          ''');
          
          print('\n\n==== JAVASCRIPT CHECK RESULT ====');
          print('JS Result: $jsResult');
          print('==================================\n');
          
          // Close the dialog
          if (context.mounted && Navigator.canPop(context)) {
            Navigator.pop(context);
          }
          
          // Show the result dialog
          if (context.mounted) {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('JavaScript Environment Check'),
                  content: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text('JavaScript check result: $jsResult'),
                        const SizedBox(height: 16),
                        const Text('HTML Content:', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text(html ?? 'No HTML content available'),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('OK'),
                    ),
                  ],
                );
              },
            );
          }
        },
        onConsoleMessage: (controller, consoleMessage) {
          // Log console messages from JavaScript
          print('JS Console: ${consoleMessage.message}');
        },
      );
      
      // Show the WebView in a dialog
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('JavaScript Environment'),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: webView,
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      // Close loading dialog if it's still showing
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
      
      // Show error dialog
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Error'),
              content: Text('Failed to check JavaScript environment: $e'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      }
    }
  }
}
