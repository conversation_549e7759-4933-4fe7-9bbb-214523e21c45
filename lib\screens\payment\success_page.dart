import 'package:ekub/routs/shared.dart';
import 'package:flutter/material.dart';
import 'package:ekub/screens/dashboard/admin/member_equbs.dart';
import 'package:ekub/models/members.dart';

class SuccessPage extends StatelessWidget {
  final Member member;

  SuccessPage({required this.member});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Payment Success'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 100.0,
            ),
            SizedBox(height: 20),
            Text(
              'Your payment was successful!',
              style: TextStyle(fontSize: 24),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.pushReplacementNamed(
                  context,
                  MemberEqubs.routeName,
                  arguments: MemberEqubsArgs(
                    member: member,
                    isOnline: true,
                  ),
                );
              },
              child: Text('Back to Home'),
            ),
          ],
        ),
      ),
    );
  }
}
