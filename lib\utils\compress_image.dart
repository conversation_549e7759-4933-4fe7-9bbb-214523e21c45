import 'dart:io';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';

compressAndGetFile(File file, String targetPath) async {
  XFile? result;
  try {
    result = await FlutterImageCompress.compressAndGetFile(
        file.absolute.path, targetPath,
        quality: 40, rotate: 0);
  } catch (err) {
    return XFile(file.path);
  }
  return result;
}
