import 'dart:convert'; // For jsonEncode, base64Encode, utf8.encode
import 'dart:math'; // For min function
import 'package:ekub/exports/models.dart';
import 'package:ekub/exports/screens.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/admin/tabs/member_payments.dart';
import 'package:ekub/screens/dashboard/admin/tabs/member_tabs.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'dart:js' as js;

// App imports
import 'package:ekub/models/members.dart';
import 'package:ekub/screens/payment/payment_success.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import '../service/headers.dart';
import 'constants.dart';
import 'js_interop.dart';
import 'session.dart';

/// JSPaymentExecutor handles the payment process by:
/// 1. Making a backend request to initialize the payment
/// 2. Processing the response and extracting necessary data (rawRequest)
/// 3. Executing JavaScript code with the response data on web platforms
/// 4. Simulating the process on non-web platforms
/// 5. Handling errors and showing appropriate dialogs
class JSPaymentExecutor {
  // Static flag to track if we're already handling a 405 error
  static bool isHandling405Error = false;

  // Global key for navigation
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  // Store the current member for use in JavaScript callbacks
  static Member? _currentMember;

  /// Executes the payment process
  ///
  /// This method handles the entire payment flow:
  /// - Shows a loading dialog
  /// - Makes a backend request to initialize the payment
  /// - Executes JavaScript with the response data on web platforms
  /// - Simulates the process on non-web platforms
  /// - Handles errors and shows appropriate dialogs
  /// - Navigates to success page on completion
  ///
  /// @param context The BuildContext for showing dialogs and navigation
  /// @param title The payment title
  /// @param amount The payment amount
  /// @param member The member making the payment
  /// @param equbId The ID of the equb
  static Future<void> executePayment({
    required BuildContext context,
    required String title,
    required String amount,
    required Member member,
    required String equbId,
    required int index,
    required Equb? equb,
    required String? role,
    required double? remainingAmount,
  }) async {
    // Store initial mounted state for debugging
    debugPrint("⚠️ INITIAL CONTEXT MOUNTED: ${context.mounted}");

    if (kIsWeb) {
      // For Payment
      setupJavaScriptCallback(
        context,
        index,
        member,
        equb,
        role,
        remainingAmount,
      );
    }

    // Show loading dialog in a microtask to avoid build cycle issues
    // This ensures we're not in the middle of a build when showing the dialog
    if (context.mounted) {
      // Show loading screen
      _showLoadingDialog(context);
      debugPrint("⚠️ LOADING DIALOG SHOWN");
    } else {
      debugPrint("⚠️ CONTEXT NOT MOUNTED, CANNOT SHOW LOADING DIALOG");
    }
    // await Future.microtask(() {
    // });

    var uiContext = context.mounted ? context : navigatorKey.currentContext!;

    try {
      // Log payment details for debugging
      _logPaymentDetails(title, amount, member.id);

      // Initialize payment object with default values
      // This will be updated with actual values from the backend response
      final paymentObject = _createInitialPaymentObject(title, amount);

      // Store the current member for use in JavaScript callbacks
      _currentMember = member;

      // Register JavaScript callback handlers if on web platform
      if (kIsWeb) {
        try {
          // These handlers will be called by the JavaScript code
          js.context.callMethod('eval', [
            """
            if (typeof window.navigateToSuccess === 'undefined') {
              window.navigateToSuccess = function() {
                console.log('navigateToSuccess called');
                // This will be intercepted by Dart
              };
            }

            if (typeof window.showErrorDialog === 'undefined') {
              window.showErrorDialog = function(message) {
                console.log('showErrorDialog called with: ' + message);
                // This will be intercepted by Dart
              };
            }
          """
          ]);

          // Register the Dart callbacks
          js.context['navigateToSuccess'] = () {
            debugPrint("⚠️ JS CALLED navigateToSuccess");
            navigateToSuccessPage(member);
          };

          js.context['showErrorDialog'] = (dynamic message) {
            String errorMessage =
                message is String ? message : message.toString();
            debugPrint("⚠️ JS CALLED showErrorDialog: $errorMessage");
            showJavaScriptErrorDialog(errorMessage);
          };

          debugPrint("⚠️ Successfully registered JavaScript callbacks");
        } catch (e) {
          debugPrint("⚠️ Error registering JavaScript callbacks: $e");
        }
      }

      // Variable to store raw request data from backend response
      String rawRequest = "";

      try {
        // Log backend request details
        _logBackendRequestDetails(member.id, equbId, amount);

        // Make actual backend request
        debugPrint("⚠️ MAKING ACTUAL BACKEND REQUEST");
        final response = await _makeBackendRequest(equbId, amount, member.id);

        // Log the actual response for debugging
        debugPrint("⚠️ RECEIVED ACTUAL BACKEND RESPONSE");
        debugPrint("⚠️ STATUS CODE: ${response.statusCode}");
        debugPrint(
            "⚠️ RESPONSE BODY: ${response.body.substring(0, min(100, response.body.length))}${response.body.length > 100 ? "..." : ""}");

        // Log the actual response for debugging
        Session().logSession("payment_actual_response", response.body);

        debugPrint("⚠️ RESPONSE STATUS CODE: ${response.statusCode}");
        debugPrint("⚠️ CONTEXT AFTER RESPONSE: ${context.mounted}");
        // Handle HTTP 405 error (Method Not Allowed)
        if (response.statusCode == 405) {
          // Log the error for debugging
          Session().logError("payment_http_error",
              "Status Code: ${response.statusCode}, Body: ${response.body}");

          debugPrint(
              "⚠️ HANDLING 405 ERROR - Context mounted: ${context.mounted}");

          // Check if we're already handling a 405 error
          // This prevents multiple error dialogs from being shown
          if (!isHandling405Error) {
            isHandling405Error = true;

            // Remove any popup first
            _removePopupDialog(context.mounted ? context : null);
            uiContext =
                context.mounted ? context : navigatorKey.currentContext!;
            // Show a generic error message for 405
            _showErrorDialog(uiContext, "Error Processing",
                "Something went wrong. Please try again later.");
          }

          // Reset the flag when we're done
          isHandling405Error = false;

          return; // Exit the method
        }

        // Parse response data
        var responseData;
        try {
          debugPrint(
              "⚠️ ATTEMPTING TO PARSE RESPONSE BODY: ${response.body.substring(0, min(100, response.body.length))}${response.body.length > 100 ? "..." : ""}");
          responseData = jsonDecode(response.body) as Map<String, dynamic>;
          debugPrint("⚠️ SUCCESSFULLY PARSED RESPONSE BODY AS JSON");
        } catch (e) {
          debugPrint("⚠️ ERROR PARSING RESPONSE BODY AS JSON: $e");
          // Create a default response data object
          responseData = {
            "code": response.statusCode,
            "message": "Response body is not valid JSON",
            "rawRequest": "SIMULATED_rawRequest_FOR_TESTING_12345"
          };
          debugPrint("⚠️ USING DEFAULT RESPONSE DATA: $responseData");
        }

        // Log the parsed response for debugging
        _logBackendResponse(responseData);
        uiContext = context.mounted ? context : navigatorKey.currentContext!;

        // Handle HTTP 400 error (Bad Request) or 405 error (Method Not Allowed)
        if (response.statusCode == 400 || response.statusCode == 405) {
          // Log the error for debugging
          Session().logError("payment_http_error",
              "Status Code: ${response.statusCode}, Body: ${response.body}");

          // Debug log for context mounted state
          debugPrint("⚠️ CONTEXT MOUNTED CHECK (400/405): ${context.mounted}");

          // Prepare error message
          String title = "Info";
          String message = responseData['message'] ?? "An error occurred";

          // Set generic message for 405 Method Not Allowed
          if (response.statusCode == 405) {
            title = "Error Processing";
            message = "Something went wrong. Please try again later.";
          }

          // Remove any popup first
          _removePopupDialog(context.mounted ? context : null);
          // Show the error dialog
          uiContext = context.mounted ? context : navigatorKey.currentContext!;
          if (title == "Info") {
            _showInfoDialog(uiContext, title, message);
          } else {
            _showErrorDialog(uiContext, title, message);
          }

          return; // Exit the method
        }

        // Handle successful HTTP response (200 OK)
        if (response.statusCode == 200) {
          // Log successful response for debugging
          Session()
              .logSession("payment_success_response", responseData.toString());

          // Handle server error code within a successful HTTP response
          if (responseData['code'] == 500) {
            // Log server error for debugging
            Session().logError("payment_server_error", responseData['message']);
            throw Exception(responseData['message']);
          }

          // Extract raw request data from the response
          debugPrint("⚠️ RESPONSE DATA TYPE: ${responseData.runtimeType}");
          debugPrint("⚠️ RESPONSE DATA: $responseData");

          if (responseData.containsKey("rawRequest")) {
            debugPrint("⚠️ RAW REQUEST EXISTS");
            debugPrint(
                "⚠️ RAW REQUEST TYPE: ${responseData["rawRequest"].runtimeType}");

            // Check if rawRequest is a string or an object
            if (responseData["rawRequest"] is String) {
              debugPrint("⚠️ RAW REQUEST IS STRING");
              rawRequest = responseData["rawRequest"];
            } else {
              debugPrint("⚠️ RAW REQUEST IS NOT STRING, CONVERTING TO JSON");
              // If it's an object, convert it to JSON string
              try {
                rawRequest = jsonEncode(responseData["rawRequest"]);
                debugPrint(
                    "⚠️ SUCCESSFULLY CONVERTED RAW REQUEST TO JSON STRING");
              } catch (e) {
                debugPrint("⚠️ ERROR CONVERTING RAW REQUEST TO JSON: $e");
                // Fallback to default if conversion fails
                rawRequest = "SIMULATED_rawRequest_FOR_TESTING_12345";
              }
            }
            debugPrint("⚠️ FINAL RAW REQUEST: $rawRequest");
          } else {
            debugPrint("⚠️ RAW REQUEST DOES NOT EXIST IN RESPONSE");
            // If rawRequest is null, use a default value
            rawRequest = "SIMULATED_rawRequest_FOR_TESTING_12345";
            debugPrint("⚠️ Using default raw request: $rawRequest");
          }

          // Update payment object with the raw request from backend
          final params = paymentObject['params'] as Map<String, dynamic>;
          params['rawRequest'] = rawRequest;

          // Log the final processed response for debugging
          Session().logSession(
              "payment_processed_response", responseData.toString());

          // Execute JavaScript on web platforms or simulate on non-web platforms
          if (kIsWeb) {
            try {
              // Log JavaScript execution for debugging
              // debugPrint("⚠️ EXECUTING JAVASCRIPT WITH PAYMENT OBJECT:");
              // debugPrint("⚠️ ${jsonEncode(paymentObject)}");
              Session().logSession("js_execution",
                  "Executing JavaScript on web with: ${jsonEncode(paymentObject)}");

              // Execute JavaScript code with the payment object containing rawRequest
              // debugPrint("⚠️ CALLING _executeJavaScript...");
              final jsResult = await _executeJavaScript(paymentObject);
              // debugPrint("⚠️ _executeJavaScript COMPLETED");

              // Log JavaScript execution result for debugging
              // debugPrint("⚠️ JAVASCRIPT EXECUTION RESULT: $jsResult");
              Session().logSession("js_execution_result", jsResult);
              return;
            } catch (e) {
              // Handle JavaScript execution errors
              // Remove any popup first
              _removePopupDialog(context.mounted ? context : null);
              // Show the error dialog
              uiContext =
                  context.mounted ? context : navigatorKey.currentContext!;
              _showErrorDialog(
                  uiContext, 'Error', 'Failed to execute JavaScript: $e');

              return; // Exit the method if JavaScript execution failed
            }
          } else {
            // Simulate JavaScript execution on non-web platforms (mobile, desktop)
            return await _simulateJavaScriptExecution(paymentObject);
          }

          // The payment result will be handled by the JavaScript callback
          // We'll just close the loading dialog here
          // Handle JavaScript execution errors
          // Remove any popup first
          _removePopupDialog(context.mounted ? context : null);
          // Show the error dialog
          uiContext = context.mounted ? context : navigatorKey.currentContext!;
          _showErrorDialog(uiContext, 'Error', 'Failed to execute JS: $e');

          // Note: The actual payment result handling is now done in the JavaScript callback
          // The callback will call handlePaymentResult which will show success/error UI

          return; // Exit the method after successful payment
        } else {
          // Handle other HTTP status codes
          // Debug log for context mounted state
          // debugPrint(
          //     "⚠️ CONTEXT MOUNTED CHECK (other status): ${context.mounted}");

          // Prepare error message based on status code
          String title;
          String message;

          switch (response.statusCode) {
            case 401:
              title = 'Unauthorized';
              message =
                  'You are not authorized to make this payment. Please log in again.';
              break;
            case 403:
              title = 'Forbidden';
              message = 'You do not have permission to make this payment.';
              break;
            case 404:
              title = 'Not Found';
              message =
                  'The payment service could not be found. Please try again later.';
              break;
            case 500:
            case 502:
            case 503:
            case 504:
              title = 'Server Error';
              message =
                  'The payment server is currently unavailable. Please try again later.';
              break;
            default:
              title = 'Error (${response.statusCode})';
              message = responseData['message'] ??
                  'An error occurred during payment processing';
              break;
          }

          // Use a delayed execution to ensure we're not in the middle of a build cycle
          Future.microtask(() {
            // Remove any popup first
            _removePopupDialog(context.mounted ? context : null);
            uiContext =
                context.mounted ? context : navigatorKey.currentContext!;
            // Show the error dialog
            _showErrorDialog(uiContext, title, message);
          });

          return; // Exit the method
        }

        // Commented code removed for clarity
      } catch (e, stackTrace) {
        // Handle backend request errors
        debugPrint(
            "⚠️ CONTEXT MOUNTED CHECK (backend error): ${context.mounted}");
        debugPrint("⚠️ Backend request error: $e");
        debugPrint("⚠️ Error type: ${e.runtimeType}");
        debugPrint("⚠️ Stack trace: $stackTrace");

        // Remove any popup first
        _removePopupDialog(context.mounted ? context : null);
        // Show error dialog
        uiContext = context.mounted ? context : navigatorKey.currentContext!;
        _showErrorDialog(
            uiContext, 'Payment Error', 'Failed to initialize payment: $e');

        return; // Exit the method if backend request failed
      }
    } catch (e) {
      // Handle any unexpected errors in the main try-catch block
      debugPrint("⚠️ CONTEXT MOUNTED CHECK (main error): ${context.mounted}");
      debugPrint("⚠️ Main error: $e");

      // Remove any popup first
      _removePopupDialog(context.mounted ? context : null);

      // Show error dialog
      uiContext = context.mounted ? context : navigatorKey.currentContext!;
      _showErrorDialog(
          uiContext, 'Payment Error', 'Failed to initialize payment: $e');
    }
  }

  // Auto login
  static Future<String> executeJavaScriptAutologin(BuildContext context) async {
    // debugPrint("Proccessing autologin: Preparing JavaScript execution");

    if (kIsWeb) {
      // debugPrint("Proccessing autologin: Preparing JavaScript execution");
      // For Autologin
      setupJavaScriptAutologinCallback(context);
    }

    try {
      final loginObject = jsonEncode(_createAutoLoginObject());
      // Log JavaScript execution for debugging
      debugPrint("⚠️ EXECUTING JAVASCRIPT WITH PAYMENT OBJECT:");
      debugPrint("⚠️ $loginObject");
      Session().logSession("js_execution",
          "Executing JavaScript on web with: ${jsonEncode(loginObject)}");

      // Set the payment object as a global JavaScript variable
      js.context.callMethod('eval', [
        """
        // Store payment data in a global variable for the payment processor to use
        window._authenticationObject = $loginObject;
        console.log('Payment data ready for processing');
      """
      ]);
    } catch (e) {
      // Log the error and return an error message
      debugPrint(
          "Authentication error: Failed to prepare authentication data: $e");
    }

    try {
      // Now execute the main JavaScript code that uses the global payment object
      final jsResult = await executeJavaScript("""
      (function() {
        // Initialize payment processing
        // console.log('Initializing authentication processing');

        // Check if payment object is available
        if (!window._authenticationObject) {
          console.error('Authentication object not found');
          return 'Error: Authentication object not found';
        }

        let callBackCount = 0;
        // Define the callback function that will be called by the third-party payment system
        window.handleAuthDataCallback = function(response) {
          if (callBackCount != 0) {
            return;
          }
          // Execute the function only once
          callBackCount++;
          try {
            // Pass the response to Dart's handleAuthToken function
            if (typeof window.handleAuthToken === 'function') {
              window.handleAuthToken(response, "Payment message");
            } else {
              console.error('Dart handleAuthToken function is not defined');
            }
          } catch (error) {
            console.error('Processing error:', error);
            return;
          }
        };

        // Check if window.consumerapp exists
        if (window.consumerapp) {
          // console.log('window.consumerapp exists');

          if (typeof window.consumerapp.evaluate === 'function') {
            // console.log('window.consumerapp.evaluate is a function');
            try {
              // Use the payment object from the global variable
              const result = window.consumerapp.evaluate(JSON.stringify(window._authenticationObject));
              // console.log('window.consumerapp.evaluate result:', result);
              return;
            } catch (error) {
              console.error('Error calling window.consumerapp.evaluate:', error);
              return;
            }
          } else {
            // console.log('window.consumerapp.evaluate is NOT a function');
            return;
          }
        } else {
          console.log('window.consumerapp does NOT exist, creating simulation');
          alert("[Testing] autologin");

          // Create a authentication service simulation for development/testing environments
          console.log('Creating authentication service simulation');

          // Define the authentication service interface
          window.consumerapp = {
            evaluate: function(authenticationDataStr) {
              console.log('Authentication service simulation activated');

              try {
                // Parse the authentication data
                const authenticationData = JSON.parse(authenticationDataStr);

                // Log authentication details for debugging
                console.log('Processing authentication with amount:',
                  authenticationData.params && authenticationData.params.amount ? authenticationData.params.amount : 'unknown');

                // Check if we should simulate a failure (for testing error handling)
                const simulateFailure = false; // Set to true to test error handling

                // Simulate the authentication processing with a delay
                // In production, this would be replaced by the actual authentication gateway
                setTimeout(function() {
                  if (simulateFailure) {
                    // Simulate a failed authentication
                    console.log('Simulating authentication failure');
                    window.handleAuthDataCallback('IUEJPFEIJf9qw0uefdoqjf0983fj029384f019u4jrofiqewjfqiwjeqporiuqwe==');
                  } else {
                    const testResponse = 'IUEJPFEIJf9qw0uefdoqjf0983fj029384f019u4jrofiqewjfqiwjeqporiuqwe==';
                    // Simulate a successful authentication
                    console.log('Simulating successful authentication');
                    window.handleAuthDataCallback(testResponse);
                  }
                }, 2000); // 2 second delay to simulate processing time

                return true; // Indicate that the authentication process has started
              } catch (error) {
                console.error('Authentication simulation error:', error);

                // Report the error through the callback
                window.handleAuthDataCallback('IUEJPFEIJf9qw0uefdoqjf0983fj029384f019u4jrofiqewjfqiwjeqporiuqwe==');

                return false; // Indicate that the authentication process failed to start
              }
            }
          };

          try {
            // Start the authentication process using the simulation
            console.log('Initiating simulated authentication process');

            const result = window.consumerapp.evaluate(JSON.stringify(window._authenticationObject));

            if (result) {
              console.log('Authentication process initiated successfully');
            } else {
              console.error('Failed to initiate authentication process');
              return;
            }
          } catch (error) {
            console.error('Error starting authentication process:', error);
            return;
          }
        }

        return 'JavaScript execution completed';
      })();
    """);
      debugPrint("⚠️ _executeJavaScript COMPLETED");

      // Log JavaScript execution result for debugging
      debugPrint("⚠️ JAVASCRIPT Authentication EXECUTION RESULT: $jsResult");
      Session().logSession("js_execution_result", jsResult);
      return jsResult;
    } catch (e) {
      // Log JavaScript Authentication execution result for debugging
      debugPrint("⚠️ JAVASCRIPT Authentication EXECUTION Error: $e");
      return "Error executing JavaScript: $e";
    }
  }

  static void setupJavaScriptAutologinCallback(
    BuildContext context,
  ) async {
    js.context['handleAuthToken'] =
        (dynamic res, String message, [dynamic data]) async {
      const String url =
          'https://virtualekubdash.com/api/auth/login-telebirr-miniapp';
      final Map<String, dynamic> body = {'authToken': res};

      try {
        final response = await _makeBackendCallbackRequest(
          url: url,
          body: body,
        );

        // Success response for registered user
        // final response = http.Response(
        //   jsonEncode({
        //     "code": 200,
        //     "user": {
        //       "id": 1068,
        //       "name": "Mikiyas Test",
        //       "email": "<EMAIL>",
        //       "phone_number": "+251933624757",
        //       "gender": "male",
        //       "role": "member",
        //       "enabled": 1,
        //       "member_id": 1097
        //     },
        //     "token_type": "Bearer",
        //     "token":
        //         "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3ZpcnR1YWxla3ViZGFzaC5jb20vYXBpL3RlbGViaXJyLW1pbmlhcHAvcmVnaXN0ZXItbWVtYmVyIiwiaWF0IjoxNzQ1ODM5MTA0LCJuYmYiOjE3NDU4MzkxMDQsImp0aSI6IllWcDZIQllvemVndmUwR3EiLCJzdWIiOiIxMDY4IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.tSkZtrjej4FdLi9zy8PviexNlRLjYwrXCBapgOq3ddg",
        //   }),
        //   200,
        // );

        // Success response for NOT registered user
        // final response = http.Response(
        //   jsonEncode({
        //     "code": 200,
        //     "phone_number": "+251953960596",
        //     "name": "Henok",
        //   }),
        //   200,
        // );

        // Parse the response body
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (response.statusCode == 200) {
          // Check if the user is already registered and redirect to home screen page
          if (responseData.containsKey('user') &&
              responseData['user'] != null) {
            const secureStorage = FlutterSecureStorage();
            await secureStorage.write(
                key: 'id', value: responseData['user']['id'].toString());
            await secureStorage.write(
                key: 'phone_number',
                value: responseData['user']['phone_number']);
            await secureStorage.write(
                key: 'full_name', value: responseData['user']['name']);
            await secureStorage.write(
                key: 'token', value: responseData['token']);

            await secureStorage.write(
                key: "email", value: responseData['user']['email'] ?? "");
            await secureStorage.write(
                key: "role", value: responseData['user']['role'] ?? "none");

            await secureStorage.write(
                key: "gender", value: responseData["user"]['gender'] ?? "");
            await secureStorage.write(
                key: "member_id",
                value: responseData["user"]['member_id'].toString());

            debugPrint("NAV: ${navigatorKey.currentContext}");

            // Dismiss the dialog after the operation is complete
            if (context.mounted) {
              Navigator.pop(context);
            }

            // Redirect to home page
            _openHomeScreen(
                navigatorKey.currentContext!,
                responseData['user']['role'] == "admin",
                responseData['user']['role']);
          } else {
            // Handle missing authToken
            // debugPrint("⚠️ user is not registered.");

            // Dismiss the dialog after the operation is complete
            if (context.mounted) {
              Navigator.pop(context);
            }

            // Redirect to registration page
            if (responseData.containsKey('phone_number') &&
                responseData['phone_number'] != null) {
              final phone = responseData['phone_number'];
              final name = responseData['name'];
              Navigator.pushReplacementNamed(
                  context, AutoRegisterScreen.routeName,
                  arguments: AutoRegisterScreenArgs(
                      isOnline: true, phoneNumber: phone, name: name));
            } else {
              _showErrorDialog(
                navigatorKey.currentContext!,
                "Error",
                "Authentication failed. Missing phone.",
              );
            }
          }
        } else {
          debugPrint('Auth Request Response failed: ${response.body}');

          // Dismiss the dialog after the operation is complete
          if (context.mounted) {
            Navigator.pop(context);
          }

          final message =
              (responseData.containsKey('message') && responseData['message'])
                  ? responseData['message']
                  : "Authentication failed. Please try again later.";
          _showErrorDialog(
            navigatorKey.currentContext!,
            response.statusCode == 500 ? "Error" : "Info",
            message,
          );
        }
      } catch (e) {
        debugPrint('Error making authentication response request: $e');

        // Dismiss the dialog after the operation is complete
        if (context.mounted) {
          Navigator.pop(context);
        }

        _showErrorDialog(
          navigatorKey.currentContext!,
          "Error",
          "An unexpected error occurred. Please try again later.",
        );
      }
    };
  }

  // ==================== HELPER METHODS ====================

  /// Shows a loading dialog to indicate payment processing
  static void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Container(
            width: 120, // Small square size
            height: 120, // Matches width
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  color: secondaryColor,
                  strokeWidth: 3, // Slightly thinner stroke for small size
                ),
                const SizedBox(height: 12), // Reduced spacing
                Text(
                  'Processing payment...',
                  style: Theme.of(context).textTheme.titleMedium,
                  // style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  //       fontSize: 12, // Smaller text
                  //     ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static void _removePopupDialog(BuildContext? context) {
    if (context != null) {
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    } else if (navigatorKey.currentContext != null) {
      try {
        Navigator.of(navigatorKey.currentContext!, rootNavigator: true).pop();
        // debugPrint("⚠️ Closed loading dialog using global navigator key");
      } catch (e) {
        debugPrint("⚠️ Error closing dialog with global key: $e");
      }
    }

    // Use a delayed execution to ensure we're not in the middle of a build cycle
    // Future.microtask(() {
    //   try {
    //     if (context.mounted) {
    //       // Try to close the loading dialog if it's showing
    //       try {
    //         if (Navigator.canPop(context)) {
    //           Navigator.pop(context);
    //           debugPrint("⚠️ Successfully closed loading dialog");
    //         }
    //       } catch (e) {
    //         debugPrint("⚠️ Error closing dialog: $e");
    //       }

    //       // Show a generic error message for 405
    //       _showErrorDialog(context, "Error",
    //           "Something went wrong. Please try again later.");
    //       debugPrint("⚠️ Showed 405 error dialog");
    //     } else {
    //       debugPrint(
    //           "⚠️ Context is no longer mounted, using global navigator key");

    //       // Use the global navigator key to close the loading dialog first
    //       if (navigatorKey.currentContext != null) {
    //         // Try to close any open dialogs first
    //         try {
    //           Navigator.of(navigatorKey.currentContext!, rootNavigator: true)
    //               .pop();
    //           debugPrint("⚠️ Closed loading dialog using global navigator key");
    //         } catch (e) {
    //           debugPrint("⚠️ Error closing dialog with global key: $e");
    //         }

    //         // Wait a moment before showing the error dialog
    //         Future.delayed(const Duration(milliseconds: 300), () {
    //           if (navigatorKey.currentContext != null) {
    //             _showErrorDialog(navigatorKey.currentContext!, "Error",
    //                 "Something went wrong. Please try again later.");
    //             debugPrint(
    //                 "⚠️ Showed 405 error dialog using global navigator key");
    //           } else {
    //             debugPrint(
    //                 "⚠️ Global navigator key context is null after delay");
    //           }
    //         });
    //       } else {
    //         debugPrint("⚠️ Global navigator key context is null");
    //       }
    //     }
    //   } finally {
    //     // Reset the flag when we're done
    //     isHandling405Error = false;
    //   }
    // });

    // // Wait a moment before showing the error dialog
    // Future.delayed(const Duration(milliseconds: 300), () {
    //   if (navigatorKey.currentContext != null) {
    //     _showErrorDialog(navigatorKey.currentContext!, title, message);
    //     debugPrint("⚠️ Showed error dialog using global navigator key");
    //   } else {
    //     debugPrint("⚠️ Global navigator key context is null after delay");
    //   }
    // });
  }

  /// Shows an error dialog with the specified title and message
  static Future<void> _showErrorDialog(
      BuildContext context, String title, String message) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(message),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );

    // return PanaraInfoDialog.show(
    //   context,
    //   title: title,
    //   // title: EkubLocalization.of(context)!.translate("error"),
    //   message: message,
    //   buttonText: EkubLocalization.of(context)!.translate("okay"),
    //   onTapDismiss: () {
    //     Navigator.pop(context);
    //   },
    //   imagePath: errorDialogIcon,
    //   panaraDialogType: PanaraDialogType.error,
    // );
  }

  /// Shows an error dialog with the specified title and message
  static _showInfoDialog(BuildContext context, String title, String message) {
    // // Wait a moment before showing the error dialog
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Info"),
          content: Text(message ?? "An error occurred"),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
              ),
              child: Text(EkubLocalization.of(context)!.translate("okay")),
            ),
          ],
        );
      },
    );
    // Future.delayed(const Duration(milliseconds: 300), () {
    // });
    // return showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     return AlertDialog(
    //       title: Text("info"),
    //       content: Text(message ?? "An error occurred"),
    //       actions: [
    //         ElevatedButton(
    //           onPressed: () {
    //             Navigator.pop(context);
    //           },
    //           style: ElevatedButton.styleFrom(
    //             backgroundColor: Colors.green,
    //           ),
    //           child: Text(EkubLocalization.of(context)!.translate("okay")),
    //         ),
    //       ],
    //     );
    //   },
    // );
  }

  // _showSuccessDialog method removed as we now navigate directly to success page

  // The _handlePaymentResult method has been replaced by navigateToSuccessPage and showJavaScriptErrorDialog

  /// Navigates to the success page
  /// This method is called from the JavaScript callback
  static void navigateToSuccessPage(Member member) {
    debugPrint("⚠️ JAVASCRIPT CALLED navigateToSuccessPage");

    // Use the global navigator key to navigate to the success page
    if (navigatorKey.currentContext != null) {
      Navigator.push(
        navigatorKey.currentContext!,
        MaterialPageRoute(
          builder: (context) => PaymentSuccessPage(member: member),
        ),
      );
      debugPrint("⚠️ NAVIGATED TO SUCCESS PAGE");
    } else {
      debugPrint("⚠️ FAILED TO NAVIGATE TO SUCCESS PAGE: NO VALID CONTEXT");
    }
  }

  /// Shows an error dialog
  /// This method is called from the JavaScript callback
  static void showJavaScriptErrorDialog(String message) {
    debugPrint("⚠️ JAVASCRIPT CALLED showJavaScriptErrorDialog: $message");

    // Use the global navigator key to show the error dialog
    if (navigatorKey.currentContext != null) {
      _showErrorDialog(navigatorKey.currentContext!, 'Payment Failed', message);
      debugPrint("⚠️ SHOWED ERROR DIALOG");
    } else {
      debugPrint("⚠️ FAILED TO SHOW ERROR DIALOG: NO VALID CONTEXT");
    }
  }

  /// Creates the initial payment object with default values
  static Map<String, dynamic> _createInitialPaymentObject(
      String title, String amount) {
    return {
      'functionName': 'js_fun_start_pay',
      'params': {
        'rawRequest':
            'SIMULATED_rawRequest', // Will be updated with actual value from backend
        'functionCallBackName': 'handleinitDataCallback',
      },
    };
  }

  /// Creates the initial payment object with default values
  static Map<String, dynamic> _createAutoLoginObject() {
    return {
      'functionName': 'js_fun_h5GetAccessToken',
      'params': {
        // 'appid': '1350921361971201',
        // 'appid': '1412693976883201',
        'appid': '1330403772262406',
        'functionCallBackName': 'handleAuthDataCallback',
      },
    };
  }

  /// Logs payment details for debugging
  static void _logPaymentDetails(String title, String amount, int? memberId) {
    final logMessage = """==== JAVASCRIPT PAYMENT PROCESSING ====
    Title: $title
    Amount: $amount
    Member ID: $memberId
    ======================================""";

    // Log to console and session
    Session().logSession("payment_details", logMessage);
  }

  /// Logs backend request details for debugging
  static void _logBackendRequestDetails(
      int? memberId, String equbId, String amount) {
    final logMessage = """==== MAKING BACKEND REQUEST ====
    Member ID: $memberId
    Equb ID: $equbId
    Amount: $amount
    ================================""";

    // Log to session
    Session().logSession("backend_request", logMessage);

    // For immediate visibility in debug console
    debugPrint(logMessage);
  }

  /// Logs backend response for debugging
  static void _logBackendResponse(dynamic responseData) {
    final logMessage = """==== BACKEND RESPONSE ====
    Response: $responseData
    ============================""";

    // Log to session
    Session().logSession("backend_response", responseData.toString());

    // For immediate visibility in debug console
    debugPrint(logMessage);
  }

  /// Makes the backend request to initialize payment
  static Future<http.Response> _makeBackendRequest(
      String equbId, String amount, int? memberId) async {
    try {
      // Log request details before making the request
      final requestUrl = '${RequestHeader.baseApp}/telebirr-miniapp/initialize';
      final requestBody = {
        "equb_id": equbId,
        "amount": amount,
        "member_id": memberId
      };

      debugPrint('⬆️ SENDING HTTP REQUEST:');
      debugPrint('URL: $requestUrl');
      debugPrint('Body: ${json.encode(requestBody)}');

      Session().logSession(
          "http_request_start",
          {"url": requestUrl, "method": "POST", "body": requestBody}
              .toString());

      // Make the actual HTTP request
      final response = await http
          .post(
            Uri.parse(requestUrl),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode(requestBody),
          )
          .timeout(timeout);

      // Log response details after receiving the response
      debugPrint('⬇️ RECEIVED HTTP RESPONSE:');
      debugPrint('Status code: ${response.statusCode}');
      debugPrint(
          'Response body: ${response.body.substring(0, min(100, response.body.length))}${response.body.length > 100 ? "..." : ""}');

      Session().logSession(
          "http_request_complete",
          {
            "status_code": response.statusCode,
            "body_length": response.body.length,
            "body_preview":
                response.body.substring(0, min(100, response.body.length))
          }.toString());

      return response;
    } catch (e) {
      // Log any errors that occur during the request
      debugPrint('❌ HTTP REQUEST ERROR: $e');
      Session().logError("http_request_error", e.toString());
      rethrow; // Rethrow the error to be handled by the caller
    }
  }

  /// Executes JavaScript code for payment processing on web platform
  ///
  /// This method handles the JavaScript execution for payment processing on web platforms.
  /// It first sets the payment object as a global JavaScript variable, then executes the main
  /// payment processing code that uses this variable.
  ///
  /// @param paymentObject The payment object containing all necessary payment details
  /// @return A Future that resolves to a String containing the result of the JavaScript execution
  static Future<String> _executeJavaScript(
      Map<String, dynamic> paymentObject) async {
    debugPrint("Payment processing: Preparing JavaScript execution");

    // Log payment details for debugging (without sensitive information)
    final params = paymentObject['params'] as Map<String, dynamic>?;
    if (params != null) {
      final amount = params['amount'];
      final title = params['title'];
      debugPrint("Payment details - Title: $title, Amount: $amount");

      // Log that we're using rawRequest but don't log its contents (could be sensitive)
      if (params.containsKey('rawRequest')) {
        debugPrint("Payment includes rawRequest data from backend");
      }
    }

    // First, store the payment object in a global JavaScript variable
    // This avoids having to inject it directly into the JavaScript code string
    try {
      final paymentObjJson = jsonEncode(paymentObject);
      debugPrint("Payment processing: Setting payment data in JavaScript");

      // Set the payment object as a global JavaScript variable
      js.context.callMethod('eval', [
        """
        // Store payment data in a global variable for the payment processor to use
        window._paymentObject = $paymentObjJson;
        console.log('Payment data ready for processing');
      """
      ]);

      debugPrint("Payment processing: Data successfully prepared");
    } catch (e) {
      // Log the error and return an error message
      debugPrint(
          "Payment processing error: Failed to prepare payment data: $e");
      return "Error: Failed to prepare payment data: $e";
    }

    // Now execute the main JavaScript code that uses the global payment object
    return await executeJavaScript("""
      (function() {
        // Initialize payment processing
        console.log('Initializing payment processing');

        // Check if payment object is available
        if (!window._paymentObject) {
          console.error('Payment object not found');
          return 'Error: Payment object not found';
        }

        let callBackCount = 0;
        // Define the callback function that will be called by the third-party payment system
        window.handleinitDataCallback = function(response) {
          if (callBackCount != 0) {
            return;
          }
          // Execute the function only once
          callBackCount++;
          try {
            // Pass the response to Dart's handlePaymentResult function
            if (typeof window.handlePaymentResult === 'function') {
              window.handlePaymentResult(response, "Payment message");
            } else {
              console.error('Dart handlePaymentResult function is not defined');
            }
            } catch (error) {
              console.error('Processing error:', error);
              return handlePaymentResult(false, 'Error processing payment: ' + error.message);
            }
        };

        // Check if window.consumerapp exists
        if (window.consumerapp) {
          console.log('window.consumerapp exists');

          if (typeof window.consumerapp.evaluate === 'function') {
            console.log('window.consumerapp.evaluate is a function');

            try {
              // Safely trim rawRequest if it exists
              if (window._paymentObject &&
                  window._paymentObject.params &&
                  typeof window._paymentObject.params.rawRequest === 'string') {
                console.log('Trimming rawRequest to remove whitespace');
                window._paymentObject.params.rawRequest = window._paymentObject.params.rawRequest.trim();
              } else {
                console.log('rawRequest not available or not a string - skipping trim');
              }

              // Use the payment object from the global variable
              const result = window.consumerapp.evaluate(JSON.stringify(window._paymentObject));
              console.log('window.consumerapp.evaluate result:', result);
            } catch (error) {
              console.error('Error calling window.consumerapp.evaluate:', error);
              return handlePaymentResult(false, 'Error calling payment service: ' + error.message);
            }
          } else {
            console.log('window.consumerapp.evaluate is NOT a function');
            return handlePaymentResult(false, 'Payment service is not properly configured');
          }
        }
        else {
          console.log('window.consumerapp does NOT exist, creating simulation');
          alert("[Testing] payment");

          // Create a payment service simulation for development/testing environments
          console.log('Creating payment service simulation');

          // Define the payment service interface
          window.consumerapp = {
            evaluate: function(paymentDataStr) {
              console.log('Payment service simulation activated');

              try {
                // Parse the payment data
                const paymentData = JSON.parse(paymentDataStr);

                // Log payment details for debugging
                console.log('Processing payment with amount:',
                  paymentData.params && paymentData.params.amount ? paymentData.params.amount : 'unknown');

                // Check if we should simulate a failure (for testing error handling)
                const simulateFailure = false; // Set to true to test error handling

                // Simulate the payment processing with a delay
                // In production, this would be replaced by the actual payment gateway
                setTimeout(function() {
                  if (simulateFailure) {
                    // Simulate a failed payment
                    console.log('Simulating payment failure');
                    window.handleinitDataCallback({
                      success: false,
                      result: 'failed',
                      message: 'Payment declined (simulation)',
                      error_code: 'SIMULATION_DECLINED',
                      timestamp: new Date().toISOString()
                    });
                  } else {
                    const testResponse = `
                      {
                        "result": "success",
                        "referenceData": {
                          "transactionCredentialCode": "ODAxODAwMDIwNjAxMDI5MTAyMDEzMTgxMjQwMDBBNDMONDRmMzEzMDUwNDEzMzQ5MzU2MzA00UI1Mw==",
                          "showBanner": "true",
                          "bannerResponse": "[{endTime:1764190800000,execute: https://deje.shop/,imgUrl:https://developerportal.ethiotelebirr.et:38443/customer/img/Money_Transfer.png,order:1,reportTag:,showSeconds:4,startTime:1732693066000},{endTime:1746011109000,execute: merchant://100000000036,imgUrl:https://developerportal.ethiotelebirr.et:38443/customer/img/eid/banner/en.png,order:1,reportTag:,showSeconds:4,startTime:1742814347000},{endTime:1765659600000,execute: https://deje.shop/,imgUrl:https://developerportal.ethiotelebirr.et:38443/customer/img/Money_Transfer.png,order:5,reportTag:,showSeconds:5,startTime:1734173353000}]",
                          "billShareUrl": "merchant://10000000009?orderId=016011074023151700001008&mmOrderId=CDO70PA43Z",
                          "tipUrl": "merchant://10000000006?orderId=016011074023151700001008",
                          "billShareDisplay": "Bill Share",
                          "tipDisplay": "Give Tip",
                          "resultCode": "1",
                          "prepayId": "01674b39121214bc82f95d386269e38f319008"
                        }
                      }
                    `;
                    // Simulate a successful payment
                    console.log('Simulating successful payment');
                    window.handleinitDataCallback(testResponse);
                  }
                }, 2000); // 2 second delay to simulate processing time

                return true; // Indicate that the payment process has started
              } catch (error) {
                console.error('Payment simulation error:', error);

                // Report the error through the callback
                window.handleinitDataCallback({
                  success: false,
                  status: 'error',
                  message: 'Payment processing error: ' + error.message,
                  error_code: 'SIMULATION_ERROR',
                  timestamp: new Date().toISOString()
                });

                return false; // Indicate that the payment process failed to start
              }
            }
          };

          try {
            // Start the payment process using the simulation
            console.log('Initiating simulated payment process');

            // Safely trim rawRequest if it exists (same as in the real payment flow)
            if (window._paymentObject &&
                window._paymentObject.params &&
                typeof window._paymentObject.params.rawRequest === 'string') {
              console.log('Simulation: Trimming rawRequest to remove whitespace');
              window._paymentObject.params.rawRequest = window._paymentObject.params.rawRequest.trim();
            }

            const result = window.consumerapp.evaluate(JSON.stringify(window._paymentObject));

            if (result) {
              console.log('Payment process initiated successfully');
            } else {
              console.error('Failed to initiate payment process');
              return handlePaymentResult(false, 'Failed to initiate payment process');
            }
          } catch (error) {
            console.error('Error starting payment process:', error);
            return handlePaymentResult(false, 'Error starting payment: ' + error.message);
          }
        }

        return 'JavaScript execution completed';
      })();
    """);
  }

  static void setupJavaScriptCallback(
    BuildContext context,
    int index,
    Member member,
    Equb? equb,
    String? role,
    double? remainingAmount,
  ) {
    js.context['handlePaymentResult'] =
        (dynamic res, String message, [dynamic data]) async {
      debugPrint("Response is already a Map: $res");
      const String url =
          'https://virtualekubdash.com/api/telebirr-miniapp/callback-miniapp';
      final Map<String, dynamic> body = {'data': res};

      try {
        final response = await _makeBackendCallbackRequest(
          url: url,
          body: body,
        );

        if (response.statusCode == 200) {
          debugPrint('Payment successful: ${response.body}');
        } else {
          debugPrint('Payment failed: ${response.body}');
        }
      } catch (e) {
        debugPrint('Error making payment request: $e');
      }

      try {
        // Parse the response if it's a JSON string
        if (res is String) {
          // Clip the JSON string to start from the desired key
          int startIndex = res.indexOf('"result":"success"');
          int startIndex2 = res.indexOf('"result": "success"');

          debugPrint("startIndex: $startIndex $startIndex2");
          if (startIndex != -1 || startIndex2 != -1) {
            // String clippedJson = res.substring(startIndex, endIndex);
            // final parsedResponse =
            //     jsonDecode(clippedJson) as Map<String, dynamic>;
            // debugPrint("Parsed Response: $parsedResponse");
            // Handle the parsed response
            handleParsedResponse(
                context.mounted ? context : navigatorKey.currentContext!,
                "success",
                data,
                index,
                member,
                equb,
                role,
                remainingAmount);
          }
        } else if (res is Map<String, dynamic>) {
          // If the response is already a Map, use it directly
          debugPrint("Response is already a Map: $res");
          handleParsedResponse(
              context.mounted ? context : navigatorKey.currentContext!,
              res['message'],
              data,
              index,
              member,
              equb,
              role,
              remainingAmount);
        } else {
          debugPrint("Invalid response type: $res");
        }
      } catch (e) {
        debugPrint("Error parsing response: $e");

        // Show the error in a pop-up dialog
        if (navigatorKey.currentContext != null) {
          showDialog(
            context: navigatorKey.currentContext!,
            builder: (context) => AlertDialog(
              title: const Text("Error"),
              content: Text("Error parsing response: $e"),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text("OK"),
                ),
              ],
            ),
          );
        } else {
          debugPrint("⚠️ Navigator context is null, cannot show error dialog.");
        }
      }
    };
  }

  static void handleParsedResponse(
      BuildContext context,
      // Map<String, dynamic> response,
      String message,
      dynamic data,
      int index,
      Member member,
      Equb? equb,
      String? role,
      double? remainingAmount) async {
    String? currentPage = ModalRoute.of(context)?.settings.name;
    debugPrint("Current Page: $currentPage");
    debugPrint("member: ${member.id}");
    debugPrint("equb: ${equb?.id}");

    // Check the result value in the response
    if (message == 'success') {
      // Show success message
      debugPrint("Payment was successful!");

      // Option 1: Navigate to the success page
      // if (navigatorKey.currentContext != null) {
      //   Navigator.push(
      //     navigatorKey.currentContext!,
      //     MaterialPageRoute(
      //       builder: (context) => PaymentSuccessPage(
      //         member: _currentMember!, // Pass the current member
      //       ),
      //     ),
      //   );
      // } else {
      //   debugPrint(
      //       "⚠️ Navigator context is null, cannot navigate to success page");
      // }

      // Ensure _currentMember is not null
      if (role == null) {
        debugPrint("⚠️ Role is null, cannot proceed.");
        return;
      }

      // Refresh the payments
      try {
        await Provider.of<MemberProvider>(context, listen: false).getPayments(
          context,
          member.id!.toString(),
          equb!.id.toString(),
          1, // Reset to first page
          10, // Your page size
          true, // Refresh
          false, // Not a load more
        );
      } catch (e) {
        // Log the error but don't let it crash the payment success flow
        debugPrint("Error refreshing payments: $e");
      }

      // Remove any popup first
      _removePopupDialog(context.mounted ? context : null);

      // Option 2: Refresh the current page
      // if (navigatorKey.currentContext != null) {
      //   _openHomeScreen(navigatorKey.currentContext!, role == "admin", role);
      // } else {
      //   debugPrint("⚠️ Navigator context is null, cannot refresh the page");
      // }
    } else {
      // Handle error case
      final errorMessage = message ?? "An unknown error occurred";
      debugPrint("Payment failed: $errorMessage");

      // Show error dialog
      if (navigatorKey.currentContext != null) {
        _showErrorDialog(
            navigatorKey.currentContext!, "Payment Failed", errorMessage);
      } else {
        debugPrint("⚠️ Navigator context is null, cannot show error dialog");
      }
    }
  }

  static void _openHomeScreen(BuildContext context, bool isAdmin, String role) {
    try {
      HomeScreenArgs argument = HomeScreenArgs(
        isOnline: true,
        isAdmin: isAdmin,
        role: role,
      );

      Navigator.pushNamedAndRemoveUntil(
        context,
        HomeScreen.routeName,
        (Route<dynamic> route) => false,
        arguments: argument,
      );
    } catch (e) {
      debugPrint("Error navigating to HomeScreen: $e");
    }
  }

  static Future<http.Response> _makeBackendCallbackRequest({
    required String url,
    required Map<String, dynamic> body,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    try {
      // Log the request details
      debugPrint('⬆️ Sending HTTP Request:');
      debugPrint('URL: $url');
      debugPrint('Body: ${jsonEncode(body)}');

      // Make the HTTP POST request
      final response = await http
          .post(
            Uri.parse(url),
            headers: await RequestHeader().authorisedHeader(),
            body: jsonEncode(body),
          )
          .timeout(timeout);

      // Log the response details
      debugPrint('⬇️ Received HTTP Response:');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Body: ${response.body}');

      return response;
    } catch (e) {
      // Log any errors that occur during the request
      debugPrint('❌ HTTP Request Error: $e');
      throw Exception('Failed to make backend request: $e');
    }
  }

  /// Simulates JavaScript execution on non-web platforms
  ///
  /// This method provides a simulation of the payment process for non-web platforms
  /// (mobile, desktop) where JavaScript execution is not available. It simulates
  /// the payment flow with appropriate delays and logging.
  ///
  /// @param paymentObject The payment object containing payment details
  static Future<void> _simulateJavaScriptExecution(
      Map<String, dynamic> paymentObject) async {
    // Extract payment details for logging (without sensitive information)
    final params = paymentObject['params'] as Map<String, dynamic>?;
    final amount = params?['amount'] ?? 'unknown';
    final title = params?['title'] ?? 'Payment';

    // Log the start of the payment simulation
    debugPrint("Payment simulation started for: $title ($amount)");
    Session().logSession(
        "payment_simulation",
        {
          "event": "simulation_started",
          "title": title,
          "amount": amount,
          "timestamp": DateTime.now().toIso8601String()
        }.toString());

    // Simulate the payment processing delay
    debugPrint("Processing payment...");
    await Future.delayed(const Duration(seconds: 2));

    // Log successful completion
    debugPrint("Payment simulation completed successfully");
    Session().logSession(
        "payment_simulation",
        {
          "event": "simulation_completed",
          "status": "success",
          "transaction_id": "SIM_${DateTime.now().millisecondsSinceEpoch}",
          "timestamp": DateTime.now().toIso8601String()
        }.toString());

    // Note: The actual UI updates (success dialog, navigation) are handled in the main executePayment method
  }
}
