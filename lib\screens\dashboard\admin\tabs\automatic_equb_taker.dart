// ignore_for_file: must_be_immutable

import 'dart:math';
import 'package:ekub/models/ekub.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/repository/equb_repos.dart';
import 'package:ekub/screens/dashboard/admin/tabs/member_takers.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../../../repository/ekub_localization.dart';
import '../../../../repository/user_repos.dart';

class AutomaticEqubTaker extends StatefulWidget {
  AutomaticEqubTaker(
      {super.key,
      required this.equb,
      required this.member,
      required this.index,
      required this.completed});
  Equb equb;
  Member member;
  int index;
  bool completed;

  @override
  State<AutomaticEqubTaker> createState() => _AutomaticEqubTakerState();
}

class _AutomaticEqubTakerState extends State<AutomaticEqubTaker>
    with SingleTickerProviderStateMixin {
  bool _isLotteryDate = false;
  String? winner;
  String? message;
  String? winnerId;
  int rewards = 0;
  final random = Random();
  late ThemeProvider themeProvider;

  List<Map<String, dynamic>> winners = [];
  String? loggedInMemberId;

  @override
  void initState() {
    super.initState();
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    prepareRequest(context);

    // Create an instance of AuthDataProvider
    final authDataProvider = AuthDataProvider(httpClient: http.Client());

    // Fetch and store the member_id
    authDataProvider.getUserMemberId().then((memberId) {
      setState(() {
        loggedInMemberId = memberId;
      });
      // Print the fetched memberId
      print("Fetched logged-in user's memberId: $memberId");
    }).catchError((error) {
      print("Error retrieving memberId: $error");
    });
  }

  bool _isWaiting = true;

  void prepareRequest(BuildContext context) {
    var sender = EqubDataProvider(httpClient: http.Client());
    var res =
        sender.loadEqubWinner(context, widget.equb.equbType!.id.toString());

    res.then((value) {
      if (value["code"] == 200) {
        setState(() {
          winners = List<Map<String, dynamic>>.from(value["data"]);
          _isWaiting = false;
        });

        // Print each winner's memberId
      } else {
        setState(() {
          _isWaiting = false;
        });
        PanaraInfoDialog.show(
          context,
          message: value["message"],
          buttonText: EkubLocalization.of(context)!.translate("okay"),
          onTapDismiss: () {
            Navigator.pop(context);
          },
          imagePath: errorDialogIcon,
          panaraDialogType: PanaraDialogType.error,
        );
      }
    }).onError((error, stackTrace) {
      // Handle error
      return null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      body: _isWaiting
          ? searchLoading()
          : SingleChildScrollView(
              child: Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 20),
                      child: winners.isNotEmpty
                          ? ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: winners.length,
                              itemBuilder: (context, index) {
                                final winner = winners[index];
                                final winnerId = winner['memberId'].toString();
                                final isCurrentUser =
                                    winnerId == loggedInMemberId;

                                // Print the memberId of each winner and the comparison result
                                print(
                                    "Winner memberId: $winnerId, Is Current User: $isCurrentUser");

                                return Card(
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  elevation: 4,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: ListTile(
                                    tileColor: isCurrentUser
                                        ? themeProvider.getLightColor
                                        : Colors.white,
                                    leading: CircleAvatar(
                                      backgroundColor:
                                          themeProvider.getLightColor,
                                      child: Text(
                                        winner['memberName'][0],
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ),
                                    ),
                                    title: Text(
                                      winner['memberName'],
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: isCurrentUser
                                            ? Colors.white
                                            : Colors.black,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            )
                          : Center(
                              child: Text(
                                EkubLocalization.of(context)!
                                    .translate("no_winners"),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  String formatDate(String date) {
    // Use the current app locale
    final locale = Localizations.localeOf(context).toString();
    return DateFormat('MMMM-d-yyyy', locale).format(DateTime.parse(date));
  }
}
