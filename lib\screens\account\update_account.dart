// ignore_for_file: must_be_immutable, empty_catches

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/account/camera_preview_screen.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/ui_kits/app_bar.dart';
import 'package:ekub/service/headers.dart';
import 'package:ekub/utils/colors.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';

import '../../models/user.dart';
import '../../repository/ekub_localization.dart';
import '../../utils/compress_image.dart';
import '../../utils/constants.dart';
import '../../utils/global_constants.dart';
import '../../utils/validator.dart';
import '../themes/ThemeProvider.dart';
//import '../account/camera_preview_screen.dart';

class UpdateAccount extends StatefulWidget {
  static const routeName = "/update_account";

  UpdateAccount({
    super.key,
    required UpdateAccountArgs args,
    required this.user,
  });
  Member user;
  @override
  State<UpdateAccount> createState() => _UpdateAccountState();
}

class _UpdateAccountState extends State<UpdateAccount> {
  bool _onProcess = false;
  bool isSubmitted = false;
  bool isOffline = false;
  String _selectedCity = "";
  String _selectedGender = "Male";

  File? _image;
  Uint8List? image;
  late User user;
  late ThemeProvider themeProvider;
  final _updateFormKey = GlobalKey<FormState>();
  final dropdownControl = TextEditingController();
  final _appBar = GlobalKey<FormState>();
  final _baseUrl = RequestHeader.baseApp;

  List<String> cities = []; // List to hold city names
  Map<String, List<String>> subCitiesMap =
      {}; // Map to hold sub-cities for each city

  // Update the city and sub-city variables

  String? _selectedSubCity;

  // Function to get image provider
  ImageProvider _getImageProvider(image) {
    try {
      if (image is Uint8List) {
        return MemoryImage(image);
      } else if (image is File) {
        return FileImage(image);
      }
    } catch (e) {
      // Log the error if needed
    }
    // Provide a placeholder image asset if no image is found
    return const AssetImage('assets/icons/user.jpg');
  }

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  @override
  void initState() {
    _selectedCity = widget.user.city ?? "";
    _selectedSubCity = widget.user.subCity ?? null;
    _onProcess = false;
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    dropdownControl.text = widget.user.subCity ?? "Gulelle";
    _selectedGender = (widget.user.gender ?? "Male")[0].toUpperCase() +
        (widget.user.gender ?? "Male").substring(1).toLowerCase();

    loadMmembers();
    fetchCities(); // Fetch cities on initialization
    super.initState();
  }

  void loadMmembers() async {
    try {
      Provider.of<MemberProvider>(context, listen: false)
          .loadMembers(context, 0, 1);
      await _fetchProfilePicture(); // Call the new method to fetch profile picture

      // Print the loaded member information
      print("Member Info:");
      print("Full Name: ${widget.user.fullName}");
      print("Phone: ${widget.user.phone}");
      print("Email: ${widget.user.email}");
      print("City: ${widget.user.city}");
      print("SubCity: ${widget.user.subCity}");
      print("Woreda: ${widget.user.woreda}");
      print("House Number: ${widget.user.houseNumber}");
      print("Location: ${widget.user.location}");
      print("Gender: ${widget.user.gender}");
    } catch (e) {
      if (e is TimeoutException) {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("time_out"),
              message:
                  EkubLocalization.of(context)!.translate("timeout_message"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            Navigator.pop(context);
            loadMmembers();
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("error"),
              message: EkubLocalization.of(context)!.translate("error_message"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            loadMmembers();
            Navigator.pop(context);
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  Future<void> _fetchProfilePicture() async {
    try {
      image = await Provider.of<MemberProvider>(context, listen: false)
          .getProfilePicture(context, widget.user.id.toString());
    } catch (e) {
      print("Error fetching profile picture: $e");
    }
  }

  // New method to fetch cities and sub-cities
  Future<void> fetchCities() async {
    try {
      http.Response response = await http
          .get(Uri.parse('$_baseUrl/city'),
              headers: await RequestHeader().authorisedHeader())
          .timeout(timeout);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          cities = data['data']
              .map<String>((city) => city['name'] as String)
              .toList(); // Extract city names

          for (var city in data['data']) {
            // Extract sub-city names
            List<String> subCityNames = city['sub_city']
                .map<String>((subCity) => subCity['name'] as String)
                .toList();

            subCitiesMap[city['name']] = subCityNames;
          }
        });
      } else {
        throw Exception('Failed to load cities');
      }
    } catch (e) {
      print("Error fetching cities: $e"); // Log the error
    }
  }

  _updateProfile() async {
    print("Entered _updateProfile method");
    var sender = Provider.of<MemberProvider>(context, listen: false);

    setState(() {
      _onProcess = true;
    });

    // Prepare the JSON data
    Map<String, dynamic> jsonData = {
      "full_name": widget.user.fullName ?? "",
      "phone": widget.user.phone ?? "",
      "gender": _selectedGender.toLowerCase(),
      "city": _selectedCity,
      "subcity": _selectedSubCity ?? "",
      "woreda": widget.user.woreda ?? "",
      "house_number": widget.user.houseNumber ?? "",
      "specific_location": widget.user.location ?? "",
      "email": widget.user.email ?? "",
    };

    print("Data to be sent: ${json.encode(jsonData)}");

    try {
      FormData formData = FormData.fromMap({
        ...jsonData,
        if (_image != null)
          "profile_picture": await MultipartFile.fromFile(
            _image!.path,
            filename: "profile_picture.jpg",
            contentType: MediaType("image", "jpeg"),
          ),
      });

      Dio dio = Dio();
      dio.options.headers = {
        "Authorization": "Bearer ${await sender.authDataProvider.getToken()}",
        "Content-Type": "multipart/form-data",
      };

      print("Sending request to update profile");
      Response response = await dio.post(
        '$_baseUrl/member/updateProfile/${widget.user.id}',
        data: formData,
      );

      if (response.statusCode == 200) {
        print("Update successful: ${response.data}");
        Navigator.pop(context);
        // Navigator.pop(context);
        _openHomeScreen(false, "member");

        // Show success SnackBar
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  EkubLocalization.of(context)!.translate("update_successful")),
              backgroundColor: Colors.green,
            ),
          );
        });

        Navigator.pushNamedAndRemoveUntil(
          context,
          HomeScreen.routeName,
          (Route<dynamic> route) => false,
        );
      } else {
        print("Update failed: ${response.data}");
      }
    } catch (error, stackTrace) {
      print("Error during update: $error");
      print("Stack trace: $stackTrace");
    } finally {
      setState(() {
        _onProcess = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    image = Provider.of<MemberProvider>(context, listen: true).image;

    return Scaffold(
        backgroundColor: ColorProvider.backgroundColor,
        appBar: EkubAppBar(
          key: _appBar,
          title: EkubLocalization.of(context)!.translate("edit_profile"),
          widgets: const [],
        ),
        body: Form(
            key: _updateFormKey,
            autovalidateMode: isSubmitted
                ? AutovalidateMode.onUserInteraction
                : AutovalidateMode.disabled,
            child: Stack(
              children: <Widget>[
                Align(
                  alignment: Alignment.bottomCenter,
                  child: ListView(
                    children: <Widget>[
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        margin: const EdgeInsets.fromLTRB(25, 10, 25, 10),
                        padding: const EdgeInsets.fromLTRB(20, 5, 20, 10),
                        child: Column(
                          children: <Widget>[
                            const SizedBox(
                              height: 5,
                            ),
                            Stack(
                              children: [
                                GestureDetector(
                                  onTap: _getSelfieImage,
                                  child: Container(
                                    height: 70,
                                    width: 70,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: Colors.grey.shade400),
                                      shape: BoxShape.circle,
                                      image: DecorationImage(
                                        image:
                                            _getImageProvider(_image ?? image),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                ),
                                Positioned(
                                  right: 0,
                                  bottom: 0,
                                  child: GestureDetector(
                                    onTap: _getSelfieImage,
                                    child: Container(
                                      decoration: BoxDecoration(
                                          color: ColorProvider.backgroundColor,
                                          border: Border.all(
                                              color: Colors.grey.shade400)),
                                      child: Icon(
                                        Icons.add_a_photo,
                                        size: 30,
                                        color: themeProvider.getColor,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 15,
                            ),
                            _nameField(),
                            const SizedBox(
                              height: 10,
                            ),
                            _phoneNumberField(),
                            const SizedBox(
                              height: 10,
                            ),
                            _emailField(),
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(18),
                              ),
                              child: Row(
                                children: [
                                  Radio<String>(
                                    activeColor: themeProvider.getColor,
                                    value: 'Male',
                                    groupValue: _selectedGender,
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedGender = value!;
                                      });
                                    },
                                  ),
                                  Text(
                                      EkubLocalization.of(context)!
                                          .translate("male"),
                                      style: hintStyle),
                                  Radio<String>(
                                    activeColor: themeProvider.getColor,
                                    value: 'Female',
                                    groupValue: _selectedGender,
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedGender = value!;
                                      });
                                    },
                                  ),
                                  Text(
                                    EkubLocalization.of(context)!
                                        .translate("female"),
                                    style: hintStyle,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            _cityDropdown(),
                            const SizedBox(height: 10),
                            _subCityDropdown(),
                            const SizedBox(
                              height: 10,
                            ),
                            _woredaField(),
                            const SizedBox(
                              height: 10,
                            ),
                            _houseNumberField(),
                            const SizedBox(
                              height: 10,
                            ),
                            _locationField(),
                            const SizedBox(
                              height: 10,
                            ),
                            ElevatedButton(
                              onPressed: _onProcess
                                  ? null
                                  : () async {
                                      print("Update button pressed");
                                      final form = _updateFormKey.currentState;
                                      setState(() {
                                        isSubmitted = true;
                                      });
                                      if (form!.validate()) {
                                        setState(() {
                                          _onProcess = true;
                                        });
                                        form.save();
                                        if (!await InternetConnectivity()
                                            .checkInternetConnectivty(
                                                context, true)) {
                                          setState(() {
                                            _onProcess = false;
                                          });
                                          return;
                                        } else {
                                          print("Calling _updateProfile");
                                          _updateProfile();
                                        }
                                      } else {
                                        print("Form validation failed");
                                      }
                                    },
                              child: Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: themeProvider.getColor),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Spacer(),
                                      Text(
                                          EkubLocalization.of(context)!
                                              .translate("update"),
                                          style: buttonText),
                                      const Spacer(),
                                      Align(
                                        widthFactor: 2,
                                        alignment: Alignment.centerRight,
                                        child: _onProcess
                                            ? const Padding(
                                                padding: EdgeInsets.all(8.0),
                                                child: SizedBox(
                                                  height: 20,
                                                  width: 20,
                                                  child:
                                                      CircularProgressIndicator(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              )
                                            : Container(),
                                      ),
                                    ],
                                  )),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            )));
  }

  TextFormField _emailField() {
    return TextFormField(
      initialValue: widget.user.email,
      style: lableStyle,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.email_outlined,
              size: 20,
            ),
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.circular(18),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("email"),
          labelStyle: hintStyle),
      validator: (value) => Sanitizer().isValidEmail(value!, context),
      onSaved: (value) {
        widget.user.email = value;
      },
    );
  }

  TextFormField _phoneNumberField() {
    return TextFormField(
      initialValue: widget.user.phone,
      style: lableStyle,
      keyboardType:
          const TextInputType.numberWithOptions(signed: true, decimal: true),
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.phone_outlined,
              size: 20,
            ),
          ),
          enabled: false,
          border: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(18),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelStyle: hintStyle),
      onSaved: (value) {},
    );
  }

  TextFormField _nameField() {
    return TextFormField(
      initialValue: widget.user.fullName,
      style: lableStyle,
      readOnly: true,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.person_2_outlined,
              size: 20,
            ),
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(18),
          ),
          enabled: false,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelStyle: hintStyle,
          hintStyle: hintStyle),
      validator: (value) => Sanitizer().isFullNameValid(value!, false, context),
      onSaved: (value) {
        widget.user.fullName = value;
      },
    );
  }

  TextFormField _locationField() {
    return TextFormField(
      style: lableStyle,
      initialValue: widget.user.location,
      decoration: InputDecoration(
          border: const OutlineInputBorder(
              borderSide: BorderSide(style: BorderStyle.solid)),
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.location_searching_sharp,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          // labelText:
          //     EkubLocalization.of(context)!.translate("specific_location"),
          labelStyle: hintStyle),
      onSaved: (value) {
        widget.user.location = value;
      },
      validator: (value) => Sanitizer().isValidField(
          value!,
          EkubLocalization.of(context)!.translate("specific_location"),
          context),
    );
  }

  TextFormField _houseNumberField() {
    return TextFormField(
      style: lableStyle,
      initialValue: widget.user.houseNumber,
      keyboardType: TextInputType.streetAddress,
      decoration: InputDecoration(
          border: const OutlineInputBorder(
              borderSide: BorderSide(style: BorderStyle.solid)),
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.house_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("house_number"),
          labelStyle: hintStyle),
      onSaved: (value) {
        widget.user.houseNumber = value;
      },
      validator: (value) => Sanitizer().checkLength(value!,
          EkubLocalization.of(context)!.translate("house_number"), context),
    );
  }

  TextFormField _woredaField() {
    return TextFormField(
      style: lableStyle,
      initialValue: widget.user.woreda,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
          border: const OutlineInputBorder(
              borderSide: BorderSide(style: BorderStyle.solid)),
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.location_city_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("woreda"),
          labelStyle: hintStyle),
      onSaved: (value) {
        widget.user.woreda = value;
      },
      validator: (value) => Sanitizer().checkLength(
          value!, EkubLocalization.of(context)!.translate("woreda"), context),
    );
  }

  // Update the city dropdown method
  Widget _cityDropdown() {
    return DropdownButtonFormField<String>(
      value: cities.contains(_selectedCity) ? _selectedCity : null,
      onChanged: (newCity) {
        setState(() {
          _selectedCity = newCity!;
          _selectedSubCity = null; // Reset sub-city when city changes
        });
      },
      items: cities.map((city) {
        return DropdownMenuItem(
          value: city,
          child: Text(city),
        );
      }).toList(),
      decoration: InputDecoration(
        prefixIcon: Container(
          margin: const EdgeInsets.symmetric(horizontal: 5),
          height: 25,
          width: 30,
          decoration: BoxDecoration(
            color: themeProvider.getColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.location_city_outlined,
            size: 20,
            color: themeProvider.getColor,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: BorderSide(color: Colors.grey.shade400),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: BorderSide(color: themeProvider.getColor),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: const BorderSide(
            color: Colors.red,
          ),
          borderRadius: BorderRadius.circular(18),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        fillColor: Colors.white,
        filled: true,
        labelText: EkubLocalization.of(context)!.translate("city"),
      ),
    );
  }

  // Update the sub-city dropdown method
  Widget _subCityDropdown() {
    return DropdownButtonFormField<String>(
      value: (subCitiesMap[_selectedCity]?.contains(_selectedSubCity) ?? false)
          ? _selectedSubCity
          : null,
      onChanged: (newSubCity) {
        setState(() {
          _selectedSubCity = newSubCity;
        });
      },
      items: subCitiesMap[_selectedCity]?.map((subCity) {
        return DropdownMenuItem(
          value: subCity,
          child: Text(subCity),
        );
      }).toList(),
      decoration: InputDecoration(
        prefixIcon: Container(
          margin: const EdgeInsets.symmetric(horizontal: 5),
          height: 25,
          width: 30,
          decoration: BoxDecoration(
            color: themeProvider.getColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.location_city_outlined,
            size: 20,
            color: themeProvider.getColor,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: BorderSide(color: Colors.grey.shade400),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: BorderSide(color: themeProvider.getColor),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: const BorderSide(
            color: Colors.red,
          ),
          borderRadius: BorderRadius.circular(18),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        fillColor: Colors.white,
        filled: true,
        labelText: EkubLocalization.of(context)!.translate("sub_city"),
      ),
      dropdownColor: Colors.white,
      isExpanded: true,
      menuMaxHeight: 200,
    );
  }

  Future<void> _getSelfieImage() async {
    print("Attempting to capture a selfie image");
    try {
      // Obtain a list of the available cameras on the device.
      final cameras = await availableCameras();

      // Get the front camera.
      final frontCamera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
      );

      // Initialize the camera controller.
      final cameraController = CameraController(
        frontCamera,
        ResolutionPreset.high,
      );

      // Initialize the controller.
      await cameraController.initialize();

      // Navigate to a new screen to show the camera preview.
      final XFile? imageFile = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              CameraPreviewScreen(cameraController: cameraController),
        ),
      );

      if (imageFile != null) {
        // Optionally, compress the image or perform other operations.
        final appDir = await getApplicationDocumentsDirectory();
        final targetPath = '${appDir.path}/profile-${DateTime.now()}.jpg';

        // Convert XFile to File before passing to compressAndGetFile
        File imageFileAsFile = File(imageFile.path);
        XFile compressedFile =
            await compressAndGetFile(imageFileAsFile, targetPath);
        setState(() {
          _image =
              File(compressedFile.path); // Convert XFile to File using path
        });
      }

      // Dispose of the camera controller when done.
      await cameraController.dispose();
    } catch (e) {
      // Handle any exceptions
      print("Error capturing selfie: $e");
      // Optionally, show a dialog or a snackbar to inform the user
    }
  }

  _openHomeScreen(bool isAdmin, String role) async {
    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);

    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }
}
