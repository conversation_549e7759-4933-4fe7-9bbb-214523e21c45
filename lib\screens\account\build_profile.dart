// ignore_for_file: must_be_immutable, unused_field, use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/account/privacy_screen.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/service/headers.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/network.dart';
import 'package:ekub/utils/validator.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../utils/global_constants.dart';

class BuildProfile extends StatefulWidget {
  static const routeName = "/build_profile";
  BuildProfileArgs args;
  BuildProfile({
    super.key,
    required this.args,
    required this.email,
    required this.gender,
    required this.name,
    required this.phoneNumber,
  });
  String name;
  String phoneNumber;
  String gender;
  String email;
  @override
  State<BuildProfile> createState() => _BuildProfileState();
}

class _BuildProfileState extends State<BuildProfile> {
  bool _onProcess = false;
  bool isSubmitted = false;
  bool isOffline = false;
  late String id;
  bool agree = false;
  String? _selectedCity;
  String? _selectedSubCity;

  late ThemeProvider themeProvider;
  final _updateFormKey = GlobalKey<FormState>();
  final woredaControl = TextEditingController();
  final subCityControl = TextEditingController();
  final houseControl = TextEditingController();
  final dropdownControl = TextEditingController();
  final locationControl = TextEditingController();
  late StreamSubscription _connectionChangeStream;
  final _baseUrl = RequestHeader.baseApp;
  List<String> cities = []; // List to hold city names
  Map<String, List<String>> subCitiesMap =
      {}; // Map to hold sub-cities for each city

  late Future<void> _citiesFuture;

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  @override
  void initState() {
    super.initState();
    _onProcess = false;
    NetworkManager connectionStatus = NetworkManager.getInstance();
    _connectionChangeStream =
        connectionStatus.connectionChange.listen(connectionChanged);
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    getId();
    _initializeCitiesFuture(); // Initialize the Future
  }

  void _initializeCitiesFuture() {
    _citiesFuture = fetchCities();
  }

  getId() async {
    var request = AuthDataProvider(httpClient: http.Client());

    id = (await request.getUserMemberId())!;
  }

  var proLoaded = false;
  File? _image;

  _buildProfile() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    var sender = MemberProvider(httpClient: http.Client());

    setState(() {
      _onProcess = true;
    });

    try {
      String gender = widget.gender.toLowerCase();

      print("Starting profile update for user: ${widget.name}");
      print("Gender value being sent: $gender");

      var res = await sender.updateMember(
        context,
        widget.name,
        widget.phoneNumber,
        widget.email,
        gender,
        id,
        woredaControl.text,
        _selectedCity ?? '',
        locationControl.text,
        houseControl.text,
        subCityControl.text,
        _image,
      );

      print("Response received: $res");

      if (res["code"] == 200) {
        print("Profile update successful: ${res["message"]}");
        prefs.setBool("ifFirstLogin", true);
        Navigator.pop(context);
        _openHomeScreen(false, "member");
      } else {
        print("Profile update failed: ${res["message"]}");
        PanaraInfoDialog.show(
          context,
          message: res["message"],
          buttonText: EkubLocalization.of(context)!.translate("okay"),
          imagePath: errorDialogIcon,
          onTapDismiss: () {
            Navigator.pop(context);
          },
          panaraDialogType: PanaraDialogType.error,
        );
      }
    } catch (error, stackTrace) {
      print("Error during profile update: $error");
      print("Stack trace: $stackTrace");

      PanaraConfirmDialog.show(context,
          message: EkubLocalization.of(context)!.translate("error_message"),
          confirmButtonText:
              EkubLocalization.of(context)!.translate("try_again"),
          cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
          onTapConfirm: () {
        Navigator.pop(context);
        _buildProfile();
      }, onTapCancel: () {
        Navigator.pop(context);
      }, panaraDialogType: PanaraDialogType.error);
    } finally {
      setState(() {
        _onProcess = false;
      });
    }
  }

  _openHomeScreen(bool isAdmin, String role) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool("ifFirstLogin", true);

    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);
    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }

  // New method to fetch cities and sub-cities
  Future<void> fetchCities() async {
    print("fetchCities called"); // Debug print
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/city'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(Duration(seconds: 10)); // Adjust timeout as needed

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          setState(() {
            cities = data['data']
                .map<String>((city) => city['name'] as String)
                .toList();
            for (var city in data['data']) {
              if (city['sub_city'] != null) {
                subCitiesMap[city['name']] = city['sub_city']
                    .map<String>((subCity) => subCity['name'] as String)
                    .toList();
              } else {
                subCitiesMap[city['name']] = [];
              }
            }
          });
          print("Cities loaded: $cities"); // Debug print
          print("Sub-cities map: $subCitiesMap"); // Debug print
        } else {
          print("Error: Unexpected response code ${data['code']}");
        }
      } else {
        print("Error: HTTP request failed with status ${response.statusCode}");
      }
    } catch (e) {
      print("Error fetching cities: $e");
    }
  }

  void onCityChanged(String? newCity) {
    print("City changed to: $newCity"); // Debug print
    setState(() {
      _selectedCity = newCity;
      _selectedSubCity = null; // Reset sub-city selection
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: IconThemeData(color: themeProvider.getColor),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      backgroundColor: ColorProvider.backgroundColor,
      body: FutureBuilder<void>(
        future: _citiesFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error loading cities'));
          } else {
            return SingleChildScrollView(
              child: Form(
                key: _updateFormKey,
                autovalidateMode: isSubmitted
                    ? AutovalidateMode.onUserInteraction
                    : AutovalidateMode.disabled,
                child: Stack(
                  children: <Widget>[
                    Column(
                      children: <Widget>[
                        Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              // border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(25)),
                          margin: const EdgeInsets.fromLTRB(25, 30, 25, 10),
                          padding: const EdgeInsets.fromLTRB(20, 40, 20, 40),
                          child: Column(
                            children: <Widget>[
                              Text(
                                EkubLocalization.of(context)!
                                    .translate("address_detail"),
                                style: const TextStyle(
                                  fontSize: fontBig,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(
                                height: 15,
                              ),
                              DropdownButtonFormField<String>(
                                value: _selectedCity,
                                onChanged: onCityChanged,
                                items: cities.map((city) {
                                  return DropdownMenuItem(
                                    value: city,
                                    child: Text(city),
                                  );
                                }).toList(),
                                decoration: InputDecoration(
                                  prefixIcon: Container(
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 5),
                                    height: 25,
                                    width: 30,
                                    decoration: BoxDecoration(
                                      color: themeProvider.getColor
                                          .withOpacity(0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.location_city_outlined,
                                      size: 20,
                                      color: themeProvider.getColor,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide:
                                        BorderSide(color: Colors.grey.shade400),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide: BorderSide(
                                        color: themeProvider.getColor),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                      color: Colors.red,
                                    ),
                                    borderRadius: BorderRadius.circular(18),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide: const BorderSide(
                                        color: Colors.red, width: 2),
                                  ),
                                  fillColor: Colors.white,
                                  filled: true,
                                  labelText: 'City',
                                ),
                              ),
                              const SizedBox(
                                height: 15,
                              ),
                              if (_selectedCity != null) _subCityDropdown(),
                              const SizedBox(
                                height: 15,
                              ),
                              _woredaField(),
                              const SizedBox(
                                height: 15,
                              ),
                              _houseNumberField(),
                              const SizedBox(
                                height: 15,
                              ),
                              _locationField(),
                              const SizedBox(
                                height: 15,
                              ),
                              Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Column(
                                      children: [
                                        Checkbox(
                                          checkColor: Colors.white,
                                          activeColor: themeProvider.getColor,
                                          value: agree,
                                          onChanged: (value) {
                                            setState(() {
                                              agree = value!;
                                            });
                                          },
                                        ),
                                        // const Text(""),
                                      ],
                                    ),
                                    Expanded(
                                      child: InkWell(
                                        onTap: () {
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      const PrivacyScreen()));
                                        },
                                        child: Text(
                                          EkubLocalization.of(context)!
                                              .translate(
                                                  "accept_terms_conditions"),
                                          style: const TextStyle(
                                              decoration:
                                                  TextDecoration.underline,
                                              overflow: TextOverflow.clip,
                                              color: Colors.blue,
                                              fontWeight: FontWeight.normal,
                                              fontSize: 15),
                                        ),
                                      ),
                                    ),
                                  ]),
                              const SizedBox(
                                height: 50,
                              ),
                              InkWell(
                                onTap: _onProcess || !agree
                                    ? null
                                    : () async {
                                        final form =
                                            _updateFormKey.currentState;
                                        setState(() {
                                          isSubmitted = true;
                                        });
                                        if (form!.validate()) {
                                          setState(() {
                                            _onProcess = true;
                                          });
                                          form.save();
                                          if (!await InternetConnectivity()
                                              .checkInternetConnectivty(
                                                  context, true)) {
                                            setState(() {
                                              _onProcess = false;
                                            });
                                            return;
                                          } else {
                                            _buildProfile();
                                          }
                                        }
                                      },
                                child: Container(
                                  width: 400,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: agree
                                          ? themeProvider.getColor
                                          : Colors.grey[400]),
                                  height: 50,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Spacer(),
                                      Text(
                                          EkubLocalization.of(context)!
                                              .translate("submit"),
                                          style: buttonText),
                                      const Spacer(),
                                      Align(
                                        widthFactor: 2,
                                        alignment: Alignment.centerRight,
                                        child: _onProcess
                                            ? const Padding(
                                                padding: EdgeInsets.all(8.0),
                                                child: SizedBox(
                                                  height: 20,
                                                  width: 20,
                                                  child:
                                                      CircularProgressIndicator(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              )
                                            : Container(),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }

  Widget _subCityDropdown() {
    return DropdownButtonFormField<String>(
      value: (subCitiesMap[_selectedCity]?.contains(_selectedSubCity) ?? false)
          ? _selectedSubCity
          : null,
      onChanged: (newSubCity) {
        setState(() {
          _selectedSubCity = newSubCity;
        });
      },
      items: subCitiesMap[_selectedCity]?.map((subCity) {
        return DropdownMenuItem(
          value: subCity,
          child: Text(subCity),
        );
      }).toList(),
      decoration: InputDecoration(
        prefixIcon: Container(
          margin: const EdgeInsets.symmetric(horizontal: 5),
          height: 25,
          width: 30,
          decoration: BoxDecoration(
            color: themeProvider.getColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.location_city_outlined,
            size: 20,
            color: themeProvider.getColor,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: BorderSide(color: Colors.grey.shade400),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: BorderSide(color: themeProvider.getColor),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: const BorderSide(
            color: Colors.red,
          ),
          borderRadius: BorderRadius.circular(18),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(18),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        fillColor: Colors.white,
        filled: true,
        labelText: EkubLocalization.of(context)!.translate("sub_city"),
      ),
      dropdownColor: Colors.white,
      isExpanded: true,
      menuMaxHeight: 200, // Set the maximum height for the dropdown menu
    );
  }

  TextFormField _locationField() {
    return TextFormField(
      style: lableStyle,
      controller: locationControl,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            height: 25,
            width: 30,
            decoration: BoxDecoration(
                color: themeProvider.getColor.withOpacity(0.1),
                shape: BoxShape.circle),
            child: Icon(
              Icons.location_searching_outlined,
              size: 20,
              color: themeProvider.getColor,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          hintText:
              EkubLocalization.of(context)!.translate("specific_location"),
          hintStyle: hintStyle),
      validator: (value) => Sanitizer().isValidField(
          value!,
          EkubLocalization.of(context)!.translate("specific_location"),
          context),
    );
  }

  TextFormField _houseNumberField() {
    return TextFormField(
      style: lableStyle,
      controller: houseControl,
      keyboardType: TextInputType.streetAddress,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            height: 25,
            width: 30,
            decoration: BoxDecoration(
                color: themeProvider.getColor.withOpacity(0.1),
                shape: BoxShape.circle),
            child: Icon(
              Icons.house_outlined,
              size: 20,
              color: themeProvider.getColor,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("house_number"),
          labelStyle: hintStyle),
      validator: (value) => Sanitizer().checkLength(value!,
          EkubLocalization.of(context)!.translate("house_number"), context),
    );
  }

  TextFormField _woredaField() {
    return TextFormField(
      style: lableStyle,
      controller: woredaControl,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
          border: const OutlineInputBorder(
              borderSide: BorderSide(style: BorderStyle.solid)),
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            height: 25,
            width: 30,
            decoration: BoxDecoration(
                color: themeProvider.getColor.withOpacity(0.1),
                shape: BoxShape.circle),
            child: Icon(
              Icons.location_city_outlined,
              size: 20,
              color: themeProvider.getColor,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("woreda"),
          labelStyle: hintStyle),
      validator: (value) => Sanitizer().checkLength(
          value!, EkubLocalization.of(context)!.translate("woreda"), context),
    );
  }
}
