// ignore_for_file: must_be_immutable

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/ui_kits/app_bar.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/global_constants.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../../../repository/member_repos.dart';
import '../../../../utils/constants.dart';
import '../../../../utils/validator.dart';
import '../../../themes/ThemeProvider.dart';

class AddMember extends StatefulWidget {
  static const routeName = "/add_member";
  AddMemberArgs args;
  AddMember({super.key, required this.args});

  @override
  State<AddMember> createState() => _AddMemberState();
}

class _AddMemberState extends State<AddMember> {
  bool invisible = false;
  bool _onProcess = false;
  bool isSubmitted = false;
  Member? member;
  late bool forEdit;
  String _selectedCity = "";
  String _selectedGender = "Male";
  final Map<String, dynamic> _member = {};

  late ThemeProvider themeProvider;
  final _registerFormKey = GlobalKey<FormState>();
  final userNameControl = TextEditingController();
  final emailControl = TextEditingController();
  final phoneControl = TextEditingController();
  final locationControl = TextEditingController();
  final woredaControl = TextEditingController();
  final subCityControl = TextEditingController();
  final houseControl = TextEditingController();
  final dropdownControl = TextEditingController();
  final _appBar = GlobalKey<FormState>();

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  @override
  void initState() {
    _onProcess = false;
    member = widget.args.member;
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: EkubAppBar(
        key: _appBar,
        title: EkubLocalization.of(context)!.translate("add_new_member"),
        widgets: const [],
      ),
      body: Form(
        key: _registerFormKey,
        autovalidateMode: isSubmitted
            ? AutovalidateMode.onUserInteraction
            : AutovalidateMode.disabled,
        child: Stack(
          children: <Widget>[
            // Opacity(
            //   opacity: 0.5,
            //   child: ClipPath(
            //     clipper: WaveClipper(),
            //     child: Container(
            //       height: 250,
            //       color: themeProvider.getColor,
            //     ),
            //   ),
            // ),
            // ClipPath(
            //   clipper: WaveClipper(),
            //   child: Container(
            //     height: 240,
            //     color: themeProvider.getColor,
            //   ),
            // ),
            Align(
              alignment: Alignment.center,
              child: ListView(
                children: <Widget>[
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        // border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(25)),
                    margin: const EdgeInsets.fromLTRB(25, 20, 25, 10),
                    padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                    child: Column(
                      children: <Widget>[
                        const SizedBox(
                          height: 10,
                        ),
                        _nameTextField(),
                        const SizedBox(
                          height: 10,
                        ),
                        _phoneNumberTextField(),
                        const SizedBox(
                          height: 10,
                        ),
                        _emailField(),
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(18),
                            border: Border.all(color: Colors.grey.shade400),
                          ),
                          child: Row(
                            children: [
                              Radio<String>(
                                activeColor: themeProvider.getColor,
                                value: 'Male',
                                groupValue: _selectedGender,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedGender = value!;
                                    _member["gender"] = value;
                                  });
                                },
                              ),
                              Text(
                                  EkubLocalization.of(context)!
                                      .translate("male"),
                                  style: hintStyle),
                              Radio<String>(
                                value: 'Female',
                                activeColor: themeProvider.getColor,
                                groupValue: _selectedGender,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedGender = value!;
                                    _member["gender"] = value;
                                  });
                                },
                              ),
                              Text(
                                EkubLocalization.of(context)!
                                    .translate("female"),
                                style: hintStyle,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        _cityDropdown(),
                        const SizedBox(
                          height: 10,
                        ),
                        _subCity(),
                        const SizedBox(
                          height: 10,
                        ),
                        _woredaField(),
                        const SizedBox(
                          height: 10,
                        ),
                        _houseNumberField(),
                        const SizedBox(
                          height: 10,
                        ),
                        _locationField(),
                        const SizedBox(
                          height: 25,
                        ),
                        ElevatedButton(
                          onPressed: _onProcess
                              ? null
                              : () async {
                                  final form = _registerFormKey.currentState;
                                  setState(() {
                                    isSubmitted = true;
                                  });
                                  if (form!.validate()) {
                                    form.save();
                                    if (!await InternetConnectivity()
                                        .checkInternetConnectivty(
                                            context, true)) {
                                      setState(() {
                                        _onProcess = false;
                                      });
                                      return;
                                    } else {
                                      setState(() {
                                        _onProcess = true;
                                      });
                                      _registerUser(
                                        userNameControl.text,
                                        phoneControl.text,
                                        emailControl.text == ""
                                            ? null
                                            : emailControl.text,
                                        _selectedGender,
                                        _selectedCity,
                                        locationControl.text,
                                        subCityControl.text == ""
                                            ? null
                                            : subCityControl.text,
                                        woredaControl.text == ""
                                            ? null
                                            : woredaControl.text,
                                        houseControl.text == ""
                                            ? null
                                            : houseControl.text,
                                      );
                                    }
                                  }
                                },
                          child: Container(
                            height: 50,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                color: themeProvider.getColor),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Spacer(),
                                Text(
                                    EkubLocalization.of(context)!
                                        .translate("register"),
                                    style: buttonText),
                                const Spacer(),
                                Align(
                                  widthFactor: 2,
                                  alignment: Alignment.centerRight,
                                  child: _onProcess
                                      ? const Padding(
                                          padding: EdgeInsets.all(8.0),
                                          child: SizedBox(
                                            height: 20,
                                            width: 20,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                            ),
                                          ),
                                        )
                                      : Container(),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  TextFormField _nameTextField() {
    return TextFormField(
      style: lableStyle,
      controller: userNameControl,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.person_2_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("full_name"),
          labelStyle: hintStyle),
      validator: (value) => Sanitizer().isFullNameValid(value!, true, context),
      onSaved: (value) {
        _member['full_name'] = value;
      },
    );
  }

  TextFormField _phoneNumberTextField() {
    return TextFormField(
      style: lableStyle,
      controller: phoneControl,
      maxLength: 9,
      keyboardType: TextInputType.phone,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.phone_outlined,
              size: 20,
            ),
          ),
          prefix: const Text("+251"),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          counterText: "",
          labelText: EkubLocalization.of(context)!.translate("phone_number"),
          labelStyle: hintStyle),
      validator: (value) => Sanitizer().isPhoneValid(value!, true, context),
      onSaved: (value) {
        _member['phone'] = '+251$value';
      },
    );
  }

  TextFormField _emailField() {
    return TextFormField(
      style: lableStyle,
      keyboardType: TextInputType.emailAddress,
      controller: emailControl,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.email_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("email"),
          labelStyle: hintStyle),
      validator: (value) => Sanitizer().isValidEmail(value!, context),
      onSaved: (value) {
        _member["email"] = value;
      },
    );
  }

  TextFormField _locationField() {
    return TextFormField(
      style: lableStyle,
      controller: locationControl,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.location_searching_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText:
              EkubLocalization.of(context)!.translate("specific_location"),
          labelStyle: hintStyle),
      validator: (value) => Sanitizer().isValidField(
          value!,
          EkubLocalization.of(context)!.translate("specific_location"),
          context),
      onSaved: (value) {
        _member["location"] = value;
      },
    );
  }

  TextFormField _woredaField() {
    return TextFormField(
      style: lableStyle,
      controller: woredaControl,
      decoration: InputDecoration(
          border: const OutlineInputBorder(
              borderSide: BorderSide(style: BorderStyle.solid)),
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.location_city_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("woreda"),
          labelStyle: hintStyle),
      // validator: (value) => Sanitizer().isValidField(value!, "Woreda"),
      onSaved: (value) {
        _member['woreda'] = value;
      },
    );
  }

  TextFormField _houseNumberField() {
    return TextFormField(
      style: lableStyle,
      controller: houseControl,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.house_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          labelText: EkubLocalization.of(context)!.translate("house_number"),
          labelStyle: hintStyle),
      validator: (value) => Sanitizer().checkLength(value!,
          EkubLocalization.of(context)!.translate("house_number"), context),
      onSaved: (value) {
        _member['housenumber'] = value;
      },
    );
  }

  _subCity() {
    return

        // CustomDropdown<String>.search(
        //   initialItem: widget.user.subCity,
        //   onChanged: (value) {
        //     setState(() {
        //       widget.user.subCity = value;
        //     });
        //   },
        //   hintText: EkubLocalization.of(context)!.translate("sub_city"),
        //   decoration: CustomDropdownDecoration(
        //     closedBorder: Border.all(color: Colors.grey.shade400),
        //     closedFillColor: Colors.white,
        //     hintStyle: hintStyle,
        //     closedSuffixIcon: Container(
        //       padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 0),
        //       decoration: BoxDecoration(
        //           color: ColorProvider.backgroundColor, shape: BoxShape.circle),
        //       child: Icon(
        //         Icons.keyboard_arrow_down,
        //         size: 25,
        //         color: themeProvider.getColor,
        //       ),
        //     ),
        //   ),

        //   items: subCities,
        //   // errorText:
        //   //     '${EkubLocalization.of(context)!.translate("valid_field")} ${EkubLocalization.of(context)!.translate("sub_city")}',
        // );

        TextFormField(
      style: lableStyle,
      decoration: InputDecoration(
          border: const OutlineInputBorder(
              borderSide: BorderSide(style: BorderStyle.solid)),
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.signpost_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          hintText: EkubLocalization.of(context)!.translate("sub_city"),
          hintStyle: hintStyle),
      onSaved: (value) {
        subCityControl.text = value!;
      },
    );
  }

  _cityDropdown() {
    return CustomDropdown<String>.search(
      onChanged: (value) {
        setState(() {
          _selectedCity = value!;
        });
      },
      validator: (p0) {
        if (_selectedCity == "") {
          return EkubLocalization.of(context)!.translate("select_city_error");
        }
        return null;
      },
      hintText: EkubLocalization.of(context)!.translate("city"),
      decoration: CustomDropdownDecoration(
        closedBorder: Border.all(color: Colors.grey.shade400),
        closedFillColor: Colors.white,
        hintStyle: hintStyle,
        closedSuffixIcon: Container(
          padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 0),
          decoration: BoxDecoration(
              color: ColorProvider.backgroundColor, shape: BoxShape.circle),
          child: Icon(
            Icons.keyboard_arrow_down,
            size: 25,
            color: themeProvider.getColor,
          ),
        ),
      ),
      items: cities,
    );
  }

  _registerUser(String userName, String phone, var email, String gender,
      String city, String location, var subCity, var woreda, var houseNumber) {
    var sender = MemberProvider(httpClient: http.Client());
    var res = sender.addMember(context, userName, phone, email, gender, city,
        location, subCity, woreda, houseNumber);
    res
        .then((value) => {
              setState(() {
                _onProcess = false;
              }),
              if (value["code"] == 200)
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("success"),
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                      Navigator.pop(context);
                      // _openHomeScreen(true, "admin");
                    },
                    imagePath: successDialogIcon,
                    panaraDialogType: PanaraDialogType.success,
                  ),
                  setState(() {
                    _onProcess = false;
                  })
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("error"),
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                  })
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          _registerUser(userName, phone, email, gender, city, location, subCity,
              woreda, houseNumber);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        _onProcess = false;
      });
      return {};
    });
  }
}
