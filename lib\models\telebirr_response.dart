class TelebirrResponse {
  final String appid;
  final String merchCode;
  final String prepayId;
  final String timestamp;

  TelebirrResponse({
    required this.appid,
    required this.merchCode,
    required this.prepayId,
    required this.timestamp,
  });

  // Factory constructor for creating a new instance from a map
  factory TelebirrResponse.fromJson(Map<String, dynamic> json) {
    return TelebirrResponse(
      appid: json['appid'] ?? "",
      merchCode: json['merch_code'] ?? "",
      prepayId: json['prepay_id'] ?? "",
      timestamp: json['timestamp'] ?? "",
    );
  }

  // Method to convert instance to JSON format
  Map<String, dynamic> toJson() {
    return {
      'appid': appid,
      'merch_code': merchCode,
      'prepay_id': prepayId,
      'timestamp': timestamp,
    };
  }
}
