import 'package:flutter/material.dart';

class Constants {
  int code = 0;
  static const int requestSuccess = 200;
  static const int anAuthorizedC = 401;
  static const String anAuthorizedM = "Unauthorized Access";
  static const int accessForbiddenC = 403;
  static const String accessForbiddenM = "Access forbidden";
  static const int notFoundC = 404;
  static const String notFoundM = "Not Found";
  static const int methodExceptionC = 405;
  static const String methodExceptionM = "Method Not Allowed";
  static const int serverErrorC = 500;
  static const String serverErrorM = "Server ErrorX";
  static const int unknownErrorC = 00;
  static const String unknownErrorM = "Server Error";
  static const int requestTimeoutC = 408;
  static const String requestTimeoutM = "Unable To Confirm Payment: Timeout";
}

const timeout = Duration(seconds: 60);
const defaultPadding = 12.0;
const primaryColor = Color(0xFF2697FF);
const secondaryColor = Color(0xFF2A2D3E);
const bgColor = Color(0xFF212332);
var bodyTextColor = Colors.grey.shade500;
var whiteText = Colors.white;
const normalFontWeight = FontWeight.normal;
const boldFont = FontWeight.bold;
const fontBig = 18.0;
const fontMedium = 14.0;
const fontSmall = 12.0;
var hintStyle = TextStyle(color: bodyTextColor, fontSize: fontMedium);
var lableStyle = const TextStyle(color: Colors.black, fontSize: fontMedium);
var buttonText =
    TextStyle(color: whiteText, fontSize: fontBig, fontWeight: boldFont);

String phoneNumberContact = '8171';
String websiteLink = 'https://virtualequb.com/';
String facebookContact =
    'https://web.facebook.com/profile.php?id=61568622366936&mibextid=ZbWKwL&_rdc=1&_rdr';
String linkedinContact = 'https://www.linkedin.com/company/virtual-equb';
String telegramContact = 'https://t.me/virtualequb10';
String instagramContact = 'https://www.instagram.com/virtualequb';
String tiktokContact = 'https://www.tiktok.com/@virtualequb2';
String twitterContact = 'https://x.com/virtualequb';
String emailContact = '<EMAIL>';
String privacyPolicyAndTerms = 'https://virtualequb.com/privacyPolicy';
