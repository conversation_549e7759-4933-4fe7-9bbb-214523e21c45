import 'package:ekub/exports/models.dart';

import '../repository/user_repos.dart';

class HomeScreenArgs {
  bool isOnline;
  bool isAdmin;
  String role;

  HomeScreenArgs(
      {required this.isOnline, required this.isAdmin, required this.role});
}

class ChangePasswordArgs {
  bool isOnline;
  String? role;
  bool fromDrawer;

  ChangePasswordArgs(
      {required this.isOnline, required this.role, required this.fromDrawer});
}

class ProfilePageArgs {
  bool isOnline;

  ProfilePageArgs({required this.isOnline});
}

class ConfirmCodeArgs {
  bool isSelected = false;
  bool isOnline;
  String? encodedPts;

  ConfirmCodeArgs({required this.isOnline});
}

class ForgetPasswordArgs {
  bool isSelected = false;
  bool isOnline;
  String? encodedPts;

  ForgetPasswordArgs({required this.isOnline});
}

class LoginScreenArgs {
  bool isSelected = false;
  bool isOnline;
  String? encodedPts;

  LoginScreenArgs({required this.isOnline});
}

class RegisterScreenArgs {
  bool isSelected = false;
  bool isOnline;
  String phoneNumber;
  String? encodedPts;

  RegisterScreenArgs({required this.isOnline, required this.phoneNumber});
}

class AutoRegisterScreenArgs {
  bool isSelected = false;
  bool isOnline;
  String phoneNumber;
  String name;
  String? encodedPts;

  AutoRegisterScreenArgs(
      {required this.isOnline, required this.phoneNumber, required this.name});
}

class ResetPasswordArgs {
  bool isSelected = false;
  bool isOnline;
  String? encodedPts;
  String userId = "";
  String otp;

  ResetPasswordArgs({required this.isOnline, required this.otp});
}

class UpdateAccountArgs {
  bool isSelected = false;
  bool isOnline;
  Member user;
  String? encodedPts;

  UpdateAccountArgs({required this.isOnline, required this.user});
}

class EditMemberArgs {
  bool isSelected = false;
  bool forEdit;
  Member member;
  String? encodedPts;

  EditMemberArgs({required this.forEdit, required this.member});
}

class FurnitureArgs {
  bool isSelected = false;
  bool isOnline;
  String? encodedPts;

  FurnitureArgs({required this.isOnline});
}

class MemberEqubsArgs {
  bool isSelected = false;
  bool isOnline;
  Member member;
  String? encodedPts;
  bool? isAdmin;
  // String role;

  MemberEqubsArgs({
    required this.isOnline,
    required this.member,
    this.isAdmin = false,
    // required this.role
  });
}

class MemberETabsArgs {
  bool isSelected = false;
  bool isOnline;
  bool completed;
  Equb? equb;
  int index;
  Member member;
  String? encodedPts;
  String? role;
  String? memberId;
  double? remainingAmount;

  MemberETabsArgs({
    required this.isOnline,
    required this.completed,
    required this.index,
    required this.equb,
    this.memberId,
    required this.member,
    required this.role,
    this.remainingAmount,
  });
}

class AddEqubArgs {
  bool isOnline;
  Member member;
  User? user;
  bool isAdmin;

  AddEqubArgs(
      {required this.isOnline,
      required this.member,
      required this.isAdmin,
      required this.user});
}

class AddMemberArgs {
  bool forEdit;
  Member? member;

  AddMemberArgs({required this.forEdit, this.member});
}

class AddEqubTypeArgs {
  bool forEdit;
  EqubType? equbType;
  AddEqubTypeArgs({required this.forEdit, this.equbType});
}

class AddPaymentArgs {
  bool forEdit;
  Member? member;
  Equb? equb;
  String role;

  AddPaymentArgs(
      {required this.role, required this.forEdit, this.member, this.equb});
}

class UserInfoArgs {
  String message;
  String button;
  bool logout;
  AuthDataProvider? auth;

  UserInfoArgs(
      {required this.message,
      required this.button,
      required this.logout,
      this.auth});
}

class EditCollectorArgs {
  bool isSelected = false;
  bool isOnline;
  String? encodedPts;

  EditCollectorArgs({required this.isOnline});
}

class BuildProfileArgs {
  bool isOnline;
  bool isAdmin;
  String role;

  BuildProfileArgs(
      {required this.isAdmin, required this.isOnline, required this.role});
}

class NtfsDetailArgs {
  bool isSelected = false;
  bool isOnline;
  String? encodedPts;

  NtfsDetailArgs({required this.isOnline});
}

class LikeOrNotArgs {
  bool isSelected = false;
  bool isOnline;
  String? encodedPts;

  LikeOrNotArgs({required this.isOnline});
}
