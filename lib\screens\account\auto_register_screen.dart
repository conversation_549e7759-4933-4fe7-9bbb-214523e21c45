// ignore_for_file: unused_field, must_be_immutable

import 'dart:async';
import 'dart:convert';

import 'package:ekub/exports/screens.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:ekub/service/headers.dart';
import 'dart:html' as html; // Web-only import
import 'dart:ui_web' as ui; // Flutter UI library for web

import '../../utils/network.dart';
import '../../utils/validator.dart';
import '../themes/ThemeProvider.dart';

class AutoRegisterScreen extends StatefulWidget {
  static const routeName = "/auto-register";

  AutoRegisterScreen({super.key, required this.args});
  AutoRegisterScreenArgs args;

  @override
  State<AutoRegisterScreen> createState() => _AutoRegisterScreenState();
}

class _AutoRegisterScreenState extends State<AutoRegisterScreen> {
  bool isSubmitted = false;
  bool isOffline = false;
  var invisible = false;
  bool _onProcess = false;
  String _selectedGender = "Male";
  bool agree = false;
  final _baseUrl = RequestHeader.baseApp;

  late String id;
  final Map<String, dynamic> _doctor = {};
  late ThemeProvider themeProvider;
  late StreamSubscription _connectionChangeStream;
  final _registerFormKey = GlobalKey<FormState>();
  final userNameControl = TextEditingController();
  final emailControl = TextEditingController();
  final woredaControl = TextEditingController();
  final houseControl = TextEditingController();
  final locationControl = TextEditingController();
  final passwordControl = TextEditingController();
  final password2Control = TextEditingController();
  final phoneControl = TextEditingController();
  bool _isAbove18 = false;
  final TextEditingController _ageController = TextEditingController();
  bool _isAgeFieldActive = false;

  DateTime? _selectedDateOfBirth;

  // Address
  String? _selectedCity;
  String? _selectedSubCity;
  final _updateFormKey = GlobalKey<FormState>();
  final subCityControl = TextEditingController();
  final dropdownControl = TextEditingController();
  List<String> cities = []; // List to hold city names
  Map<String, List<String>> subCitiesMap =
      {}; // Map to hold sub-cities for each city

  late Future<void> _citiesFuture;

  // Function to show the date picker and select date of birth
  void _selectDateOfBirth(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDateOfBirth ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(
              primary: themeProvider.getColor, // Header background color
              onPrimary: Colors.white, // Header text color
              onSurface: Colors.black, // Calendar text color
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: themeProvider.getColor, // Button text color
              ),
            ),
          ),
          child: child!,
        );
      },
      selectableDayPredicate: (DateTime date) {
        // Highlight the selected date
        if (_selectedDateOfBirth != null &&
            date.year == _selectedDateOfBirth!.year &&
            date.month == _selectedDateOfBirth!.month &&
            date.day == _selectedDateOfBirth!.day) {
          return true;
        }
        return true; // All other dates are selectable
      },
    );

    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
        _ageController.text =
            "${_selectedDateOfBirth!.day}/${_selectedDateOfBirth!.month}/${_selectedDateOfBirth!.year}";
        _isAbove18 = _calculateAge(_selectedDateOfBirth!) >=
            18; // Check if the user is above 18
      });
    }
  }

  // Helper function to calculate age based on date of birth
  int _calculateAge(DateTime birthDate) {
    DateTime currentDate = DateTime.now();
    int age = currentDate.year - birthDate.year;
    int month1 = currentDate.month;
    int month2 = birthDate.month;
    if (month2 > month1 ||
        (month1 == month2 && currentDate.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  @override
  void initState() {
    _onProcess = false;
    NetworkManager connectionStatus = NetworkManager.getInstance();
    _connectionChangeStream =
        connectionStatus.connectionChange.listen(connectionChanged);
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _ageController.addListener(_onAgeFieldChanged);
    // getId();
    _initializeCitiesFuture(); // Initialize the Future
    initTermsIframe();
    super.initState();
  }

  void initTermsIframe() {
    ui.platformViewRegistry.registerViewFactory(
      'terms-iframe', // Unique identifier
      (int viewId) => html.IFrameElement()
        ..src =
            'https://virtualekubdash.com/terms-and-conditions' // Your URL here
        ..style.border = 'none'
        ..style.width = '100%'
        ..style.height = '100%'
        ..allowFullscreen = true,
    );
  }

  void _initializeCitiesFuture() {
    _citiesFuture = fetchCities();
  }

  getId() async {
    var request = AuthDataProvider(httpClient: http.Client());

    id = (await request.getUserMemberId())!;
  }

  void _onAgeFieldChanged() {
    setState(() {
      _isAgeFieldActive = _ageController.text.isNotEmpty;
    });
  }

  // New method to fetch cities and sub-cities
  Future<void> fetchCities() async {
    // print("fetchCities called"); // Debug print
    try {
      final response = await http
          .get(Uri.parse('$_baseUrl/city'))
          .timeout(Duration(seconds: 10)); // Adjust timeout as needed
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          setState(() {
            // Use a Set to remove duplicates
            cities = data['data']
                .map<String>((city) => city['name'] as String)
                .where((cityName) =>
                    cityName != null &&
                    cityName.isNotEmpty) // Filter out empty values
                .toSet()
                .toList();
            for (var city in data['data']) {
              if (city['sub_city'] != null) {
                subCitiesMap[city['name']] = city['sub_city']
                    .map<String>((subCity) => subCity['name'] as String)
                    .toList();
              } else {
                subCitiesMap[city['name']] = [];
              }
            }
          });
        } else {
          print("Error: Unexpected response code ${data['code']}");
        }
      } else {
        print("Error: HTTP request failed with status ${response.statusCode}");
      }
    } catch (e) {
      print("Error fetching cities: $e");
    }
  }

  @override
  void dispose() {
    _ageController.removeListener(_onAgeFieldChanged);
    _ageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorProvider.backgroundColor,
        appBar: AppBar(
          iconTheme: IconThemeData(color: themeProvider.getColor),
          backgroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
        ),
        body: Form(
            key: _registerFormKey,
            autovalidateMode: isSubmitted
                ? AutovalidateMode.onUserInteraction
                : AutovalidateMode.disabled,
            child: Stack(
              children: <Widget>[
                Align(
                  alignment: Alignment.center,
                  child: ListView(
                    children: <Widget>[
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(25)),
                        margin: const EdgeInsets.fromLTRB(25, 30, 25, 10),
                        padding: const EdgeInsets.fromLTRB(20, 40, 20, 40),
                        child: Column(
                          children: <Widget>[
                            const SizedBox(
                              height: 15,
                            ),
                            Text(
                              EkubLocalization.of(context)!
                                  .translate("registration"),
                              style: const TextStyle(
                                fontSize: 22.0,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(
                              height: 25,
                            ),
                            _nameTextField(),
                            const SizedBox(
                              height: 15,
                            ),
                            _emailField(),
                            const SizedBox(
                              height: 15,
                            ),
                            _genderField(),
                            const SizedBox(
                              height: 15,
                            ),
                            _ageField(),
                            const SizedBox(
                              height: 15,
                            ),
                            _cityField(),
                            if (_selectedCity != null &&
                                subCitiesMap[_selectedCity] != null &&
                                subCitiesMap[_selectedCity]!.isNotEmpty) ...[
                              const SizedBox(
                                height: 15,
                              ),
                              _subCityDropdown(),
                            ],
                            const SizedBox(
                              height: 15,
                            ),
                            _woredaField(),
                            const SizedBox(
                              height: 15,
                            ),
                            _houseNumberField(),
                            const SizedBox(
                              height: 15,
                            ),
                            _locationField(),
                            const SizedBox(
                              height: 15,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Column(
                                  children: [
                                    Checkbox(
                                      checkColor: Colors.white,
                                      activeColor: themeProvider.getColor,
                                      value: agree,
                                      onChanged: (value) {
                                        setState(() {
                                          agree = value!;
                                        });
                                      },
                                    ),
                                  ],
                                ),
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      _loadTermsConditions();
                                    },
                                    child: Text(
                                      EkubLocalization.of(context)!
                                          .translate("accept_terms_conditions"),
                                      // style: const TextStyle(
                                      //   overflow: TextOverflow.clip,
                                      //   color: Colors.blue,
                                      //   fontWeight: FontWeight.normal,
                                      //   fontSize: 15,
                                      // ),
                                      style: TextStyle(
                                          overflow: TextOverflow.clip,
                                          color: themeProvider.getColor,
                                          fontWeight: FontWeight.bold,
                                          fontSize: fontMedium),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 40,
                            ),
                            ElevatedButton(
                              onPressed: _onProcess
                                  ? null
                                  : () async {
                                      final form =
                                          _registerFormKey.currentState;
                                      setState(() {
                                        isSubmitted = true;
                                      });
                                      if (form!.validate()) {
                                        if (!agree) {
                                          _showWarningDialog(
                                            EkubLocalization.of(context)!
                                                .translate("warning"),
                                            "You must accept the terms and conditions to continue.",
                                          );
                                          return;
                                        }
                                        setState(() {
                                          _onProcess = true;
                                        });
                                        form.save();
                                        if (!await InternetConnectivity()
                                            .checkInternetConnectivty(
                                                context, true)) {
                                          setState(() {
                                            _onProcess = false;
                                          });
                                          return;
                                        } else {
                                          _registerNewMember(
                                            widget.args.name,
                                            emailControl.text,
                                            _selectedGender,
                                            widget.args.phoneNumber,
                                            _ageController
                                                .text, // This now contains the date of birth
                                            _selectedCity,
                                            woredaControl.text,
                                            houseControl.text,
                                            locationControl.text,
                                          );
                                        }
                                      }
                                    },
                              child: SizedBox(
                                height: 50,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Spacer(),
                                    Text(
                                        EkubLocalization.of(context)!
                                            .translate("join_ekub"),
                                        style: buttonText),
                                    const Spacer(),
                                    Align(
                                      widthFactor: 2,
                                      alignment: Alignment.centerRight,
                                      child: _onProcess
                                          ? const Padding(
                                              padding: EdgeInsets.all(8.0),
                                              child: SizedBox(
                                                height: 20,
                                                width: 20,
                                                child:
                                                    CircularProgressIndicator(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            )
                                          : Container(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            )));
  }

  void onCityChanged(String? newCity) {
    setState(() {
      _selectedCity = newCity;
      _selectedSubCity = null; // Reset sub-city selection
    });
  }

  void _loadTermsConditions() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        insetPadding: const EdgeInsets.only(
          top: 10.0, // Keeps top padding
          bottom: 10.0, // Keeps bottom padding
          left: 10.0, // Removes left padding
          right: 10.0, // Removes right padding
        ),
        contentPadding: const EdgeInsets.only(
          top: 15.0, // Keeps top padding
          bottom: 15.0, // Keeps bottom padding
          left: 0, // Removes left padding
          right: 0, // Removes right padding
        ),
        title: const Text('Terms & Conditions'),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.99,
          height: MediaQuery.of(context).size.height * 0.8,
          child: const HtmlElementView(
            viewType: 'terms-iframe',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: TextStyle(
              color: themeProvider.getColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close the dialog
              },
              child: Text(
                EkubLocalization.of(context)!.translate("close"),
                style: TextStyle(color: themeProvider.getColor),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showWarningDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: TextStyle(
              color: themeProvider.getColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close the dialog
              },
              child: Text(
                EkubLocalization.of(context)!.translate("okay"),
                style: TextStyle(color: themeProvider.getColor),
              ),
            ),
          ],
        );
      },
    );
  }

  Column _subCityDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("sub_city"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        DropdownButtonFormField<String>(
          value:
              (subCitiesMap[_selectedCity]?.contains(_selectedSubCity) ?? false)
                  ? _selectedSubCity
                  : null,
          onChanged: (newSubCity) {
            setState(() {
              _selectedSubCity = newSubCity;
            });
          },
          items: subCitiesMap[_selectedCity]?.map((subCity) {
            return DropdownMenuItem(
              value: subCity,
              child: Text(subCity),
            );
          }).toList(),
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                  color: themeProvider.getColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.location_city_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: Colors.white,
              filled: true,
              hintText: 'Select your sub city',
              hintStyle: hintStyle
              // labelText: EkubLocalization.of(context)!.translate("sub_city"),
              ),
          dropdownColor: Colors.white,
          isExpanded: true,
          menuMaxHeight: 200, // Set the maximum height for the dropdown menu
        )
      ],
    );
  }

  Column _woredaField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("woreda"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        TextFormField(
          style: lableStyle,
          controller: woredaControl,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
              border: const OutlineInputBorder(
                  borderSide: BorderSide(style: BorderStyle.solid)),
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.location_city_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: Colors.white,
              filled: true,
              hintText: 'Enter your woreda',
              hintStyle: hintStyle
              // labelText: EkubLocalization.of(context)!.translate("woreda"),
              // labelStyle: hintStyle
              ),
          validator: (value) => Sanitizer().checkLength(value!,
              EkubLocalization.of(context)!.translate("woreda"), context),
        )
      ],
    );
  }

  Column _houseNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("house_number"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        TextFormField(
          style: lableStyle,
          controller: houseControl,
          keyboardType: TextInputType.streetAddress,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.house_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: Colors.white,
              filled: true,
              hintText: 'Enter your house number',
              hintStyle: hintStyle
              // labelText:
              //     EkubLocalization.of(context)!.translate("house_number"),
              // labelStyle: hintStyle
              ),
          validator: (value) => Sanitizer().checkLength(value!,
              EkubLocalization.of(context)!.translate("house_number"), context),
        )
      ],
    );
  }

  Column _locationField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("specific_location"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        TextFormField(
          style: lableStyle,
          controller: locationControl,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  // Icons.location_searching_outlined,
                  Icons.work_outline,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: Colors.white,
              filled: true,
              hintText:
                  'Enter your ${EkubLocalization.of(context)!.translate("specific_location")}',
              hintStyle: hintStyle),
          validator: (value) => Sanitizer().isValidField(
              value!,
              EkubLocalization.of(context)!.translate("specific_location"),
              context),
        )
      ],
    );
  }

  _emailField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("email"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        TextFormField(
          style: lableStyle,
          controller: emailControl,
          keyboardType: TextInputType.emailAddress,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.email_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 10, // Adjust the top and bottom padding
                horizontal:
                    10, // Adjust the left and right padding for the text
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: Colors.white,
              filled: true,
              hintText: 'Enter your email address',
              hintStyle: hintStyle),
          validator: (value) => Sanitizer().isValidEmail(value!, context),
          onSaved: (value) {},
        ),
      ],
    );
  }

  _genderField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            "Gender",
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(18),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: 5,
            horizontal: 10,
          ),
          child: Row(
            children: [
              // Male Option
              Row(
                children: [
                  Radio<String>(
                    value: 'Male',
                    activeColor: themeProvider.getColor,
                    groupValue: _selectedGender,
                    onChanged: (value) {
                      setState(() {
                        _selectedGender = value!;
                      });
                    },
                  ),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedGender = 'Male';
                      });
                    },
                    child: Text(
                      EkubLocalization.of(context)?.translate("male") ?? "Male",
                      style: TextStyle(
                        color: themeProvider.getColor,
                        fontWeight: FontWeight.normal,
                        fontSize: fontMedium,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 15),
              // Female Option
              Row(
                children: [
                  Radio<String>(
                    value: 'Female',
                    activeColor: themeProvider.getColor,
                    groupValue: _selectedGender,
                    onChanged: (value) {
                      setState(() {
                        _selectedGender = value!;
                      });
                    },
                  ),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedGender = 'Female';
                      });
                    },
                    child: Text(
                      EkubLocalization.of(context)?.translate("female") ??
                          "Female",
                      style: TextStyle(
                        color: themeProvider.getColor,
                        fontWeight: FontWeight.normal,
                        fontSize: fontMedium,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  _ageField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("date_of_birth"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(height: 5),
        GestureDetector(
          onTap: () {
            _selectDateOfBirth(context); // Open date picker when tapped
          },
          child: AbsorbPointer(
            child: TextFormField(
              controller: _ageController,
              decoration: InputDecoration(
                prefixIcon: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 5),
                  height: 25,
                  width: 30,
                  decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.calendar_today_outlined,
                    size: 20,
                    color: themeProvider.getColor,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 10, // Adjust the top and bottom padding
                  horizontal:
                      10, // Adjust the left and right padding for the text
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(18),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(18),
                  borderSide: BorderSide(color: themeProvider.getColor),
                ),
                hintText: 'Select your date of birth',
                hintStyle: hintStyle,
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return EkubLocalization.of(context)!
                      .translate("please_select_date_of_birth");
                }
                if (!_isAbove18) {
                  return EkubLocalization.of(context)!
                      .translate("you_must_be_above_18");
                }
                return null;
              },
            ),
          ),
        ),
      ],
    );
  }

  _nameTextField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("phone_number"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          style: lableStyle,
          controller: TextEditingController(
            text: "${widget.args.phoneNumber}", // Pre-fill with phone number
          ),
          enabled: false,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.person_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 10, // Adjust the top and bottom padding
                horizontal:
                    10, // Adjust the left and right padding for the text
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(
                    color: Colors.grey.shade400), // Style for disabled state
              ),
              fillColor: Colors.grey.shade200,
              filled: true,
              hintText: "${widget.args.phoneNumber}",
              hintStyle: hintStyle),
        ),
        const SizedBox(
          height: 15,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("full_name"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        TextFormField(
          style: lableStyle,
          controller: TextEditingController(
            text: "${widget.args.name}", // Pre-fill with phone number
          ),
          enabled: false,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.person_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 10, // Adjust the top and bottom padding
                horizontal:
                    10, // Adjust the left and right padding for the text
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(
                    color: Colors.grey.shade400), // Style for disabled state
              ),
              fillColor: Colors.grey.shade200,
              filled: true,
              hintText: "${widget.args.phoneNumber}",
              hintStyle: hintStyle),
        ),
      ],
    );
  }

  _cityField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("city"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(height: 5),
        DropdownButtonFormField<String>(
          value: cities.contains(_selectedCity) ? _selectedCity : null,
          onChanged: onCityChanged,
          items: cities.map((city) {
            return DropdownMenuItem(
              value: city,
              child: Text(city),
            );
          }).toList(),
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                  color: themeProvider.getColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.location_city_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: Colors.white,
              filled: true,
              hintText: 'Select your city',
              hintStyle: hintStyle
              // labelText: 'City',
              ),
        ),
      ],
    );
  }

  _registerNewMember(
    String name,
    String email,
    String gender,
    String phoneNumber,
    String dateOfBirth,
    String? city,
    String woreda,
    String housenumber,
    String location,
  ) async {
    // final phone =
    //     "+251${widget.args.phoneNumber.substring(widget.args.phoneNumber.length - 9)}";
    _doctor['phone_number'] = phoneNumber;

    // Convert the date of birth to the format YYYY-MM-DD
    DateTime dob = DateFormat('dd/MM/yyyy').parse(dateOfBirth);
    String formattedDateOfBirth = DateFormat('yyyy-MM-dd').format(dob);
    _doctor['date_of_birth'] = formattedDateOfBirth;

    var sender = MemberProvider(httpClient: http.Client());

    // Print the formatted date of birth
    // print('Formatted Date of Birth: $formattedDateOfBirth');

    setState(() {
      _onProcess = true;
    });
    var res = sender.autoSignupMember(context, name, phoneNumber, email, gender,
        dateOfBirth, city, woreda, housenumber, location, _doctor);
    res
        .then((value) => {
              setState(() {
                _onProcess = false;
              }),
              // debugPrint("Auth Succ: ${value.message}"),
              if (value.code == "200")
                {_openHomeScreen(value.message == "admin", value.message)}
              // else if (value.code == "401")
              //   {_checkLogin(value.message)}
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("error"),
                    message: value.message,
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                  })
                }
            })
        .onError((error, stackTrace) {
      debugPrint("Error response: $error");
      PanaraConfirmDialog.show(context,
          message: error.toString(),
          confirmButtonText:
              EkubLocalization.of(context)!.translate("try_again"),
          cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
          onTapConfirm: () {
        Navigator.pop(context);
        _registerNewMember(
          name,
          email,
          gender,
          phoneNumber,
          dateOfBirth,
          city,
          woreda,
          housenumber,
          location,
        );
      }, onTapCancel: () {
        Navigator.pop(context);
      }, panaraDialogType: PanaraDialogType.error);
      setState(() {
        _onProcess = false;
      });
      return {};
    });
  }

  _openHomeScreen(bool isAdmin, String role) async {
    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);

    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }

  _openLoginScreen() {
    LoginScreenArgs argument = LoginScreenArgs(isOnline: false);
    Navigator.pushNamedAndRemoveUntil(
        context,
        kIsWeb ? WelcomeScreen.routeName : LoginScreen.routeName,
        (Route<dynamic> route) => false,
        arguments: argument);
  }
}
