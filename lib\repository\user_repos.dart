// ignore_for_file: empty_catches, use_build_context_synchronously

import 'dart:async';
import 'dart:convert';

import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/utils/constants.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:ekub/service/headers.dart';
import 'package:http/http.dart' as http;

import '../models/user.dart';
import '../models/result.dart';
import '../screens/dashboard/root/root_screen.dart';

class AuthDataProvider {
  final _baseUrl = RequestHeader.baseApp;
  final http.Client httpClient;
  final secureStorage = const FlutterSecureStorage();
  AuthDataProvider({required this.httpClient});

  Future<Result> loginUser(
    Map<String, dynamic> user,
    int loginAttempts,
    BuildContext context,
  ) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/auth/login'),
            headers: await RequestHeader().defaultHeader(),
            body: json.encode(user),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        Map<String, dynamic> output = jsonDecode(response.body);
        if (output['code'].toString() == "200") {
          await secureStorage.write(
              key: 'id', value: output['user']['id'].toString());
          await secureStorage.write(
              key: 'phone_number', value: output['user']['phone_number']);
          await secureStorage.write(
              key: 'full_name', value: output['user']['name']);
          await secureStorage.write(key: 'token', value: output['token']);

          await secureStorage.write(
              key: "email", value: output['user']['email'] ?? "");
          await secureStorage.write(
              key: "role", value: output['user']['role'] ?? "none");

          await secureStorage.write(
              key: "gender", value: output["user"]['gender'] ?? "");
          await secureStorage.write(
              key: "member_id", value: output["user"]['member_id'].toString());

          return RequestResult().requestResult(
              response.statusCode.toString(), output['user']['role'] ?? "none");
        } else if (output["message"] == "Incorrect phonenumber or Password!") {
          await secureStorage.write(
              key: "login_attempts", value: loginAttempts.toString());
          return RequestResult()
              .requestResult(output['code'].toString(), output['message']);
        } else {
          return RequestResult()
              .requestResult(output['code'].toString(), output['message']);
        }
      } else {
        return RequestResult()
            .requestResult(response.statusCode.toString(), response.body);
      }
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<Result> changePassword(
      BuildContext context, String oldPassword, String newPassword) async {
    String userId = await getUserId() ?? "null";
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/changePassword/$userId'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              'old_password': oldPassword,
              'new_password': newPassword,
            }),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        var resCode = jsonDecode(response.body)['code'];
        if (resCode.toString() == "200") {
          return Result(response.statusCode.toString(), true,
              jsonDecode(response.body)['message']);
        } else {
          return Result(response.statusCode.toString(), false,
              jsonDecode(response.body)['message']);
        }
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
          return Result(response.statusCode.toString(), false,
              jsonDecode(response.body)['message']);
        } else {
          return Result(response.statusCode.toString(), false,
              jsonDecode(response.body)['message']);
        }
      }
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<Result> resetPassword(
      BuildContext context, int? id, String newPassword, String otp) async {
    // String userId = await getUserId() ?? "null";

    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/resetPassword'),
            headers: await RequestHeader().defaultHeader(),
            body: json.encode(
                {'u_id': id, 'reset_password': newPassword, 'otp': otp}),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        var resCode = jsonDecode(response.body)['code'];
        if (resCode.toString() == "200") {
          return Result(response.statusCode.toString(), true,
              jsonDecode(response.body)['message']);
        } else {
          return Result(response.statusCode.toString(), false,
              jsonDecode(response.body)['message']);
        }
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
          return Result(response.statusCode.toString(), false,
              jsonDecode(response.body)['message']);
        } else {
          return Result(response.statusCode.toString(), false,
              jsonDecode(response.body)['message']);
        }
      }
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<String?> getUserId() async {
    return await secureStorage.read(key: "id");
  }

  Future<String?> getloginAttempts() async {
    return await secureStorage.read(key: "login_attempts");
  }

  Future setloginAttempts(String attempt) async {
    return await secureStorage.write(key: "login_attempts", value: attempt);
  }

  Future<String?> getUserMemberId() async {
    return await secureStorage.read(key: "member_id");
  }

  Future checkPhoneNumber(
    String phone,
    BuildContext context,
  ) async {
    try {
      final response = await http
          .post(Uri.parse('$_baseUrl/checkUserPhoneExist'),
              headers: await RequestHeader().defaultHeader(),
              body: json.encode({
                // "m_id": 1,
                "phone": phone,
              }))
          .timeout(timeout);

      if (response.statusCode == 200) {
        var output = jsonDecode(response.body);
        if (output["data"] == true) {
          return jsonDecode(response.body);
        } else if (output["false"] == false) {
          return jsonDecode(response.body);
        }
      } else {
        return jsonDecode(response.body);
      }
      return jsonDecode(response.body);
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future sendOtp(BuildContext context, String phone) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/sendOtp/$phone'),
            headers: await RequestHeader().defaultHeader(),
          )
          .timeout(timeout);

      var output = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return output;
      } else if (response.statusCode == 401) {
        gotoSignIn(context);
        return output;
      } else {
        return output;
      }
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future verifyOtp(BuildContext context, String phone, String code) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/verifyOtp/$code/$phone'),
            headers: await RequestHeader().defaultHeader(),
          )
          .timeout(timeout);

      var output = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return output;
      } else if (response.statusCode == 401) {
        gotoSignIn(context);
        return output;
      } else {
        return output;
      }
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<Result> isPhoneRegistered(
    String phone,
    BuildContext context,
  ) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/users/findUserByPhoneAndStatus/$phone'),
            headers: await RequestHeader().defaultHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        Map<String, dynamic> output = jsonDecode(response.body);
        if (output['code'] == "200") {
          return RequestResult()
              .requestResult(response.statusCode.toString(), output['message']);
        } else {
          return RequestResult()
              .requestResult(response.statusCode.toString(), output['message']);
        }
      } else {
        return RequestResult()
            .requestResult(response.statusCode.toString(), response.body);
      }
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<User> getUserData() async {
    final token = await secureStorage.readAll();
    (token);
    return User.fromStorage(token);
  }

  Future updateUserData(User user) async {
    await secureStorage.write(key: 'phone_number', value: user.phoneNumber);

    await secureStorage.write(key: "email", value: user.email ?? "");
  }

  Future updateProfile(String url) async {
    await secureStorage.write(
        key: 'profile_image', value: "${_baseUrl}profile/$url");
  }

  Future logOut() async {
    try {
      await http
          .post(
            Uri.parse('$_baseUrl/auth/logout'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);
      // await FirebaseMessaging.instance.deleteToken();
      await secureStorage.deleteAll();
    } catch (e) {}
  }

  Future<String?> getToken() async {
    return await secureStorage.read(key: "token");
  }

  Future<String?> getImageUrl() async {
    return await secureStorage.read(key: "profile_image");
  }
}
