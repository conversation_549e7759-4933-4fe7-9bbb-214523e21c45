// ignore_for_file: must_be_immutable, use_build_context_synchronously

import 'dart:async';

import 'package:ekub/exports/screens.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/validator.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../ui_kits/internet_connectivity_check.dart';
import '../settings/constant.dart';
import '../themes/ThemeProvider.dart';

class ConfirmCode extends StatefulWidget {
  static const routeName = "/confirm_code";
  String phoneNumber;
  String status;
  int userId;
  ConfirmCode(
      {super.key,
      required this.phoneNumber,
      required this.status,
      required this.userId});

  @override
  State<ConfirmCode> createState() => _ConfirmCodeState();
}

class _ConfirmCodeState extends State<ConfirmCode> {
  int resendToken = 0;
  int currentSeconds = 0;
  final int timerMaxSeconds = 120;
  bool _onProcess = false;
  bool isOffline = false;
  bool isSubmitted = false;
  bool resendCode = false;
  bool resending = false;
  String otpCode = "";
  String verificationID = "";
  // User? user;
  final interval = const Duration(seconds: 1);
  late ThemeProvider themeProvider;
  final _confirmFormKey = GlobalKey<FormState>();
  TextEditingController code1 = TextEditingController();
  TextEditingController code2 = TextEditingController();
  TextEditingController code3 = TextEditingController();
  TextEditingController code4 = TextEditingController();
  TextEditingController code5 = TextEditingController();
  TextEditingController code6 = TextEditingController();

  String get timerText =>
      '${((timerMaxSeconds - currentSeconds) ~/ 60).toString().padLeft(2, '0')}: ${((timerMaxSeconds - currentSeconds) % 60).toString().padLeft(2, '0')}';

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  startTimeout([milliseconds]) {
    setState(() {
      resendCode = false;
    });
    var duration = interval;
    Timer.periodic(duration, (timer) {
      if (mounted) {
        setState(() {
          currentSeconds = timer.tick;
          if (timer.tick >= timerMaxSeconds) {
            timer.cancel();
            setState(() {
              resendCode = true;
            });
          }
        });
      }
    });
  }

  @override
  void initState() {
    _onProcess = false;
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    startTimeout();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var language = EkubLocalization.of(context)!;

    return Scaffold(
      body: SingleChildScrollView(
        child: SafeArea(
          child: Form(
            key: _confirmFormKey,
            autovalidateMode: isSubmitted
                ? AutovalidateMode.onUserInteraction
                : AutovalidateMode.disabled,
            child: Column(
              children: [
                Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(15.0),
                      child: IconButton(
                          icon: Icon(
                            Icons.arrow_back,
                            color: themeProvider.getColor,
                          ),
                          onPressed: () {
                            PanaraConfirmDialog.show(
                              context,
                              title: language.translate("warning"),
                              message: language.translate("go_back"),
                              confirmButtonText: language.translate("confirm"),
                              cancelButtonText: language.translate("cancel"),
                              onTapCancel: () {
                                Navigator.pop(context);
                              },
                              onTapConfirm: () {
                                Navigator.pushNamedAndRemoveUntil(
                                  context,
                                  LoginScreen.routeName,
                                  (Route<dynamic> route) => false,
                                  arguments: LoginScreenArgs(
                                    isOnline: true,
                                  ),
                                );
                              },
                              imagePath: warningDialogIcon,
                              panaraDialogType: PanaraDialogType.warning,
                            );
                          }),
                    ),
                  ],
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 30),
                  child: Column(
                    children: [
                      Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 50, vertical: 20),
                        child: const Image(
                          image: AssetImage(
                            "assets/icons/verify.png",
                          ),
                          fit: BoxFit.cover,
                          height: 130,
                        ),
                      ),
                      Text(
                        language.translate("otp_title"),
                        style: const TextStyle(
                          fontSize: fontBig,
                          fontWeight: boldFont,
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Text(
                        "${language.translate("otp_message")}\n +251${widget.phoneNumber}.",
                        style: TextStyle(
                          fontSize: fontSmall,
                          fontWeight: normalFontWeight,
                          color: bodyTextColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(language.translate("resend_otp"),
                              style: TextStyle(
                                color: bodyTextColor,
                                fontWeight: normalFontWeight,
                                fontSize: fontSmall,
                              )),
                          Text(' $timerText ',
                              style: TextStyle(
                                color: themeProvider.getColor,
                                fontWeight: boldFont,
                                fontSize: fontBig,
                              )),
                        ],
                      ),
                      const SizedBox(
                        height: 28,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _textFieldOTP(
                              first: true, last: false, otpControl: code1),
                          _textFieldOTP(
                              first: false, last: false, otpControl: code2),
                          _textFieldOTP(
                              first: false, last: false, otpControl: code3),
                          _textFieldOTP(
                              first: false, last: false, otpControl: code4),
                          _textFieldOTP(
                              first: false, last: false, otpControl: code5),
                          _textFieldOTP(
                              first: false, last: true, otpControl: code6),
                        ],
                      ),
                      const SizedBox(
                        height: 22,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GestureDetector(
                            onTap: resending
                                ? null
                                : () async {
                                    final _form = _confirmFormKey.currentState;
                                    setState(() {
                                      isSubmitted = true;
                                    });
                                    if (_form!.validate()) {
                                      setState(() {
                                        _onProcess = true;
                                        otpCode =
                                            "${code1.text}${code2.text}${code3.text}${code4.text}${code5.text}${code6.text}";
                                      });
                                      _form.save();
                                      if (!await InternetConnectivity()
                                          .checkInternetConnectivty(
                                              context, true)) {
                                        setState(() {
                                          _onProcess = false;
                                        });
                                        return;
                                      } else {
                                        if (otpCode.length == 6) {
                                          verifyOtp(context, widget.phoneNumber,
                                              otpCode);
                                        } else {
                                          PanaraInfoDialog.show(
                                            context,
                                            title: EkubLocalization.of(context)!
                                                .translate("login_failed"),
                                            message:
                                                EkubLocalization.of(context)!
                                                    .translate(
                                                        "invalid_otp_message"),
                                            buttonText:
                                                EkubLocalization.of(context)!
                                                    .translate("okay"),
                                            imagePath: errorDialogIcon,
                                            onTapDismiss: () {
                                              Navigator.pop(context);
                                            },
                                            panaraDialogType:
                                                PanaraDialogType.error,
                                          );
                                          setState(() {
                                            _onProcess = false;
                                          });
                                        }
                                      }
                                    }
                                  },
                            child: Container(
                              width: double.infinity,
                              height: 55,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: !resending
                                      ? themeProvider.getColor
                                      : themeProvider.getColor
                                          .withOpacity(0.5)),
                              padding: const EdgeInsets.all(14.0),
                              child: resending
                                  ? Center(
                                      child: Text(
                                          '${EkubLocalization.of(context)!.translate("resending_code")}...',
                                          style: buttonText),
                                    )
                                  : Center(
                                      child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            const Spacer(),
                                            Text(
                                                EkubLocalization.of(context)!
                                                    .translate("verify"),
                                                style: buttonText),
                                            const Spacer(),
                                            Align(
                                              widthFactor: 2,
                                              alignment: Alignment.centerRight,
                                              child: _onProcess
                                                  ? const Padding(
                                                      padding:
                                                          EdgeInsets.all(8.0),
                                                      child: SizedBox(
                                                        height: 15,
                                                        width: 15,
                                                        child:
                                                            CircularProgressIndicator(
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    )
                                                  : Container(),
                                            ),
                                          ]),
                                    ),
                            ),
                          ),
                          const SizedBox(
                            height: 18,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                  "${EkubLocalization.of(context)!.translate("code_not_sent")}? ",
                                  style: TextStyle(
                                      color: bodyTextColor,
                                      fontWeight: normalFontWeight,
                                      fontSize: fontMedium)),
                              GestureDetector(
                                onTap: resendCode && !resending
                                    ? () {
                                        setState(() {
                                          resending = true;
                                        });
                                        // Timer(const Duration(seconds: 5), () {
                                        sendOtp(context, widget.phoneNumber);
                                      }
                                    : null,
                                child: Text(
                                    EkubLocalization.of(context)!
                                        .translate("resend"),
                                    style: TextStyle(
                                      color: resendCode
                                          ? themeProvider.getColor
                                          : themeProvider.getColor
                                              .withOpacity(0.4),
                                      fontWeight: boldFont,
                                      fontSize: fontBig,
                                    )),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _textFieldOTP(
      {required bool first,
      required bool last,
      required TextEditingController otpControl}) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.1,
      width: MediaQuery.of(context).size.width * 0.12,
      child: AspectRatio(
        aspectRatio: 1,
        child: TextFormField(
          autofocus: true,
          onChanged: (value) {
            if (value.length == 1 && last == false) {
              FocusScope.of(context).nextFocus();
            }
            if (value.isEmpty && first == false) {
              FocusScope.of(context).previousFocus();
            }
          },
          showCursor: false,
          readOnly: false,
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          keyboardType: TextInputType.number,
          maxLength: 1,
          decoration: InputDecoration(
            counter: const Offstage(),
            enabledBorder: OutlineInputBorder(
                borderSide: const BorderSide(width: 2, color: Colors.black12),
                borderRadius: BorderRadius.circular(12)),
            focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(width: 2, color: themeProvider.getColor),
                borderRadius: BorderRadius.circular(12)),
          ),
          controller: otpControl,
          validator: ((value) =>
              Sanitizer().isValidField("code", value!, context)),
        ),
      ),
    );
  }

  void sendOtp(BuildContext context, String phone) {
    var sender = AuthDataProvider(httpClient: http.Client());
    startTimeout();
    var res = sender.sendOtp(context, phone);
    res
        .then((value) => {
              if (value["acknowledge"] == "success")
                {
                  setState(() {
                    _onProcess = false;
                    resending = false;
                  }),
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value["message"][0],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                    resending = false;
                  }),
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          sendOtp(context, phone);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        _onProcess = false;
        resending = false;
      });
      return {};
    });
  }

  void verifyOtp(BuildContext context, String phone, String code) {
    var sender = AuthDataProvider(httpClient: http.Client());
    setState(() {
      _onProcess = true;
    });

    var res = sender.verifyOtp(context, phone, code);
    res
        .then((value) => {
              if (value["acknowledge"] == "success")
                {
                  if (widget.status == "newUser")
                    {
                      Navigator.pushReplacementNamed(
                          context, RegisterScreen.routeName,
                          arguments: RegisterScreenArgs(
                              isOnline: true, phoneNumber: widget.phoneNumber)),
                      setState(() {
                        _onProcess = false;
                        resending = false;
                      }),
                    }
                  else if (widget.status == "forgotPassword")
                    {
                      Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                              builder: (context) => ResetPassword(
                                  userId: widget.userId,
                                  args: ResetPasswordArgs(
                                    otp: otpCode,
                                    isOnline: true,
                                  )))),
                      setState(() {
                        _onProcess = false;
                        resending = false;
                      }),
                    },
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("error"),
                    message: value["message"][0],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                    resending = false;
                  }),
                },
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            // title: "Request Timeout",
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          verifyOtp(context, phone, otpCode);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
        setState(() {
          _onProcess = false;
        });
      }
      return {};
    });
  }
}
