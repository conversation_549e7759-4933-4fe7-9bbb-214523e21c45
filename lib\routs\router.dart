import 'package:ekub/exports/models.dart';
import 'package:ekub/exports/screens.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/admin/members.dart';
import 'package:ekub/screens/dashboard/ekubs/add_ekub_type.dart';
import 'package:ekub/screens/dashboard/notifications/view_notification.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../init/splash.dart';
import '../screens/dashboard/admin/actions/add_member.dart';
import '../screens/dashboard/admin/actions/user_info.dart';
import '../screens/dashboard/admin/member_equbs.dart';
import '../screens/dashboard/admin/tabs/member_tabs.dart';
import '../screens/dashboard/reports/reports.dart';
import '../screens/dashboard/root/cubit/botttom_nav_cubit.dart';
import '../screens/ui_kits/connectivity_test_page.dart';

class AppRoute {
  static Route generateRoute(RouteSettings settings) {
    // if (settings.name == ConfirmCode.routeName) {
    //   ConfirmCodeArgs argument = settings.arguments as ConfirmCodeArgs;
    //   return MaterialPageRoute(
    //       builder: (context) => ConfirmCode(
    //             // args: argument,
    //           ));
    // }
    if (settings.name == HomeScreen.routeName) {
      HomeScreenArgs argument = settings.arguments as HomeScreenArgs;
      return MaterialPageRoute(builder: (context) {
        return BlocProvider(
          create: (context) => BottomNavCubit(),
          child: HomeScreen(
            args: argument,
          ),
        );
      });
    }
    if (settings.name == ResetPassword.routeName) {
      ResetPasswordArgs argument = settings.arguments as ResetPasswordArgs;
      return MaterialPageRoute(
          builder: (context) => ResetPassword(
                userId: argument as int,
                args: argument,
              ));
    }
    if (settings.name == UpdateAccount.routeName) {
      UpdateAccountArgs argumnet = settings.arguments as UpdateAccountArgs;
      return MaterialPageRoute(
          builder: (context) => UpdateAccount(
                user: argumnet as Member,
                args: argumnet,
              ));
    }
    if (settings.name == ChangePassword.routeName) {
      ChangePasswordArgs argument = settings.arguments as ChangePasswordArgs;
      return MaterialPageRoute(
          builder: (context) => ChangePassword(
                arg: argument,
              ));
    }
    if (settings.name == AddMember.routeName) {
      AddMemberArgs argument = settings.arguments as AddMemberArgs;
      return MaterialPageRoute(
          builder: (context) => AddMember(
                args: argument,
              ));
    }

    if (settings.name == AddEqubType.routeName) {
      AddEqubTypeArgs argument = settings.arguments as AddEqubTypeArgs;
      return MaterialPageRoute(
          builder: (context) => AddEqubType(
                args: argument,
              ));
    }

    if (settings.name == UserInfo.routeName) {
      UserInfoArgs argument = settings.arguments as UserInfoArgs;
      return MaterialPageRoute(
          builder: (context) => UserInfo(
                args: argument,
              ));
    }

    if (settings.name == LoginScreen.routeName) {
      LoginScreenArgs argument = settings.arguments as LoginScreenArgs;
      return MaterialPageRoute(
          builder: (context) => LoginScreen(
                args: argument,
              ));
    }

    if (settings.name == WelcomeScreen.routeName) {
      LoginScreenArgs argument = settings.arguments as LoginScreenArgs;
      return MaterialPageRoute(
          builder: (context) => WelcomeScreen(
                args: argument,
              ));
    }

    if (settings.name == RegisterScreen.routeName) {
      RegisterScreenArgs argument = settings.arguments as RegisterScreenArgs;
      return MaterialPageRoute(
          builder: (context) => RegisterScreen(
                args: argument,
              ));
    }

    // Auto Registration
    if (settings.name == AutoRegisterScreen.routeName) {
      AutoRegisterScreenArgs argument =
          settings.arguments as AutoRegisterScreenArgs;
      return MaterialPageRoute(
          builder: (context) => AutoRegisterScreen(
                args: argument,
              ));
    }

    if (settings.name == MemberTabs.routeName) {
      MemberETabsArgs argument = settings.arguments as MemberETabsArgs;
      return MaterialPageRoute(
          builder: (context) => MemberTabs(
                args: argument,
              ));
    }
    if (settings.name == MemberEqubs.routeName) {
      MemberEqubsArgs argument = settings.arguments as MemberEqubsArgs;
      return MaterialPageRoute(
          builder: (context) => MemberEqubs(
                args: argument,
              ));
    }
    if (settings.name == MembersScreen.routeName) {
      MemberEqubsArgs argument = settings.arguments as MemberEqubsArgs;
      return MaterialPageRoute(
          builder: (context) => MembersScreen(
                args: argument,
              ));
    }

    if (settings.name == ViewNTFS.routeName) {
      NtfsDetailArgs argument = settings.arguments as NtfsDetailArgs;
      return MaterialPageRoute(
          builder: (context) => ViewNTFS(
                args: argument,
              ));
    }
    // if (settings.name == ProfilePage.routeName) {
    //   return MaterialPageRoute(builder: (context) => const ProfilePage());
    // }
    if (settings.name == PhoneVerification.routeName) {
      return MaterialPageRoute(
          builder: (context) => PhoneVerification(
                status: "",
              ));
    }

    if (settings.name == ReportScreen.routeName) {
      return MaterialPageRoute(builder: (context) => const ReportScreen());
    }

    if (settings.name == '/connectivity_test') {
      return MaterialPageRoute(
          builder: (context) => const ConnectivityTestPage());
    }

    if (settings.name == '/language_selection') {
      return MaterialPageRoute(
          builder: (context) => const LanguageSelectionScreen());
    }

    return MaterialPageRoute(builder: (context) => const SplashScreen());
  }
}
