import UIKit
import Flutter 

@main
@objc class AppDelegate: FlutterAppDelegate, EthiopiaPayManagerDelegate {

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)


        let controller = window?.rootViewController as! FlutterViewController
        let telebirrChannel = FlutterMethodChannel(name: "telebirr", binaryMessenger: controller.binaryMessenger)

        telebirrChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
            if call.method == "nativeFunction" {
            

                guard let arguments = call.arguments as? [String: Any],
                    
                      let appId = arguments["appid"] as? String,
                      let shortCode = arguments["merch_code"] as? String,
                      let prepayId = arguments["prepay_id"] as? String,
                      let amount = arguments["amount"] as? String else {
                        print("Received call from flutter: \(call.arguments )")
                    result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing or invalid arguments", details: nil))
                    return
                }
                
                print("Received arguments: appId=\(appId), shortCode=\(shortCode), prepayId=\(prepayId), amount=\(amount)")
                
                self?.initiatePayment(appId: appId, shortCode: shortCode, prepayId: prepayId, amount: amount)
                result("Payment initiated")
            } else {
                result(FlutterMethodNotImplemented)
            }
        }


        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    override func application(
        _ app: UIApplication,
        open url: URL,
        options: [UIApplication.OpenURLOptionsKey : Any] = [:]
    ) -> Bool {
        EthiopiaPayManager.shared().handleOpen(url)
        return true
    }

    func initiatePayment(appId: String, shortCode: String, prepayId: String, amount: String) {
        do {
            print("initiatePayment: \(appId) \(shortCode) \(prepayId) \(amount)")
         let timestamp = getCurrentTimestampString()
        let receiveCode = "TELEBIRR$virtualEqub$\(shortCode)$\(amount)$\(prepayId)$\(timestamp)"

        let manager = EthiopiaPayManager.shared()
        manager.delegate = self
        
        manager.startPay(withAppId: appId, shortCode: shortCode, receiveCode: receiveCode, returnAppScheme: "virtualEqub")
        print("Payment initiated")
        } catch {
            print("Error: \(error)")
        }
        
       
    }

    func getCurrentTimestampString() -> String {
        let currentDate = Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMddHHmmss"
        return formatter.string(from: currentDate)
    }

    // EthiopiaPayManagerDelegate method
    func payResultCallback(withCode code: Int, msg: String) {
        // Handle the payment result
    }
}
