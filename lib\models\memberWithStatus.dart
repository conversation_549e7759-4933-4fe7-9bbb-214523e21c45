
// ignore_for_file: file_names

import '../exports/models.dart';

class MemberWithStatus {
  String? completedEqubs;
  String? ongoingEqubs;
  Member member;
  MemberWithStatus({
    this.completedEqubs,
    this.ongoingEqubs,
    required this.member
  });
  factory MemberWithStatus.fromJson(Map<String, dynamic> json) {
    return MemberWithStatus(
      completedEqubs: json["inactiveEqubs"],
      ongoingEqubs: json["activeEqubs"],
      member: Member.from<PERSON><PERSON>(json["member"])
    );
  }
}
