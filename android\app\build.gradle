// def localProperties = new Properties()
// def localPropertiesFile = rootProject.file('local.properties')
// if (localPropertiesFile.exists()) {
//     localPropertiesFile.withReader('UTF-8') { reader ->
//         localProperties.load(reader)
//     }
// }
// def keystoreProperties = new Properties()
// def keystorePropertiesFile = rootProject.file('key.properties')
// if (keystorePropertiesFile.exists()) {
//     keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
// }
// def flutterRoot = localProperties.getProperty('flutter.sdk')
// if (flutterRoot == null) {
//     throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
// }

// def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
// if (flutterVersionCode == null) {
//     flutterVersionCode = '1'
// }

// def flutterVersionName = localProperties.getProperty('flutter.versionName')
// if (flutterVersionName == null) {
//     flutterVersionName = '1.0'
// }

// apply plugin: 'com.android.application'
// apply plugin: 'kotlin-android'
// // apply plugin: 'com.google.gms.google-services'
// apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"


// android {
//     compileSdkVersion flutter.compileSdkVersion
//     ndkVersion flutter.ndkVersion

//     compileOptions {
//         sourceCompatibility JavaVersion.VERSION_1_8
//         targetCompatibility JavaVersion.VERSION_1_8
//     }

//     kotlinOptions {
//         jvmTarget = '1.8'
//     }

//     sourceSets {
//         main.java.srcDirs += 'src/main/kotlin'
//     }

//     defaultConfig {
//         // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
//         applicationId "com.vintechplc.virtualequb"
//         // You can update the following values to match your application needs.
//         // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.

//         minSdkVersion 23
//         targetSdkVersion 33
//         versionCode flutterVersionCode.toInteger()
//         versionName flutterVersionName
//     }
//     signingConfigs {
//        release {
//             keyAlias keystoreProperties['keyAlias']
//             keyPassword keystoreProperties['keyPassword']
//             storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
//             storePassword keystoreProperties['storePassword']
//         }

//     }
//     buildTypes {
//         release {
//             // TODO: Add your own signing config for the release build.
//             // Signing with the debug keys for now, so `flutter run --release` works.
//             signingConfig signingConfigs.release
//         }
//     }
// }

// flutter {
//     source '../..'
// }

// dependencies {
//     implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
//     // implementation platform('com.google.firebase:firebase-bom:32.1.1')
//     // implementation 'com.google.firebase:firebase-analytics'
//     implementation "androidx.browser:browser:1.2.0"


    
// }

// app/build.gradle

// Apply necessary plugins
plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

// Load keystore properties
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

// Load local properties
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

// Set Flutter version code and name
def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '1'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '1.0'

android {
    // Android configuration
    compileSdkVersion 34
    namespace "com.vintechplc.virtualequb"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion "25.1.8937393"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // Default app configuration
        applicationId "com.vintechplc.virtualequb"
        minSdkVersion 21
        targetSdkVersion 34  // Update this line
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    // Signing configuration for release build
   signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // Use the release signing configuration
            signingConfig signingConfigs.release
        }
    }
}


// Flutter configuration
flutter {
    source '../..'
}

// Firebase and Google Play Services dependencies
dependencies {
    implementation 'com.google.android.material:material:1.8.0'
    // Firebase BOM
    implementation platform("com.google.firebase:firebase-bom:32.7.2")
    implementation "androidx.browser:browser:1.2.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10"
    implementation platform('com.google.firebase:firebase-bom:32.0.0')
    implementation 'com.google.firebase:firebase-messaging'
    //implementation fileTree(dir: 'libs', include: ['*.aar'])
    // implementation files('libs/EthiopiaPaySdkModule-prod-release.aar')
    // implementation files('libs/EthiopiaPaySdkModule-uat-release.aar')
    implementation files('libs/EthiopiaPaySdkModule-prod-release.aar')
    implementation 'androidx.annotation:annotation:1.5.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
}

// At the bottom of the file
apply plugin: 'com.google.gms.google-services'
