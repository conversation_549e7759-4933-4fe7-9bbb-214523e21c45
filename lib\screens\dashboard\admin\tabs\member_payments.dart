// ignore_for_file: must_call_super, no_logic_in_create_state, empty_catches, use_build_context_synchronously

import 'dart:async';
import 'dart:io';
import 'dart:convert';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/models/telebirr.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/screens/payment/webview_screen.dart';
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:ekub/service/headers.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/device.dart';
import 'package:ekub/utils/tools.dart';
import 'package:ekub/utils/validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_telebirr/flutter_telebirr.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:ekub/repository/language.dart';

import '../../../../models/ekub.dart';
import '../../../../models/members.dart';
import '../../../../models/user.dart';
import '../../../../repository/user_repos.dart';
import '../../../settings/constant.dart';
import '../../../themes/ThemeProvider.dart';
import '../../../ui_kits/internet_connectivity_check.dart';
import '../../../ui_kits/list_view.dart';
import '../../../ui_kits/no_internet_connection_found.dart';
import '../../../../screens/payment/payment_options.dart';
import '../../../../screens/payment/payment_resolution_screen.dart';
import '../../../../utils/js_payment_executor.dart';

class MemberPayments extends StatefulWidget {
  static const routeName = "/member_payment";

  final int index;
  final Member? member;
  final Equb? equb;
  final String? role;
  final double? remainingAmount;
  const MemberPayments({
    super.key,
    required this.index,
    required this.member,
    required this.equb,
    required this.role,
    this.remainingAmount,
  });

  @override
  State<MemberPayments> createState() => _MemberPaymentsState();
}

class _MemberPaymentsState extends State<MemberPayments>
    with AutomaticKeepAliveClientMixin<MemberPayments> {
  @override
  bool get wantKeepAlive => true;
  int page = 1;
  int offset = 0;
  bool isConnected = true;
  bool _onProcess = false;
  bool _isLoading = true;
  bool isSubmitted = false;
  bool _isMessageLoading = false;
  bool hasNextPage = true;
  bool _isLoadMoreRunning = false;
  List<Payment>? payments = [];
  File? _image;
  String amount = "0";
  String credit = "0";
  String selectedType = "Cash";
  late User user;
  Payments? allPayments;
  List<String> paymentTypes = ['Cash', 'Check', 'Bank Transfer', 'Other'];
  late ThemeProvider themeProvider;
  late ScrollController _controller;
  final dropdownControl = TextEditingController();
  final amountControl = TextEditingController();
  // final creditControl = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  // Language selection variables
  List<String> languages = ["English", "አማርኛ", "Oromic", "ትግሪኛ", "Somaali"];
  String selectedLanguage = "Language";
  late AppLanguage appLanguage;

  @override
  void initState() {
    super.initState();
    // Log debug information
    debugPrint('user id: ${widget.member?.id}');
    debugPrint(
        'Remaining Amount from member_equbs: ${widget.remainingAmount?.toStringAsFixed(2) ?? "Not available"}');

    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    appLanguage = Provider.of<AppLanguage>(context, listen: false);
    _controller = ScrollController()..addListener(_loadMore);
    amount = widget.equb!.amount!;
    amountControl.text = amount;
    _fetchLocale();
    _loadProfile();
    loadPayments();
  }

  _fetchLocale() async {
    await appLanguage.fetchLocale();
    String language = appLanguage.appLocale.languageCode.toLowerCase();

    if (language == "fr") {
      setState(() {
        selectedLanguage = "Oromic";
      });
    } else if (language == "es") {
      setState(() {
        selectedLanguage = "ትግሪኛ";
      });
    } else if (language == "am") {
      setState(() {
        selectedLanguage = "አማርኛ";
      });
    } else if (language == "tl") {
      setState(() {
        selectedLanguage = "Somaali";
      });
    } else if (language == "en") {
      setState(() {
        selectedLanguage = "English";
      });
    }
  }

  var proLoaded = false;
  _loadProfile() {
    var auth = AuthDataProvider(httpClient: http.Client());
    auth.getUserData().then((value) => {
          setState(() {
            user = value;
            proLoaded = true;
          })
        });
  }

  void loadPayments() async {
    setState(() {
      _isLoading = true;
    });
    try {
      page = 1;
      offset = 0;
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        if (mounted) {
          setState(() {
            isConnected = false;
            _isLoading = false;
          });
        }
        return;
      }

      try {
        await Provider.of<MemberProvider>(context, listen: false).getPayments(
            context,
            widget.member!.id!.toString(),
            widget.equb!.id.toString(),
            page,
            offset,
            false,
            false);
      } catch (e) {
        debugPrint("Error loading payments: $e");
        // Continue with the function, we'll handle the error in the outer catch block
      }
    } catch (e) {
      if (e is TimeoutException) {
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title:
                    Text(EkubLocalization.of(context)!.translate("time_out")),
                content: Text(
                    EkubLocalization.of(context)!.translate("timeout_message")),
                actions: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                    ),
                    child:
                        Text(EkubLocalization.of(context)!.translate("cancel")),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      loadPayments();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeProvider.getLightColor,
                    ),
                    child: Text(
                        EkubLocalization.of(context)!.translate("try_again")),
                  ),
                ],
              );
            },
          );
        }
      } else {
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text(EkubLocalization.of(context)!.translate("error")),
                content: Text(e.toString()),
                actions: [
                  ElevatedButton(
                    onPressed: () {
                      loadPayments();
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeProvider.getLightColor,
                    ),
                    child: Text(
                        EkubLocalization.of(context)!.translate("try_again")),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                    ),
                    child:
                        Text(EkubLocalization.of(context)!.translate("cancel")),
                  ),
                ],
              );
            },
          );
        }
      }
    }
    if (mounted) {
      setState(() {
        _isLoading = false;
        isConnected = true;
        _isMessageLoading = false;
        _isLoadMoreRunning = false;
      });
    }
  }

  void _loadMore() async {
    if (hasNextPage == true &&
        _controller.offset >= _controller.position.maxScrollExtent &&
        !_controller.position.outOfRange &&
        _isMessageLoading == false &&
        _isLoadMoreRunning == false &&
        _controller.position.extentAfter < 50) {
      try {
        setState(() {
          _isLoadMoreRunning = true;
        });
        page += 1;
        offset += 10;

        Map<String, dynamic> result = {
          'success': false,
          'has_next_page': false,
          'current_page': page
        };
        try {
          result = await Provider.of<MemberProvider>(context, listen: false)
              .getPayments(context, widget.member!.id!.toString(),
                  widget.equb!.id.toString(), page, offset, false, true);
        } catch (e) {
          debugPrint("Error loading more payments: $e");
          // We'll handle this with the default result values
        }

        // debugPrint("result: ${result['has_next_page']}");
        if (result['success'] == true) {
          setState(() {
            hasNextPage = result['has_next_page'];
            page = result['current_page'];
          });
        } else {
          // Show error message if needed
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content:
                    Text(result['message'] ?? 'Failed to load more payments')),
          );
        }
      } catch (e) {
        if (e is TimeoutException) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title:
                    Text(EkubLocalization.of(context)!.translate("time_out")),
                content: Text(
                    EkubLocalization.of(context)!.translate("timeout_message")),
                actions: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                    ),
                    child:
                        Text(EkubLocalization.of(context)!.translate("cancel")),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      loadPayments();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeProvider.getLightColor,
                    ),
                    child: Text(
                        EkubLocalization.of(context)!.translate("try_again")),
                  ),
                ],
              );
            },
          );
        } else {
          //   PanaraConfirmDialog.show(context,
          //       message: "Something went wrong! Please try again.",
          //       confirmButtonText: "Try Again",
          //       cancelButtonText: "Cancel", onTapConfirm: () {
          //     loadPayments(false);
          //     Navigator.pop(context);
          //   }, onTapCancel: () {
          //     Navigator.pop(context);
          //   }, panaraDialogType: PanaraDialogType.error);
        }
      }

      setState(() {
        _isMessageLoading = false;
        _isLoadMoreRunning = false;
      });
    }
  }

  void addPayment() {}

  @override
  Widget build(BuildContext context) {
    payments = Provider.of<MemberProvider>(context, listen: true).payments;

    var language = EkubLocalization.of(context)!;

    // Calculate the total paid amount
    double totalPaidAmount = payments?.fold(0.0, (sum, payment) {
          return sum! + (double.tryParse(payment.amount ?? "0") ?? 0);
        }) ??
        0.0;

    // Calculate the total expected amount
    double totalExpectedAmount =
        double.tryParse(widget.equb?.amount ?? "0") ?? 0.0;

    // Log debug information
    debugPrint('user id: ${widget.member?.id}');
    debugPrint(
        'Remaining Amount from member_equbs: ${widget.remainingAmount?.toStringAsFixed(2) ?? "Not available"}');
    debugPrint("Equb Start Date: ${widget.equb?.startDate}");

    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Text(
          language.translate("payment_history"),
          style: TextStyle(
              color: themeProvider.getColor,
              fontWeight: boldFont,
              fontSize: fontBig),
        ),
        // actions: [
        //   // Language Settings Button
        //   PopupMenuButton<String>(
        //     icon: Icon(
        //       Icons.language,
        //       color: themeProvider.getColor,
        //       size: 25,
        //     ),
        //     tooltip: 'Language Settings',
        //     onSelected: (value) {
        //       setState(() {
        //         selectedLanguage = value;
        //         if (selectedLanguage == "English") {
        //           appLanguage.changeLanguage(const Locale("en"));
        //         } else if (selectedLanguage == "አማርኛ") {
        //           appLanguage.changeLanguage(const Locale("am"));
        //         } else if (selectedLanguage == "Oromic") {
        //           appLanguage.changeLanguage(const Locale("fr"));
        //         } else if (selectedLanguage == "Somaali") {
        //           appLanguage.changeLanguage(const Locale("tl"));
        //         } else if (selectedLanguage == "ትግሪኛ") {
        //           appLanguage.changeLanguage(const Locale("es"));
        //         }
        //       });
        //     },
        //     itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        //       const PopupMenuItem<String>(
        //         value: 'English',
        //         child: Text('English'),
        //       ),
        //       const PopupMenuItem<String>(
        //         value: 'አማርኛ',
        //         child: Text('አማርኛ'),
        //       ),
        //       const PopupMenuItem<String>(
        //         value: 'ትግሪኛ',
        //         child: Text('ትግሪኛ'),
        //       ),
        //       const PopupMenuItem<String>(
        //         value: 'Oromic',
        //         child: Text('Oromic'),
        //       ),
        //       const PopupMenuItem<String>(
        //         value: 'Somaali',
        //         child: Text('Somaali'),
        //       ),
        //     ],
        //   ),
        //   // Refresh Button
        //   IconButton(
        //     icon: Icon(
        //       Icons.refresh,
        //       color: themeProvider.getColor,
        //     ),
        //     onPressed: () {
        //       loadPayments();
        //     },
        //   ),
        // ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          loadPayments();
        },
        key: _refreshIndicatorKey,
        child: SizedBox(
          height: Device.body(context),
          child: isConnected
              ? _isLoading
                  ? searchLoading()
                  : listHolder(payments, themeProvider.getColor, language,
                      totalPaidAmount, totalExpectedAmount)
              : NoConnectionWidget(
                  fun: loadPayments,
                  isLoading: _isLoading,
                ),
        ),
      ),
    );
  }

  Widget listHolder(items, theme, EkubLocalization language,
      double totalPaidAmount, double totalExpectedAmount) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        proLoaded
            ? double.parse(widget.equb!.remainingPayment ?? "0.0") > 0.0
                ? Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                        decoration: BoxDecoration(
                            // color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(20)),
                        margin: const EdgeInsets.fromLTRB(10, 0, 10, 10),
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                        child: Column(
                          children: <Widget>[
                            Form(
                              key: _formKey,
                              autovalidateMode: !isSubmitted
                                  ? AutovalidateMode.disabled
                                  : AutovalidateMode.onUserInteraction,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Row(
                                    children: [
                                      user.role == "member"
                                          ? Container()
                                          // _image != null
                                          //     ? Stack(
                                          //         children: [
                                          //           Container(
                                          //             margin:
                                          //                 const EdgeInsets.only(
                                          //                     right: 5,
                                          //                     top: 20,
                                          //                     bottom: 10),
                                          //             height: 130,
                                          //             width: 200,
                                          //             decoration: BoxDecoration(
                                          //                 image: DecorationImage(
                                          //                     image: FileImage(
                                          //                         _image!),
                                          //                     fit: BoxFit
                                          //                         .cover)),
                                          //           ),
                                          //           Positioned(
                                          //             right: 5,
                                          //             top: 20,
                                          //             child: Container(
                                          //               color: whiteText,
                                          //               child: GestureDetector(
                                          //                 onTap: _removeImage,
                                          //                 child: const Icon(
                                          //                   Icons.cancel,
                                          //                   color: Colors.red,
                                          //                   size: 30,
                                          //                 ),
                                          //               ),
                                          //             ),
                                          //           ),
                                          //         ],
                                          //       )
                                          //     : Container(
                                          //         margin: const EdgeInsets.only(
                                          //             right: 5,
                                          //             top: 10,
                                          //             bottom: 10),
                                          //         decoration: BoxDecoration(
                                          //           borderRadius:
                                          //               BorderRadius.circular(
                                          //                   20),
                                          //           color: Colors.white,
                                          //           boxShadow: [
                                          //             BoxShadow(
                                          //               color: Colors.grey
                                          //                   .withOpacity(
                                          //                       0.5), //color of shadow
                                          //               spreadRadius:
                                          //                   .1, //spread radius
                                          //               blurRadius:
                                          //                   2, // blur radius
                                          //               offset: const Offset(0,
                                          //                   2), // changes position of shadow
                                          //             ),
                                          //           ],
                                          //         ),
                                          //         height: 150,
                                          //         width: 200,
                                          //         child: Column(
                                          //           mainAxisAlignment:
                                          //               MainAxisAlignment
                                          //                   .center,
                                          //           crossAxisAlignment:
                                          //               CrossAxisAlignment
                                          //                   .center,
                                          //           children: [
                                          //             InkWell(
                                          //               onTap: _getGalleryImage,
                                          //               child: Image.asset(
                                          //                 "assets/icons/upload-cloud.png",
                                          //                 height: 50,
                                          //               ),
                                          //             ),
                                          //             Text(
                                          //               EkubLocalization.of(
                                          //                       context)!
                                          //                   .translate(
                                          //                       "upload_image"),
                                          //               style: lableStyle,
                                          //               textAlign:
                                          //                   TextAlign.center,
                                          //             ),
                                          //           ],
                                          //         ),
                                          //       )
                                          : Flexible(
                                              flex: 1,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 8.0),
                                                    child: Text(
                                                        language.translate(
                                                            "select_payment_method"),
                                                        style: lableStyle),
                                                  ),
                                                  const SizedBox(
                                                    height: 3,
                                                  ),
                                                  CustomDropdown<String>(
                                                    // fillColor: Colors.white,
                                                    onChanged: (value) {
                                                      setState(() {
                                                        selectedType =
                                                            value.toString();
                                                      });
                                                    },
                                                    hintText: language.translate(
                                                        "select_payment_method"),
                                                    items: paymentTypes,
                                                    // controller: dropdownControl,
                                                    // errorText:
                                                    //     language.translate(
                                                    //         "method_empty"),
                                                  ),
                                                ],
                                              ),
                                            ),
                                      user.role == "member"
                                          ? Container()
                                          : const SizedBox(
                                              width: 10,
                                            ),
                                      // user.role == "member"
                                      //     ? Flexible(
                                      //         flex: 1,
                                      //         child: Column(
                                      //           children: [
                                      //             makePayment(context),
                                      //             sendPayment(language)
                                      //           ],
                                      //         ))
                                      //     :

                                      Flexible(
                                          flex: 1,
                                          child: Container(
                                              margin: const EdgeInsets.only(
                                                  top: 10),
                                              child: makePayment(
                                                  context,
                                                  totalPaidAmount,
                                                  totalExpectedAmount))),
                                      user.role != "member"
                                          ? Container()
                                          : Flexible(
                                              flex: 1,
                                              child: Container(
                                                  height: 60,
                                                  margin: const EdgeInsets.only(
                                                      top: 20, left: 15),
                                                  child: sendPayment(language)))
                                    ],
                                  ),
                                  user.role != "member"
                                      ? sendPayment(language)
                                      : Container(),
                                  const SizedBox(
                                    width: 8,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )),
                  )
                : Container()
            : Container(),
        payments!.isNotEmpty
            ? Expanded(
                child: ListView.builder(
                    controller: _controller,
                    itemCount: payments!.length,
                    padding: const EdgeInsets.all(0.0),
                    itemBuilder: (context, item) {
                      return _buildListItems(
                          context, payments![item], item, theme);
                    }),
              )
            : Expanded(
                child: ListView(
                  children: [
                    Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 0),
                      height: 200,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                              image: AssetImage("assets/icons/pay.png"))),
                      // child: const Image(
                      //   image: AssetImage(
                      //     "assets/icons/pay.png",
                      //   ),
                      //   fit: BoxFit.cover,
                      // ),
                    ),
                    Center(
                        child: Text(
                      "${language.translate("no_payment_history")} !",
                      style: TextStyle(
                          color: bodyTextColor, fontWeight: normalFontWeight),
                    )),
                  ],
                ),
              ),
        if (_isLoadMoreRunning == true)
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Center(
              child: CircularProgressIndicator(
                color: themeProvider.getColor,
                strokeWidth: 1,
              ),
            ),
          ),
      ],
    );
  }

  GestureDetector sendPayment(EkubLocalization language) {
    return GestureDetector(
      onTap: _onProcess ||
              widget.member!.status != "Active" ||
              widget.equb!.status == "Deactive"
          ? null
          : () async {
              final form = _formKey.currentState;
              setState(() {
                isSubmitted = true;
              });
              if (form!.validate()) {
                form.save();

                // Check if the entered amount exceeds the remaining amount
                double enteredAmount =
                    double.tryParse(amountControl.text) ?? 0.0;
                double remainingAmount = widget.remainingAmount ?? 0.0;

                if (enteredAmount > remainingAmount) {
                  // Show dialog if the entered amount exceeds the remaining amount
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text("Limit Reached"),
                        content: Text(
                            "You have exceeded the maximum allowed amount. Please adjust your input and try again."),
                        actions: [
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: themeProvider.getLightColor,
                            ),
                            child: Text(language.translate("okay")),
                          ),
                        ],
                      );
                    },
                  );
                  return; // Exit the function to prevent further processing
                }

                if (!await InternetConnectivity()
                    .checkInternetConnectivty(context, true)) {
                  setState(() {
                    _onProcess = false;
                  });
                  return;
                } else {
                  DateTime currentDate = DateTime.now();
                  DateTime? equbStartDate = widget.equb!.startDate != null
                      ? DateTime.tryParse(widget.equb!.startDate!)
                      : null;

                  if (equbStartDate != null &&
                      equbStartDate.isAfter(currentDate)) {
                    String formattedDate = DateFormat('MMMM dd, yyyy', 'en_US')
                        .format(equbStartDate);
                    Text('Start Date: $formattedDate');
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title:
                              Text(language.translate("payment_not_allowed")),
                          content: Text(
                              "${language.translate("payment_not_allowed_msg")} $formattedDate"),
                          actions: [
                            ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: themeProvider.getLightColor),
                              child: Text(language.translate("okay")),
                            ),
                          ],
                        );
                      },
                    );
                  } else {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Text(language.translate("confirm_payment")),
                          content:
                              Text(language.translate("confirm_payment_msg")),
                          actions: [
                            ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey,
                              ),
                              child: Text(language.translate("cancel")),
                            ),
                            ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);

                                if (user.role == "member") {
                                  // Call the JavaScript payment function directly
                                  JSPaymentExecutor.executePayment(
                                      context: context,
                                      title: 'Virtual Equb Payment',
                                      amount: amountControl.text,
                                      member: widget.member!,
                                      equbId: widget.equb!.id.toString(),
                                      index: widget.index,
                                      role: widget.role,
                                      equb: widget.equb,
                                      remainingAmount: widget.remainingAmount);
                                } else {
                                  setState(() {
                                    _onProcess = true;
                                  });
                                  _savePaymentAdmin(
                                      widget.member?.id.toString() ?? "0",
                                      widget.equb!.id.toString(),
                                      selectedType,
                                      amountControl.text,
                                      credit);
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: themeProvider.getLightColor,
                              ),
                              child: Text(language.translate("confirm")),
                            ),
                          ],
                        );
                      },
                    );
                  }
                }
              }
            },
      child: Container(
        // margin: const EdgeInsets.only(top: 5),
        alignment: Alignment.center,
        height: 50,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: _onProcess ||
                  widget.member!.status != "Active" ||
                  widget.equb!.status == "Deactive"
              ? themeProvider.getColor.withOpacity(0.5)
              : themeProvider.getColor.withOpacity(0.9),
        ),
        child: _onProcess
            ? const CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              )
            : widget.member!.status == "Active"
                ? widget.equb!.status == "Deactive"
                    ? Text(" ${language.translate("inactive_ekub")} ",
                        style: TextStyle(
                          overflow: TextOverflow.ellipsis,
                          fontWeight: boldFont,
                          fontSize: fontBig,
                          color: whiteText,
                        ))
                    : Text(
                        " ${language.translate("proceed")} ",
                        style: TextStyle(
                          overflow: TextOverflow.ellipsis,
                          fontWeight: boldFont,
                          fontSize: fontMedium,
                          color: whiteText,
                        ),
                      )
                : Text(" ${language.translate("inactive_member")} ",
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      fontWeight: boldFont,
                      fontSize: fontMedium,
                      color: whiteText,
                    )),
      ),
    );
  }

  Widget _buildListItems(
      BuildContext context, Payment payment, int item, theme) {
    if (payment.status == "pending") {
      return Container(); // Skip rendering for pending payments
    }
    return GestureDetector(
      onTap: () {},
      child: _listUi(theme, payment),
    );
  }

  _listUi(Color theme, Payment payment) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, right: 8.0),
      child: payment.status == "unpaid" || payment.status == "void"
          ? Container()
          : PaymentCard(
              status: payment.status ?? "loading...",
              amount: payment.amount ?? "0",
              icon: Icons.payments_outlined,
              paymentType: payment.paymentType ?? "loading...",
              updatedAt: payment.updatedAt ?? "loading...",
              createdAt: payment.createdAt ?? "loading...",
              balance: payment.balance ?? "0",
              credit: payment.credit ?? "0",
              collector: payment.collector ?? "loading...",
              theme: themeProvider,
              onTap: () {
                debugPrint("payment.id: ${payment.id}");
                if (payment.status == "pending" &&
                    payment.paymentType == "CBE Gateway") {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text('Payment Action'),
                        content: Text(
                            'What would you like to do with this payment?'),
                        actions: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _retryPayment(payment.id.toString());
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: themeProvider.getLightColor,
                                ),
                                child: Text('Retry'),
                              ),
                              SizedBox(width: 10),
                              ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  PaymentResolutionScreen.cancelPayment(
                                    context,
                                    payment.id.toString(),
                                    loadPayments, // Pass the loadPayments function
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                ),
                                child: Text('Cancel'),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  );
                }
              },
            ),
    );
  }

  _savePaymentAdmin(String memberId, String equbId, String type, String amount,
      String credit) {
    var sender = MemberProvider(httpClient: http.Client());
    var res = sender.savePaymentAdmin(
        context, type, amount, credit, equbId, memberId, _image);
    res
        .then((value) => {
              setState(() {
                _onProcess = false;
              }),
              if (value["code"] == 200)
                {
                  _removeImage(),
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text(
                            EkubLocalization.of(context)!.translate("success")),
                        content: Text(value["message"]),
                        actions: [
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: themeProvider.getLightColor,
                            ),
                            child: Text(EkubLocalization.of(context)!
                                .translate("okay")),
                          ),
                        ],
                      );
                    },
                  ),
                }
              else
                {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text(
                            EkubLocalization.of(context)!.translate("error")),
                        content: Text(value["message"]),
                        actions: [
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: themeProvider.getLightColor,
                            ),
                            child: Text(EkubLocalization.of(context)!
                                .translate("okay")),
                          ),
                        ],
                      );
                    },
                  ),
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Text(error.toString()),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child:
                      Text(EkubLocalization.of(context)!.translate("cancel")),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _savePaymentAdmin(memberId, equbId, type, amount, credit);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeProvider.getLightColor,
                  ),
                  child: Text(
                      EkubLocalization.of(context)!.translate("try_again")),
                ),
              ],
            );
          },
        );
        setState(() {
          _onProcess = false;
        });
      }
      return {};
    });
  }

  _payWithTelebirr(
      String memberId, String equbId, String amount, String credit) {
    setState(() {
      _onProcess = true;
    });
    var sender = MemberProvider(httpClient: http.Client());
    var res = sender.payWithTelebirr(context, memberId, amount, equbId);
    res.then((value) async {
      setState(() {
        _onProcess = false;
      });
      if (value["code"] == 200) {
        TelePack teleBirr = TelePack.fromJson(value["data"]);
        TelebirrPayment.instance.configure(
          publicKey: teleBirr.publicKey ?? "",
          appId: teleBirr.appId ?? "",
          appKey: teleBirr.appKey ?? "",
          notifyUrl: teleBirr.notifyUrl ?? "",
          shortCode: teleBirr.shortCode ?? "",
          merchantDisplayName: teleBirr.receiverName ?? "",
          // mode: Mode.test,
          // testUrl: 'http://<IP>:<PORT>/service-openup',
        );

        final res = await TelebirrPayment.instance.startPayment(
          itemName: teleBirr.subject ?? "",
          totalAmount: teleBirr.totalAmount ?? "1",
        );

        var toPayUrl = '';
        if (res != null && res.isSuccess) {
          toPayUrl = res.data?.toPayUrl ?? '';
          openSocialMedia(toPayUrl);
          // Navigator.push(
          //     context,
          //     MaterialPageRoute(
          //         builder: (context) => PaymentWithChapa(
          //               args: {"checkout_url":teleBirr.inAppPaymentUrl,"amount":amount,},
          //               index: 0,
          //               equb: widget.equb,
          //               member: widget.member,
          //             )));
        }
      } else {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(EkubLocalization.of(context)!.translate("error")),
              content: Text(value["message"]),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeProvider.getLightColor,
                  ),
                  child: Text(EkubLocalization.of(context)!.translate("okay")),
                ),
              ],
            );
          },
        );
        setState(() {
          _onProcess = false;
        });
      }
    }).onError((error, stackTrace) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Text(error.toString()),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child:
                      Text(EkubLocalization.of(context)!.translate("cancel")),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _payWithTelebirr(memberId, equbId, amount, credit);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeProvider.getLightColor,
                  ),
                  child: Text(
                      EkubLocalization.of(context)!.translate("try_again")),
                ),
              ],
            );
          },
        );
        setState(() {
          _onProcess = false;
        });
      }
      // return {};
    });
  }

  _savePayment(String memberId, String equbId, String type, String amount,
      String credit) {
    var sender = MemberProvider(httpClient: http.Client());
    var res = sender.savePayment(
        context, type, amount, credit, equbId, memberId, _image);
    res
        .then((value) => {
              setState(() {
                _onProcess = false;
              }),
              if (value["code"] == 200)
                {
                  _removeImage(),
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text(
                            EkubLocalization.of(context)!.translate("success")),
                        content: Text(value["message"]),
                        actions: [
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: themeProvider.getLightColor,
                            ),
                            child: Text(EkubLocalization.of(context)!
                                .translate("okay")),
                          ),
                        ],
                      );
                    },
                  ),
                }
              else
                {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text(
                            EkubLocalization.of(context)!.translate("error")),
                        content: Text(value["message"]),
                        actions: [
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: themeProvider.getLightColor,
                            ),
                            child: Text(EkubLocalization.of(context)!
                                .translate("okay")),
                          ),
                        ],
                      );
                    },
                  ),
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Text(error.toString()),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child:
                      Text(EkubLocalization.of(context)!.translate("cancel")),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _savePayment(memberId, equbId, type, amount, credit);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeProvider.getLightColor,
                  ),
                  child: Text(
                      EkubLocalization.of(context)!.translate("try_again")),
                ),
              ],
            );
          },
        );
        setState(() {
          _onProcess = false;
        });
      }
      return {};
    });
  }

  Map<String, dynamic> arguments = {};
  Column makePayment(BuildContext context, double totalPaidAmount,
      double totalExpectedAmount) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 10.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text(
                  EkubLocalization.of(context)!.translate("amount"),
                  style: lableStyle,
                ),
              ),
              const SizedBox(
                height: 3,
              ),
              TextFormField(
                controller: amountControl,
                keyboardType: const TextInputType.numberWithOptions(
                    signed: true, decimal: true),
                style: TextStyle(
                    overflow: TextOverflow.ellipsis,
                    fontWeight: boldFont,
                    fontSize: fontMedium,
                    color: bodyTextColor),
                decoration: InputDecoration(
                    enabled: true,
                    prefix: Text(
                      "${EkubLocalization.of(context)!.translate('etb')} ",
                      style: TextStyle(
                          fontWeight: boldFont,
                          fontSize: fontMedium,
                          color: bodyTextColor),
                    ),
                    border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(color: themeProvider.getColor),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderSide: const BorderSide(
                        color: Colors.red,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                    fillColor: Colors.white,
                    filled: true,
                    labelStyle: lableStyle),
                onChanged: (value) {
                  var enteredAmount =
                      amountControl.text.isEmpty ? "0" : amountControl.text;
                  var res = double.parse(amount) - double.parse(enteredAmount);
                  credit = res.toString();
                },
                validator: (value) => Sanitizer().isValidField(value!,
                    EkubLocalization.of(context)!.translate("amount"), context),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Future _getGalleryImage() async {
  //   try {
  //     final image = await ImagePicker().pickImage(source: ImageSource.gallery);
  //     if (image != null) {
  //       final imageFile = File(image.path);

  //       final appDir = await getApplicationDocumentsDirectory();
  //       final targetPath = '${appDir.path}/profile-${DateTime.now()}.jpg';

  //       XFile compressedFile = await compressAndGetFile(imageFile, targetPath);
  //       _image = File(compressedFile.path);
  //       setState(() {});
  //     }
  //   } catch (e) {}
  // }

  void _removeImage() {
    try {
      setState(() {
        _image = null;
      });
    } catch (e) {}
  }

  final formatCurrency = NumberFormat("#,##0.00", "en_US");

  void _retryPayment(String paymentId) async {
    final retryUrl = await PaymentResolutionScreen.retryPayment(paymentId);
    if (retryUrl != null && context.mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewScreen(url: retryUrl),
        ),
      );
    } else {
      // Handle error or show a message to the user
      debugPrint('Failed to retrieve retry URL');
    }
  }
}
