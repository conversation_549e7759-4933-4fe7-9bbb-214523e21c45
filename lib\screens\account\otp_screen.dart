// ignore_for_file: use_build_context_synchronously, must_be_immutable, library_private_types_in_public_api
import 'dart:async';

import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/account/register_screen.dart';
import 'package:ekub/screens/account/reset_password.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';

import '../../utils/colors.dart';

class OtpScreen extends StatefulWidget {
  String phoneNumber;
  String status;
  int userId;
  OtpScreen(
      {super.key,
      required this.phoneNumber,
      required this.status,
      required this.userId});
  @override
  _OtpScreenState createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  List<String> _otpDigits = List.generate(6, (_) => '');
  String code = "";
  bool resending = false;
  bool isFirstTime = true;
  bool resendCode = false;
  bool _onProcess = false;
  bool isOffline = false;
  bool isSubmitted = false;

  String verificationID = "";
  int resendToken = 0;
  int currentSeconds = 0;
  final int timerMaxSeconds = 120;
  int _animatedIndex = -1;
  int selectedIndex = -1;
  final interval = const Duration(seconds: 1);
  String get timerText =>
      '${((timerMaxSeconds - currentSeconds) ~/ 60).toString().padLeft(2, '0')}: ${((timerMaxSeconds - currentSeconds) % 60).toString().padLeft(2, '0')}';
  startTimeout([milliseconds]) {
    setState(() {
      resendCode = false;
    });
    var duration = interval;
    Timer.periodic(duration, (timer) {
      if (mounted) {
        setState(() {
          currentSeconds = timer.tick;
          if (timer.tick >= timerMaxSeconds) {
            timer.cancel();
            setState(() {
              resendCode = true;
            });
          }
        });
      }
    });
  }

  void _addNumberToOtp(String number) {
    if (_otpDigits.any((digit) => digit.isEmpty)) {
      setState(() {
        int emptyIndex = _otpDigits.indexWhere((digit) => digit.isEmpty);
        _otpDigits[emptyIndex] = number;
        if (number == "0") {
          selectedIndex = 0;
        } else {
          _animatedIndex = int.parse(number) - 1;
        }
      });
      Future.delayed(const Duration(milliseconds: 100), () {
        setState(() {
          _animatedIndex = -1;
          selectedIndex = -1;
        });
      });
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_otpDigits[5] != "") {
          code = _otpDigits[0] +
              _otpDigits[1] +
              _otpDigits[2] +
              _otpDigits[3] +
              _otpDigits[4] +
              _otpDigits[5];
        }
      });
    }
  }

  void _removeLastNumber() {
    setState(() {
      int lastIndex = _otpDigits.lastIndexWhere((digit) => digit.isNotEmpty);
      if (lastIndex >= 0) {
        _otpDigits[lastIndex] = '';
        selectedIndex = 1;
      }
    });
    Future.delayed(const Duration(milliseconds: 100), () {
      setState(() {
        selectedIndex = -1;
      });
    });
  }

  void _clearPassword() {
    setState(() {
      _otpDigits = List.generate(6, (_) => '');
      selectedIndex = 3;
    });
    Future.delayed(const Duration(milliseconds: 100), () {
      setState(() {
        selectedIndex = -1;
      });
    });
  }

  void sendOtp(BuildContext context, String phone) {
    var sender = AuthDataProvider(httpClient: http.Client());
    startTimeout();
    var res = sender.sendOtp(context, phone);
    res
        .then((value) => {
              if (value["acknowledge"] == "success")
                {
                  setState(() {
                    _onProcess = false;
                    resending = false;
                  }),
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value["message"][0],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                    resending = false;
                  }),
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          sendOtp(context, phone);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        _onProcess = false;
        resending = false;
      });
      return {};
    });
  }

  verifyCode() async {
    setState(() {
      isFirstTime = true;
    });
    if (!await InternetConnectivity().checkInternetConnectivty(context, true)) {
      return;
    } else {}
  }

  @override
  void initState() {
    super.initState();
    startTimeout();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Container(
              width: 45,
              height: 45,
              alignment: Alignment.center,
              child: const Icon(
                Icons.arrow_back_ios,
                color: Colors.black,
              )),
        ),
        elevation: 0,
      ),
      body: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: ListView(
            children: [
              SizedBox(
                height: MediaQuery.of(context).size.height - 80,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 0,
                    ),
                    Text(
                      timerText,
                      style: const TextStyle(
                          fontSize: 25, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16.0),
                    Text(
                      EkubLocalization.of(context)!.translate("otp_title"),
                      style: const TextStyle(
                        fontSize: fontBig,
                        fontWeight: boldFont,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      "${EkubLocalization.of(context)!.translate("otp_message")}\n +251${widget.phoneNumber}.",
                      style: TextStyle(
                        fontSize: fontSmall,
                        fontWeight: normalFontWeight,
                        color: bodyTextColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    const SizedBox(height: 32.0),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        for (int i = 0; i < _otpDigits.length; i++)
                          Row(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 45,
                                width: 45,
                                decoration: BoxDecoration(
                                    color: _otpDigits[i] == ""
                                        ? Colors.white
                                        : ColorProvider.primary,
                                    borderRadius: BorderRadius.circular(10),
                                    border: _otpDigits[i] == ""
                                        ? Border.all(
                                            color: ColorProvider.primary,
                                            width: 2)
                                        : null),
                                child: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 250),
                                  transitionBuilder: (Widget child,
                                      Animation<double> animation) {
                                    return ScaleTransition(
                                        scale: animation, child: child);
                                  },
                                  child: Text(
                                    _otpDigits[i] == "" ? "-" : _otpDigits[i],
                                    key: ValueKey<String>(_otpDigits[i]),
                                    style: TextStyle(
                                      color: _otpDigits[i] == ""
                                          ? Colors.grey
                                          : Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 25,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                            ],
                          ),
                      ],
                    ),
                    const SizedBox(height: 50.0),
                    Expanded(
                      child: Wrap(
                        alignment: WrapAlignment.center,
                        runSpacing: 0.0,
                        children: [
                          Padding(
                            padding: const EdgeInsets.fromLTRB(
                                4.0, 16.0, 16.0, 10.0),
                            child: NotificationListener<
                                OverscrollIndicatorNotification>(
                              onNotification: (notification) {
                                notification.disallowIndicator();
                                return true;
                              },
                              child: SingleChildScrollView(
                                physics: const NeverScrollableScrollPhysics(),
                                child: GridView.count(
                                  shrinkWrap: true,
                                  crossAxisCount: 3,
                                  padding: EdgeInsets.zero,
                                  mainAxisSpacing: 4.0,
                                  crossAxisSpacing: 4.0,
                                  childAspectRatio: 2,
                                  children: List.generate(
                                    9,
                                    (index) => GestureDetector(
                                      onTap: () {
                                        _addNumberToOtp((index + 1).toString());
                                      },
                                      child: AnimatedContainer(
                                        duration:
                                            const Duration(milliseconds: 500),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(50.0),
                                          color: _animatedIndex == index
                                              ? Colors.grey
                                              : Colors.white,
                                        ),
                                        child: Center(
                                          child: Text(
                                            (index + 1).toString(),
                                            style: const TextStyle(
                                              fontSize: 24.0,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.fromLTRB(4.0, 0, 16.0, 16.0),
                            child: NotificationListener<
                                OverscrollIndicatorNotification>(
                              onNotification: (notification) {
                                notification.disallowIndicator();
                                return true;
                              },
                              child: GridView.count(
                                shrinkWrap: true,
                                crossAxisCount: 3,
                                padding: EdgeInsets.zero,
                                mainAxisSpacing: 4.0,
                                crossAxisSpacing: 4.0,
                                childAspectRatio: 2,
                                children: [
                                  GestureDetector(
                                    onTap: _removeLastNumber,
                                    child: AnimatedContainer(
                                        duration:
                                            const Duration(milliseconds: 500),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(50.0),
                                          color: selectedIndex == 1
                                              ? Colors.grey
                                              : Colors.white,
                                        ),
                                        child: const Icon(
                                          Icons.backspace,
                                          color: Colors.black,
                                        )),
                                  ),
                                  GestureDetector(
                                    onTap: () => _addNumberToOtp('0'),
                                    child: AnimatedContainer(
                                      duration:
                                          const Duration(milliseconds: 500),
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(50.0),
                                        color: selectedIndex == 0
                                            ? Colors.grey
                                            : Colors.white,
                                      ),
                                      child: const Center(
                                        child: Text(
                                          '0',
                                          style: TextStyle(
                                            fontSize: 24.0,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: _clearPassword,
                                    child: AnimatedContainer(
                                        duration:
                                            const Duration(milliseconds: 500),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(50.0),
                                          color: selectedIndex == 3
                                              ? Colors.grey
                                              : Colors.white,
                                        ),
                                        child: const Icon(Icons.clear,
                                            color: Colors.black)),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: resending
                          ? null
                          : () async {
                              // final _form = _confirmFormKey.currentState;
                              setState(() {
                                isSubmitted = true;

                                _onProcess = true;
                              });
                              if (!await InternetConnectivity()
                                  .checkInternetConnectivty(context, true)) {
                                setState(() {
                                  _onProcess = false;
                                });
                                return;
                              } else {
                                if (code != '') {
                                  verifyOtp(context, widget.phoneNumber, code);
                                } else {
                                  PanaraInfoDialog.show(
                                    context,
                                    title: EkubLocalization.of(context)!
                                        .translate("login_failed"),
                                    message: EkubLocalization.of(context)!
                                        .translate("invalid_otp_message"),
                                    buttonText: EkubLocalization.of(context)!
                                        .translate("okay"),
                                    imagePath: errorDialogIcon,
                                    onTapDismiss: () {
                                      Navigator.pop(context);
                                    },
                                    panaraDialogType: PanaraDialogType.error,
                                  );
                                  setState(() {
                                    _onProcess = false;
                                  });
                                }
                              }
                              // }
                            },
                      child: Container(
                        width: double.infinity,
                        height: 55,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: !resending
                                ? ColorProvider.primary
                                : ColorProvider.primary.withOpacity(0.5)),
                        padding: const EdgeInsets.all(14.0),
                        child: resending
                            ? Center(
                                child: Text(
                                    '${EkubLocalization.of(context)!.translate("resending_code")}...',
                                    style: buttonText),
                              )
                            : Center(
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Spacer(),
                                      Text(
                                          EkubLocalization.of(context)!
                                              .translate("verify"),
                                          style: buttonText),
                                      const Spacer(),
                                      Align(
                                        widthFactor: 2,
                                        alignment: Alignment.centerRight,
                                        child: _onProcess
                                            ? const Padding(
                                                padding: EdgeInsets.all(8.0),
                                                child: SizedBox(
                                                  height: 15,
                                                  width: 15,
                                                  child:
                                                      CircularProgressIndicator(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              )
                                            : Container(),
                                      ),
                                    ]),
                              ),
                      ),
                    ),
                    TextButton(
                        onPressed: resendCode
                            ? () {
                                setState(() {
                                  resending = true;
                                });
                                sendOtp(context, widget.phoneNumber);
                                startTimeout();
                              }
                            : null,
                        child: Text(
                          EkubLocalization.of(context)!.translate("resend"),
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: !resendCode
                                  ? Colors.grey.shade500
                                  : Colors.black),
                        )),
                    const SizedBox(
                      height: 18,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void verifyOtp(BuildContext context, String phone, String code) {
    var sender = AuthDataProvider(httpClient: http.Client());
    setState(() {
      _onProcess = true;
    });

    var res = sender.verifyOtp(context, phone, code);
    res
        .then((value) => {
              if (value["acknowledge"] == "success")
                {
                  if (widget.status == "newUser")
                    {
                      Navigator.pushReplacementNamed(
                          context, RegisterScreen.routeName,
                          arguments: RegisterScreenArgs(
                              isOnline: true, phoneNumber: widget.phoneNumber)),
                      setState(() {
                        _onProcess = false;
                        resending = false;
                      }),
                    }
                  else if (widget.status == "forgotPassword")
                    {
                      Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                              builder: (context) => ResetPassword(
                                  userId: widget.userId,
                                  args: ResetPasswordArgs(
                                    otp: code,
                                    isOnline: true,
                                  )))),
                      setState(() {
                        _onProcess = false;
                        resending = false;
                      }),
                    },
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("error"),
                    message: value["message"][0],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                    resending = false;
                  }),
                },
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            // title: "Request Timeout",
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          verifyOtp(context, phone, code);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
        setState(() {
          _onProcess = false;
        });
      }
      return {};
    });
  }
}
