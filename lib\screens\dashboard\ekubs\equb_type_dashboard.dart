// ignore_for_file: empty_catches, must_be_immutable, use_build_context_synchronously

import 'dart:async';

import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/equb_repos.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/screens/ui_kits/no_internet_connection_found.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../../exports/models.dart';
import '../../../repository/user_repos.dart';
import '../../../utils/colors.dart';
import '../../../utils/constants.dart';
import '../../themes/ThemeProvider.dart';
import '../../ui_kits/app_bar.dart';

class EqubTypeDashboard extends StatefulWidget {
  EqubType? equbType;
  EqubTypeDashboard({super.key, required this.equbType});

  @override
  State<EqubTypeDashboard> createState() => _EqubTypeDashboardState();
}

class _EqubTypeDashboardState extends State<EqubTypeDashboard> {
  var proLoaded = false;
  bool isConnected = true;
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      gender: "loading",
      email: "loading",
      role: "loading",
      memberId: "loading");
  late ThemeProvider themeProvider;
  final _appBar = GlobalKey<FormState>();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _loadProfile();
    loadData();
    super.initState();
  }

  _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());

    try {
      await auth.getUserData().then((value) => {
            setState(() {
              user = value;
              proLoaded = true;
            })
          });
    } catch (e) {}
  }

  loadData() async {
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        setState(() {
          isConnected = false;
        });
        return;
      }

      Provider.of<EqubDataProvider>(context, listen: false)
          .fetchDashboard(context, widget.equbType!.id.toString());
      setState(() {
        isConnected = true;
      });
    } catch (e) {
      if (e is TimeoutException && mounted) {
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("time_out"),
            message: EkubLocalization.of(context)!.translate("timeout_message"),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          loadData();

          Navigator.pop(context);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("error"),
              message:
                  EkubLocalization.of(context)!.translate("error_load_data"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            loadData();
            Navigator.pop(context);
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // var appLanguage = Provider.of<AppLanguage>(context);

    final dashboard =
        Provider.of<EqubDataProvider>(context, listen: true).dashboard;
    // final members = dashboard.tudayPaidMember?.members ?? [];
    final language = EkubLocalization.of(context)!;
    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: EkubAppBar(key: _appBar, title: widget.equbType!.name!, widgets: [
        IconButton(
            onPressed: () {
              loadData();
            },
            icon: const Icon(Icons.refresh)),
      ]),
      body: isConnected
          ? RefreshIndicator(
              onRefresh: () async {
                await Future.delayed(const Duration(seconds: 2));

                loadData();
              },
              key: _refreshIndicatorKey,
              child: SizedBox(
                height: MediaQuery.of(context).size.height,
                child: SingleChildScrollView(
                  child: Container(
                    padding: const EdgeInsets.all(defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: defaultPadding),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 20.0,
                          ),
                          decoration: BoxDecoration(
                            color: themeProvider.getColor,
                            borderRadius:
                                const BorderRadius.all(Radius.circular(20)),
                          ),
                          /*child: Chart(height: 200)*/
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Column(
                                children: [
                                  Text(
                                    language.translate("collected"),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        overflow: TextOverflow.clip,
                                        color: themeProvider.getLightColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 20.0),
                                  ),
                                  Text(
                                    language.translate("today"),
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                        overflow: TextOverflow.clip,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 30.0),
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    '${formatCurrency.format(dashboard.daylyPaidAmount)} ETB',
                                    style: const TextStyle(
                                        overflow: TextOverflow.clip,
                                        color: Colors.white,
                                        fontSize: 18.0),
                                  )
                                ],
                              ),
                              const SizedBox(
                                width: 50,
                              ),
                              CircularPercentIndicator(
                                radius: 50.0,
                                lineWidth: 10.0,
                                animation: true,
                                animationDuration: 2500,
                                percent: _inDecimal(
                                    dashboard.daylyPaidAmount ?? 0,
                                    double.parse(
                                        dashboard.daylyExpected ?? "0")),
                                center: Text(
                                  _inPercent(
                                      dashboard.daylyPaidAmount ?? 0,
                                      double.parse(
                                          dashboard.daylyExpected ?? "0")),
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: themeProvider.getLightColor,
                                      fontSize: 20.0),
                                ),
                                circularStrokeCap: CircularStrokeCap.butt,
                                progressColor: _inDecimal(
                                            dashboard.daylyPaidAmount ?? 0,
                                            double.parse(
                                                dashboard.daylyExpected ??
                                                    "0")) !=
                                        1
                                    ? themeProvider.getLightColor
                                        .withOpacity(0.5)
                                    : themeProvider.getLightColor,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Text(
                            "${language.translate("equbs")} ${language.translate("status")}",
                            style: const TextStyle(
                                fontSize: fontBig, fontWeight: FontWeight.bold),
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        SizedBox(
                          height: 700,
                          child: ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: 4,
                              itemBuilder: (context, index) {
                                return equbProgress(index, dashboard);
                              }),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            )
          : NoConnectionWidget(
              fun: loadData,
              isLoading: false,
            ),
    );
  }

  Container equbProgress(int index, Dashboard dashboard) {
    List all = [
      {
        "svgSrc": "assets/icons/Documents.svg",
        "title": EkubLocalization.of(context)!.translate("daily"),
        "collected": dashboard.daylyPaidAmount ?? 0,
        "expected": double.parse(dashboard.daylyExpected ?? "0"),
        "icon": Icons.person_pin,
      },
      {
        "svgSrc": "assets/icons/Documents.svg",
        "title": EkubLocalization.of(context)!.translate("weekly"),
        "collected": double.parse(dashboard.weeklyPaidAmount ?? "0"),
        "expected": dashboard.weeklyExpected ?? 0,
        "icon": Icons.person_pin,
      },
      {
        "svgSrc": "assets/icons/Documents.svg",
        "title": EkubLocalization.of(context)!.translate("monthly"),
        "collected": dashboard.monthlyPaidAmount ?? 0,
        "expected": dashboard.monthlyExpected ?? 0,
        "icon": Icons.person_pin,
      },
      {
        "svgSrc": "assets/icons/Documents.svg",
        "title": EkubLocalization.of(context)!.translate("yearly"),
        "collected": dashboard.yearlyPaidAmount ?? 0,
        "expected": dashboard.yearlyExpected ?? 0,
        "icon": Icons.person_pin,
      }
    ];

    return Container(
      height: 145,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
      margin: const EdgeInsets.only(bottom: 10, left: 5, right: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5), //color of shadow
            spreadRadius: .1, //spread radius
            blurRadius: 2, // blur radius
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            all[index]["title"],
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 5,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    EkubLocalization.of(context)!.translate("collected"),
                    style: TextStyle(
                        color: Colors.grey.shade400, fontSize: fontSmall),
                  ),
                  Text(
                    '${formatCurrency.format(all[index]["collected"])} ETB',
                    style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: fontMedium),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(EkubLocalization.of(context)!.translate("expected"),
                      style: TextStyle(
                          color: Colors.grey.shade400, fontSize: fontSmall)),
                  Text('${formatCurrency.format(all[index]["expected"])} ETB',
                      style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: fontMedium))
                ],
              ),
            ],
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                _inPercent(all[index]["collected"] ?? 0,
                    double.parse(all[index]["expected"].toString())),
                style:
                    TextStyle(color: Colors.grey.shade400, fontSize: fontSmall),
              ),
            ],
          ),
          LinearPercentIndicator(
              // width: MediaQuery.of(context).size.width,
              alignment: MainAxisAlignment.start,
              animation: true,
              lineHeight: 5.0,
              animationDuration: 2500,
              percent: _inDecimal(all[index]["collected"] ?? 0,
                  double.parse(all[index]["expected"].toString())),
              backgroundColor: _inDecimal(all[index]["collected"] ?? 0,
                          double.parse(all[index]["expected"].toString())) !=
                      1
                  ? Colors.grey.shade300
                  : themeProvider.getLightColor.withOpacity(0.4),
              barRadius: const Radius.circular(3),
              progressColor: themeProvider.getLightColor),
        ],
      ),
    );
  }

  _inDecimal(double collected, double expected) {
    var dec = collected / expected;
    if (dec > 1) {
      dec = 1;
    }
    if (dec.isNaN) {
      return 0.toDouble();
    }

    return dec;
  }

  String _inPercent(double collected, double expected) {
    double val = _inDecimal(collected, expected) * 100;
    String p = "%";
    String res = val.toString();
    String result = res.split(".")[0];
    return result + p;
  }

  final formatCurrency = NumberFormat("#,##0.00", "en_US");
}
