// ignore_for_file: unused_field, must_be_immutable

import 'dart:async';

import 'package:ekub/exports/screens.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

import '../../utils/network.dart';
import '../../utils/validator.dart';
import '../themes/ThemeProvider.dart';

class RegisterScreen extends StatefulWidget {
  static const routeName = "/register";

  RegisterScreen({super.key, required this.args});
  RegisterScreenArgs args;

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  bool isSubmitted = false;
  bool isOffline = false;
  var invisible = false;
  bool _onProcess = false;
  String _selectedGender = "Male";

  late String id;
  final Map<String, dynamic> _doctor = {};
  late ThemeProvider themeProvider;
  late StreamSubscription _connectionChangeStream;
  final _registerFormKey = GlobalKey<FormState>();
  final userNameControl = TextEditingController();
  final emailControl = TextEditingController();
  final woredaControl = TextEditingController();
  final houseControl = TextEditingController();
  final locationControl = TextEditingController();
  final passwordControl = TextEditingController();
  final password2Control = TextEditingController();
  final phoneControl = TextEditingController();
  bool _isAbove18 = false;
  final TextEditingController _ageController = TextEditingController();
  bool _isAgeFieldActive = false;

  DateTime? _selectedDateOfBirth;

  // Function to show the date picker and select date of birth
  void _selectDateOfBirth(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDateOfBirth ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(
              primary: themeProvider.getColor, // Header background color
              onPrimary: Colors.white, // Header text color
              onSurface: Colors.black, // Calendar text color
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: themeProvider.getColor, // Button text color
              ),
            ),
          ),
          child: child!,
        );
      },
      selectableDayPredicate: (DateTime date) {
        // Highlight the selected date
        if (_selectedDateOfBirth != null &&
            date.year == _selectedDateOfBirth!.year &&
            date.month == _selectedDateOfBirth!.month &&
            date.day == _selectedDateOfBirth!.day) {
          return true;
        }
        return true; // All other dates are selectable
      },
    );

    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
        _ageController.text =
            "${_selectedDateOfBirth!.day}/${_selectedDateOfBirth!.month}/${_selectedDateOfBirth!.year}";
        _isAbove18 = _calculateAge(_selectedDateOfBirth!) >=
            18; // Check if the user is above 18
      });
    }
  }

  // Helper function to calculate age based on date of birth
  int _calculateAge(DateTime birthDate) {
    DateTime currentDate = DateTime.now();
    int age = currentDate.year - birthDate.year;
    int month1 = currentDate.month;
    int month2 = birthDate.month;
    if (month2 > month1 ||
        (month1 == month2 && currentDate.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  @override
  void initState() {
    _onProcess = false;
    NetworkManager connectionStatus = NetworkManager.getInstance();
    _connectionChangeStream =
        connectionStatus.connectionChange.listen(connectionChanged);
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _ageController.addListener(_onAgeFieldChanged);
    super.initState();
  }

  void _onAgeFieldChanged() {
    setState(() {
      _isAgeFieldActive = _ageController.text.isNotEmpty;
    });
  }

  @override
  void dispose() {
    _ageController.removeListener(_onAgeFieldChanged);
    _ageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorProvider.backgroundColor,
        appBar: AppBar(
          iconTheme: IconThemeData(color: themeProvider.getColor),
          backgroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
        ),
        body: Form(
            key: _registerFormKey,
            autovalidateMode: isSubmitted
                ? AutovalidateMode.onUserInteraction
                : AutovalidateMode.disabled,
            child: Stack(
              children: <Widget>[
                Align(
                  alignment: Alignment.center,
                  child: ListView(
                    children: <Widget>[
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(25)),
                        margin: const EdgeInsets.fromLTRB(25, 30, 25, 10),
                        padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                        child: Column(
                          children: <Widget>[
                            const SizedBox(
                              height: 15,
                            ),
                            Text(
                              EkubLocalization.of(context)!
                                  .translate("registration"),
                              style: const TextStyle(
                                fontSize: fontBig,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(
                              height: 15,
                            ),
                            _nameTextField(),
                            const SizedBox(
                              height: 15,
                            ),
                            _emailField(),
                            const SizedBox(
                              height: 15,
                            ),
                            const SizedBox(
                              height: 15,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(18),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 5),
                              child: Row(
                                children: [
                                  Radio<String>(
                                    activeColor: themeProvider.getColor,
                                    value: 'Male',
                                    groupValue: _selectedGender,
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedGender = value!;
                                      });
                                    },
                                  ),
                                  Text(
                                      EkubLocalization.of(context)
                                              ?.translate("male") ??
                                          "Male",
                                      style: hintStyle),
                                  Radio<String>(
                                    value: 'Female',
                                    activeColor: themeProvider.getColor,
                                    groupValue: _selectedGender,
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedGender = value!;
                                      });
                                    },
                                  ),
                                  Text(
                                    EkubLocalization.of(context)
                                            ?.translate("female") ??
                                        "Female",
                                    style: hintStyle,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 15,
                            ),
                            _ageField(),
                            const SizedBox(
                              height: 15,
                            ),
                            _passwordFileld(),
                            const SizedBox(
                              height: 15,
                            ),
                            _confirmPasswordField(),
                            const SizedBox(
                              height: 40,
                            ),
                            ElevatedButton(
                              onPressed: _onProcess
                                  ? null
                                  : () async {
                                      final form =
                                          _registerFormKey.currentState;
                                      setState(() {
                                        isSubmitted = true;
                                      });
                                      if (form!.validate()) {
                                        setState(() {
                                          _onProcess = true;
                                        });
                                        form.save();
                                        if (!await InternetConnectivity()
                                            .checkInternetConnectivty(
                                                context, true)) {
                                          setState(() {
                                            _onProcess = false;
                                          });
                                          return;
                                        } else {
                                          _registerNewMember(
                                            userNameControl.text,
                                            emailControl.text,
                                            _selectedGender,
                                            password2Control.text,
                                            _ageController
                                                .text, // This now contains the date of birth
                                          );
                                        }
                                      }
                                    },
                              child: SizedBox(
                                height: 50,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Spacer(),
                                    Text(
                                        EkubLocalization.of(context)!
                                            .translate("next"),
                                        style: buttonText),
                                    const Spacer(),
                                    Align(
                                      widthFactor: 2,
                                      alignment: Alignment.centerRight,
                                      child: _onProcess
                                          ? const Padding(
                                              padding: EdgeInsets.all(8.0),
                                              child: SizedBox(
                                                height: 20,
                                                width: 20,
                                                child:
                                                    CircularProgressIndicator(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            )
                                          : Container(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            )));
  }

  _confirmPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("confirm_password"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          style: lableStyle,
          controller: password2Control,
          obscureText: invisible,
          decoration: InputDecoration(
            prefixIcon: Container(
              margin: const EdgeInsets.symmetric(horizontal: 5),
              height: 25,
              width: 30,
              decoration: BoxDecoration(
                  color: themeProvider.getColor.withOpacity(0.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.lock_outlined,
                size: 20,
                color: themeProvider.getColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: BorderSide(color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: BorderSide(color: themeProvider.getColor),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: Colors.red,
              ),
              borderRadius: BorderRadius.circular(18),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            fillColor: Colors.white,
            filled: true,
            hintText: '******',
            hintStyle: hintStyle,
            suffix: GestureDetector(
              onTap: _onProcess ? null : changeSate,
              child: Icon(
                invisible ? Icons.visibility : Icons.visibility_off,
                color: themeProvider.getColor,
                size: 15,
              ),
            ),
          ),
          validator: (value) => Sanitizer()
              .isPasswordMatch(passwordControl.text, value!, context),
          onSaved: (value) {
            _doctor["password"] = value;
          },
        ),
      ],
    );
  }

  _passwordFileld() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("password"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          style: lableStyle,
          controller: passwordControl,
          obscureText: invisible,
          decoration: InputDecoration(
            prefixIcon: Container(
              margin: const EdgeInsets.symmetric(horizontal: 5),
              height: 25,
              width: 30,
              decoration: BoxDecoration(
                  color: themeProvider.getColor.withOpacity(0.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.lock_outlined,
                size: 20,
                color: themeProvider.getColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: BorderSide(color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: BorderSide(color: themeProvider.getColor),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: Colors.red,
              ),
              borderRadius: BorderRadius.circular(18),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            fillColor: Colors.white,
            filled: true,
            hintText: '******',
            hintStyle: hintStyle,
            suffix: GestureDetector(
              onTap: _onProcess ? null : changeSate,
              child: Icon(
                invisible ? Icons.visibility : Icons.visibility_off,
                color: themeProvider.getColor,
                size: 15,
              ),
            ),
          ),
          validator: (value) => Sanitizer().isPasswordValid(value!, context),
        ),
      ],
    );
  }

  _emailField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("email"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          style: lableStyle,
          controller: emailControl,
          keyboardType: TextInputType.emailAddress,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.email_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: Colors.white,
              filled: true,
              hintText: 'Enter your email address',
              hintStyle: hintStyle),
          validator: (value) => Sanitizer().isValidEmail(value!, context),
          onSaved: (value) {},
        ),
      ],
    );
  }

  _ageField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("date_of_birth"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(height: 3),
        GestureDetector(
          onTap: () {
            _selectDateOfBirth(context); // Open date picker when tapped
          },
          child: AbsorbPointer(
            child: TextFormField(
              controller: _ageController,
              decoration: InputDecoration(
                prefixIcon: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 5),
                  height: 25,
                  width: 30,
                  decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.calendar_today_outlined,
                    size: 20,
                    color: themeProvider.getColor,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(18),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(18),
                  borderSide: BorderSide(color: themeProvider.getColor),
                ),
                hintText: 'Select your date of birth',
                hintStyle: hintStyle,
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return EkubLocalization.of(context)!
                      .translate("please_select_date_of_birth");
                }
                if (!_isAbove18) {
                  return EkubLocalization.of(context)!
                      .translate("you_must_be_above_18");
                }
                return null;
              },
            ),
          ),
        ),
      ],
    );
  }

  _nameTextField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("full_name"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          style: lableStyle,
          controller: userNameControl,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.person_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: Colors.white,
              filled: true,
              hintText: 'Alemu Kassa',
              hintStyle: hintStyle),
          validator: (value) =>
              Sanitizer().isFullNameValid(value!, false, context),
        ),
      ],
    );
  }

  _registerNewMember(String name, String email, String gender, String password,
      String dateOfBirth) async {
    _doctor['phone_number'] = "+251${widget.args.phoneNumber}";

    // Convert the date of birth to the format YYYY-MM-DD
    DateTime dob = DateFormat('dd/MM/yyyy').parse(dateOfBirth);
    String formattedDateOfBirth = DateFormat('yyyy-MM-dd').format(dob);
    _doctor['date_of_birth'] = formattedDateOfBirth;

    var sender = MemberProvider(httpClient: http.Client());
    var send = AuthDataProvider(httpClient: http.Client());

    // Print the formatted date of birth
    print('Formatted Date of Birth: $formattedDateOfBirth');

    setState(() {
      _onProcess = true;
    });
    var res = sender.signupMember(
      context,
      name,
      "+251${widget.args.phoneNumber}",
      email,
      gender,
      _doctor,
      password,
    );

    res.then((res) => {
          if (res.code == "200")
            {
              send
                  .loginUser(_doctor, 0, context)
                  .then((value) => {
                        setState(() {
                          _onProcess = false;
                        }),
                        if (value.code == "200")
                          {
                            if (value.message == "admin")
                              {
                                _openHomeScreen(
                                    value.message == "admin", value.message)
                              }
                            else
                              {
                                _openHomeScreen(
                                    value.message == "admin", value.message)
                              }
                          }
                        else
                          {
                            PanaraInfoDialog.show(
                              context,
                              message: value.message,
                              buttonText: EkubLocalization.of(context)!
                                  .translate("okay"),
                              onTapDismiss: () {
                                Navigator.pop(context);
                              },
                              imagePath: errorDialogIcon,
                              panaraDialogType: PanaraDialogType.error,
                            ),
                            setState(() {
                              _onProcess = false;
                            }),
                            _openLoginScreen(),
                          }
                      })
                  .onError((error, stackTrace) {
                PanaraConfirmDialog.show(context,
                    message: error.toString(),
                    confirmButtonText:
                        EkubLocalization.of(context)!.translate("try_again"),
                    cancelButtonText: EkubLocalization.of(context)!
                        .translate("cancel"), onTapConfirm: () {
                  Navigator.pop(context);
                  _registerNewMember(
                      name, email, gender, password, dateOfBirth);
                }, onTapCancel: () {
                  Navigator.pop(context);
                }, panaraDialogType: PanaraDialogType.error);
                setState(() {
                  _onProcess = false;
                });
                return {};
              })
            }
          else
            {
              PanaraInfoDialog.show(
                context,
                message:
                    EkubLocalization.of(context)!.translate("error_message"),
                buttonText: EkubLocalization.of(context)!.translate("okay"),
                onTapDismiss: () {
                  Navigator.pop(context);
                },
                imagePath: errorDialogIcon,
                panaraDialogType: PanaraDialogType.error,
              ),
              setState(() {
                _onProcess = false;
              }),
            }
        });
  }

  _openHomeScreen(bool isAdmin, String role) async {
    // if (isFirstLogin == null) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BuildProfile(
          name: userNameControl.text,
          email: emailControl.text,
          phoneNumber: "+251${widget.args.phoneNumber}",
          gender: _selectedGender,
          args: BuildProfileArgs(
            isOnline: true,
            role: role,
            isAdmin: isAdmin,
          ),
        ),
      ),
    );
    // } else {

    // Navigator.pushNamedAndRemoveUntil(
    //     context, HomeScreen.routeName, (Route<dynamic> route) => false,
    //     arguments: argument);
    // }
  }

  _openLoginScreen() {
    LoginScreenArgs argument = LoginScreenArgs(isOnline: false);
    Navigator.pushNamedAndRemoveUntil(
        context,
        kIsWeb ? WelcomeScreen.routeName : LoginScreen.routeName,
        (Route<dynamic> route) => false,
        arguments: argument);
  }
}
