// ignore_for_file: unused_element, unused_field, non_constant_identifier_names, empty_catches, use_build_context_synchronously

import 'dart:async';

import 'package:ekub/models/user.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/language.dart'; // Added for language selection
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/screens/account/change_password.dart';
import 'package:ekub/screens/dashboard/admin/actions/add_member.dart';
import 'package:ekub/screens/dashboard/ekubs/equb_type_dashboard.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/screens/settings/profile.dart';
import 'package:ekub/screens/ui_kits/list_view.dart';
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:ekub/screens/ui_kits/no_internet_connection_found.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../../models/equb_type.dart';
import '../../../repository/equb_repos.dart';
import '../../../routs/shared.dart';
import '../../../utils/device.dart';
import '../../settings/constant.dart';
import '../../themes/ThemeProvider.dart';
import '../../ui_kits/app_bar.dart';
import 'add_ekub_type.dart';

class EqubsScreen extends StatefulWidget {
  const EqubsScreen({super.key});

  @override
  State<EqubsScreen> createState() => _EqubsScreenState();
}

class _EqubsScreenState extends State<EqubsScreen> {
  int totalEqubTypes = 0;
  var proLoaded = false;
  bool isLoading = true;
  bool isConnected = true;
  List<EqubType>? _items = [];
  List<String> equbTypes = [];
  var myMenuItems = <String>['Edit', 'Delete'];
  // Language selection variables
  List<String> languages = ["English", "አማርኛ", "Oromic", "ትግሪኛ", "Somaali"];
  String selectedLanguage = "Language";
  late AppLanguage appLanguage;
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      gender: "loading",
      email: "loading",
      role: "loading",
      memberId: "loading");
  final _appBar = GlobalKey<FormState>();
  late ThemeProvider themeProvider;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    appLanguage = Provider.of<AppLanguage>(context, listen: false);
    _controller = ScrollController();
    _loadProfile();
    loadEqubType();
    _fetchLocale(); // Initialize language selection
    super.initState();
  }

  _fetchLocale() async {
    await appLanguage.fetchLocale();
    String language = appLanguage.appLocale.languageCode.toLowerCase();

    if (language == "fr") {
      setState(() {
        selectedLanguage = "Oromic";
      });
    } else if (language == "es") {
      setState(() {
        selectedLanguage = "ትግሪኛ";
      });
    } else if (language == "am") {
      setState(() {
        selectedLanguage = "አማርኛ";
      });
    } else if (language == "tl") {
      setState(() {
        selectedLanguage = "Somaali";
      });
    } else if (language == "en") {
      setState(() {
        selectedLanguage = "English";
      });
    }
  }

  String formatDate(String date, [BuildContext? ctx]) {
    // Use the current app locale
    final locale = ctx != null
        ? Localizations.localeOf(ctx).toString()
        : Localizations.localeOf(context).toString();
    return DateFormat('MMMM-d-yyyy', locale).format(DateTime.parse(date));
  }

  _addEqubType() {
    Navigator.pushNamed(context, AddEqubType.routeName,
        arguments: AddEqubTypeArgs(forEdit: false));
  }

  _openProfile() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: ((context) => const ProfilePage(member: null))));
  }

  _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());

    try {
      await auth.getUserData().then((value) => {
            setState(() {
              user = value;
              proLoaded = true;
            })
          });
    } catch (e) {}
  }

  _deleteEqub(String id) {
    setState(() {
      isLoading = true;
    });
    var sender = EqubDataProvider(httpClient: http.Client());
    var res = sender.deleteEqubType(context, id);
    res
        .then((value) => {
              if (value["code"] == 200)
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("success"),
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: successDialogIcon,
                    panaraDialogType: PanaraDialogType.success,
                  ),
                  setState(() {
                    isLoading = false;
                  })
                  // _toastPassed("equb deleted sucessfully"),
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("error"),
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    panaraDialogType: PanaraDialogType.error,
                    imagePath: errorDialogIcon,
                  ),
                  setState(() {
                    isLoading = false;
                  })
                  // _toastPassed(value.message),
                }
            })
        .onError((error, stackTrace) {
      PanaraConfirmDialog.show(context,
          // title: "Request Timeout",
          message: error.toString(),
          confirmButtonText:
              EkubLocalization.of(context)!.translate("try_again"),
          cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
          onTapConfirm: () {
        Navigator.pop(context);
        _deleteEqub(id);
      }, onTapCancel: () {
        Navigator.pop(context);
      }, panaraDialogType: PanaraDialogType.error);
      return {};
    });
  }

  void loadEqubType() async {
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        setState(() {
          isConnected = false;
          isLoading = false;
        });
        return;
      }
      setState(() {
        isLoading = true;
      });
      await Provider.of<EqubDataProvider>(context, listen: false)
          .loadAllEqubTypes(context, 0, 1);

      setState(() {
        isLoading = false;
        isConnected = true;
      });
    } catch (e) {
      if (e is TimeoutException) {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("time_out"),
              message:
                  EkubLocalization.of(context)!.translate("timeout_message"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            Navigator.pop(context);
            loadEqubType();
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              message: e.toString(),
              // EkubLocalization.of(context)!
              //     .translate("error_load_equbtype"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            loadEqubType();
            Navigator.pop(context);
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    _items = Provider.of<EqubDataProvider>(context, listen: true)
        .equbTypes
        .equbTypes;
    totalEqubTypes = Provider.of<EqubDataProvider>(context, listen: true)
        .equbTypes
        .totalMember;

    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: EkubAppBar(
          key: _appBar,
          title: toCamelCase(user.fullName ?? ""),
          widgets: [
            // IconButton(onPressed: _addEqubType, icon: const Icon(Icons.add)),
            // Language Settings Button
            PopupMenuButton<String>(
              icon: Icon(
                Icons.language,
                color: themeProvider.getColor,
                size: 25,
              ),
              tooltip: 'Language Settings',
              onSelected: (value) {
                setState(() {
                  selectedLanguage = value;
                  if (selectedLanguage == "English") {
                    appLanguage.changeLanguage(const Locale("en"));
                  } else if (selectedLanguage == "አማርኛ") {
                    appLanguage.changeLanguage(const Locale("am"));
                  } else if (selectedLanguage == "Oromic") {
                    appLanguage.changeLanguage(const Locale("fr"));
                  } else if (selectedLanguage == "Somaali") {
                    appLanguage.changeLanguage(const Locale("tl"));
                  } else if (selectedLanguage == "ትግሪኛ") {
                    appLanguage.changeLanguage(const Locale("es"));
                  }
                });
              },
              itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                const PopupMenuItem<String>(
                  value: 'English',
                  child: Text('English'),
                ),
                const PopupMenuItem<String>(
                  value: 'አማርኛ',
                  child: Text('አማርኛ'),
                ),
                const PopupMenuItem<String>(
                  value: 'ትግሪኛ',
                  child: Text('ትግሪኛ'),
                ),
                const PopupMenuItem<String>(
                  value: 'Oromic',
                  child: Text('Oromic'),
                ),
                const PopupMenuItem<String>(
                  value: 'Somaali',
                  child: Text('Somaali'),
                ),
              ],
            ),
            IconButton(
                onPressed: () {
                  loadEqubType();
                },
                icon: const Icon(Icons.refresh)),
          ]),
      body: isConnected
          ? isLoading
              ? searchLoading()
              : _items!.isNotEmpty
                  ? RefreshIndicator(
                      onRefresh: () async {
                        await Future.delayed(const Duration(seconds: 2));
                        loadEqubType();
                      },
                      key: _refreshIndicatorKey,
                      child: SizedBox(
                        height: Device.body(context),
                        child: _items!.isEmpty
                            ? const CustomLoadingIndicator()
                            : listHolder(_items, themeProvider.getColor),
                      ),
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Center(
                            child: Text(
                          EkubLocalization.of(context)!
                              .translate("no_equbtypes_found"),
                          style: TextStyle(
                              color: bodyTextColor,
                              fontWeight: normalFontWeight),
                        )),
                      ],
                    )
          : NoConnectionWidget(
              fun: loadEqubType,
              isLoading: isLoading,
            ),
      drawer: _drawer(context),
    );
  }

  Widget listHolder(items, theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 15.0, top: 15),
          child: Text(EkubLocalization.of(context)!.translate("equbs"),
              style:
                  const TextStyle(fontWeight: FontWeight.bold, fontSize: 20)),
        ),
        Expanded(
          child: ListView.builder(
              itemCount: items.length,
              padding: const EdgeInsets.all(0.0),
              itemBuilder: (context, item) {
                return _buildListItems(context, items[item], item, theme);
              }),
        ),
      ],
    );
  }

  late ScrollController _controller;

  Widget _buildListItems(BuildContext context, EqubType equb, int item, theme) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => EqubTypeDashboard(
                      equbType: equb,
                    )));
      },
      child: _listUi(themeProvider, equb),
    );
  }

  _listUi(theme, EqubType equb) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, right: 8.0),
      child: GestureDetector(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => EqubTypeDashboard(
                        equbType: equb,
                      )));
        },
        child: Slidable(
          key: ValueKey(equb.id),
          startActionPane: ActionPane(
            motion: const ScrollMotion(),
            dismissible: DismissiblePane(
              onDismissed: () async {
                if (!await InternetConnectivity()
                    .checkInternetConnectivty(context, true)) {
                  setState(() {
                    isLoading = false;
                  });
                  return;
                }
                _onSelected(context, "Delete", equb);
              },
            ),
            children: [
              SlidableAction(
                onPressed: (context) async {
                  if (!await InternetConnectivity()
                      .checkInternetConnectivty(context, true)) {
                    setState(() {
                      isLoading = false;
                    });
                    return;
                  }
                  _onSelected(context, "Delete", equb);
                },
                backgroundColor: const Color(0xFFFE4A49),
                foregroundColor: Colors.white,
                icon: Icons.delete,
                label: EkubLocalization.of(context)!.translate("delete"),
              ),
              SlidableAction(
                onPressed: (context) {
                  _onSelected(context, "Edit", equb);
                },
                backgroundColor: const Color(0xFF21B7CA),
                foregroundColor: Colors.white,
                icon: Icons.edit,
                label: EkubLocalization.of(context)!.translate("edit"),
              ),
            ],
          ),
          child: EqubTypeCard(
            title: equb.name ?? "loading...",
            round: equb.round ?? "loading...",
            status: equb.status ?? "loading...",
            rote: equb.rote ?? "loading...",
            type: equb.type ?? "loading...",
            icon: Icons.payments,
            theme: theme,
            // widget: actionWidget(equb),
          ),
        ),
      ),
    );
  }

  Drawer _drawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: const EdgeInsets.only(top: 30),
        children: [
          Padding(
            padding: const EdgeInsets.only(
                top: 30.0, left: 20, bottom: 25, right: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CircleAvatar(
                  backgroundColor: themeProvider.getLightColor,
                  radius: 30,
                  child: Text(
                    user.fullName!.substring(0, 1).toUpperCase(),
                    style: const TextStyle(fontSize: 30.0, color: Colors.white),
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        toCamelCase(proLoaded ? user.fullName! : ""),
                        style: TextStyle(
                            color: themeProvider.getColor,
                            fontWeight: FontWeight.bold,
                            fontSize: fontMedium),
                      ),
                      Text(
                        proLoaded ? user.email ?? "" : "",
                        style: TextStyle(
                            color: Colors.grey.shade500,
                            fontWeight: FontWeight.bold,
                            fontSize: fontMedium),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Divider(
            color: Colors.grey.shade400,
          ),
          const SizedBox(
            height: 15,
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.home_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("home"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pushReplacementNamed(context, HomeScreen.routeName,
                  arguments: HomeScreenArgs(
                      isAdmin: user.role == "admin",
                      isOnline: true,
                      role: user.role!));
              // Navigator.pop(context);
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.group_add_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("add_member"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) =>
                        AddMember(args: AddMemberArgs(forEdit: false))),
              );
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.person_outline,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("my_profile"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pop(context);
              _openProfile();
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.lock_outline,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("change_password"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pushNamed(context, ChangePassword.routeName,
                  arguments: ChangePasswordArgs(
                      isOnline: true, role: user.role, fromDrawer: true));
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.logout,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("logout"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              PanaraConfirmDialog.show(
                context,
                title: EkubLocalization.of(context)!.translate("warning"),
                message:
                    EkubLocalization.of(context)!.translate("confirm_logout"),
                confirmButtonText:
                    EkubLocalization.of(context)!.translate("confirm"),
                cancelButtonText:
                    EkubLocalization.of(context)!.translate("cancel"),
                onTapCancel: () {
                  Navigator.pop(context);
                },
                onTapConfirm: () {
                  gotoSignIn(context);
                },
                imagePath: warningDialogIcon,

                panaraDialogType: PanaraDialogType.warning,
                barrierDismissible:
                    false, // optional parameter (default is true)
              );
            },
          ),
        ],
      ),
    );
  }

  _onSelected(context, item, EqubType equb) {
    switch (item) {
      case 'Edit':
        _editEqub(equb);

        break;
      case 'Delete':
        _deleteEqub(equb.id.toString());
    }
  }

  _editEqub(EqubType equbType) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => AddEqubType(
                args: AddEqubTypeArgs(forEdit: true, equbType: equbType))));
  }
}
