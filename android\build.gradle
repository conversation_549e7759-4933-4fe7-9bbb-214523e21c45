buildscript {
    ext.kotlin_version = '1.9.22'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.15'
    }
}


// allprojects {
//     repositories {
//         google()
//         jcenter()
//         mavenCentral()
//     }
// }

// rootProject.buildDir = '../build'
// subprojects {
//     project.buildDir = "${rootProject.buildDir}/${project.name}"
// }
// subprojects {
//     project.evaluationDependsOn(':app')
// }

// tasks.register("clean", Delete) {
//     delete rootProject.buildDir
// }

// build.gradle (allprojects)

// Set up repositories for dependencies
allprojects {
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs 'libs'
            dirs project(':app').file('libs')
        }
    }

    // Set the build directory
    rootProject.buildDir = '../build'

    // Configure subprojects
    subprojects {
        project.buildDir = "${rootProject.buildDir}/${project.name}"
    }

    // Ensure evaluation depends on the app module
    subprojects {
        project.evaluationDependsOn(':app')
    }

    // Register a clean task
    // tasks.register("clean", Delete) {
    //     delete rootProject.buildDir
    // }
}
