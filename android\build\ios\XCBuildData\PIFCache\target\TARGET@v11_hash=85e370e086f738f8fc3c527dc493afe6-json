{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f75edb0b6e17a726ea2d9b7b7d42dfb0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988d4b24edf61921b900096a16c395be01", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839bea6cb237dff03748e92e7be59c56b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850a074068a52047968b72c9dc4e6cb54", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839bea6cb237dff03748e92e7be59c56b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98118a40313daa9fc884de5844c740b2c8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989ad3ddea012ed61f3d9faa5b6c17601d", "guid": "bfdfe7dc352907fc980b868725387e9895c6dfd17a3f10e686c5ca9409445c54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98539c997b490d0101aa67955bc097a8c0", "guid": "bfdfe7dc352907fc980b868725387e987bc728f89015bbd936a34e9de289d9e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e654f07ad6003bbf30224807a26a79b0", "guid": "bfdfe7dc352907fc980b868725387e9861b2c9a89768cb1a181f5c441935f094", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897b8f394a7ceda79601719d86ec6e9b0", "guid": "bfdfe7dc352907fc980b868725387e980707ba19a20700ccf747b79d7d5109b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870db81092c1038e551102e66a6c68e2a", "guid": "bfdfe7dc352907fc980b868725387e98bad327881ae6ed98ff1c805597b52c68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0fdfb957699f2cf0134d7204ec133d4", "guid": "bfdfe7dc352907fc980b868725387e981bd517428d7042cf28f74f4471581cb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c83f9629066675c8402d74d4b57cd30", "guid": "bfdfe7dc352907fc980b868725387e98d355b082caeee570e5a1212ce631e46b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b93ffd806d85c2d2b1bf141a792caa7e", "guid": "bfdfe7dc352907fc980b868725387e98e9b40301943a0a957f4389082f8ed023", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874dfc5b970da839bbf6795ccd2c5454e", "guid": "bfdfe7dc352907fc980b868725387e9812dde9527931b5b0c539c78b925ec433", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98790b590ac74f356980abd0eb35ec37e8", "guid": "bfdfe7dc352907fc980b868725387e984e792f8f8ee7a727635b512e961ea4a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c28345b9e4006772864fd60b6a064d7", "guid": "bfdfe7dc352907fc980b868725387e989c45bf235811e0e4e5dbe8657bf6ae76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b9c7715a71972cd0ac3ae1c00abf535", "guid": "bfdfe7dc352907fc980b868725387e985234734e9a0e01fc3515666029c51dae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98838cdb4e807bab8b542db04540954a68", "guid": "bfdfe7dc352907fc980b868725387e988a92be63516213ba3169abdccd6f4de2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98862113a1856d5b2e3be099a7db232e0a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9874fb849b7430bee757835f2732a5e3c8", "guid": "bfdfe7dc352907fc980b868725387e98028f4652b935e8870024aa9f49dc7e53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98554be578eb9138cec2a4e1094f1cfd37", "guid": "bfdfe7dc352907fc980b868725387e98f8d1beda6c58ed31f7a465f7e1d8f0e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835560d3b97b03157450b9dcd7a942186", "guid": "bfdfe7dc352907fc980b868725387e98540f9a8fce3830c2c9b36318fede7165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98349ccdaf9c7037806c544102b676663e", "guid": "bfdfe7dc352907fc980b868725387e988ceee7c07a207205daf3b3324178fa27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb13f1aec9d209c9ae1a1b593ba38a6a", "guid": "bfdfe7dc352907fc980b868725387e98b8190744acfe552d5493f3b7c6cd45df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bebd673349d94f55ccc194b7030a34dd", "guid": "bfdfe7dc352907fc980b868725387e989674ff6b579f485977d157749853f2fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f13cc457801ce8e3049d2740f882fe49", "guid": "bfdfe7dc352907fc980b868725387e987f2769f40dd7b992ff2d0731ed65af37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824634659b8d553e67cc99d475eb0627e", "guid": "bfdfe7dc352907fc980b868725387e988e61c81107bc5f8ded598c0291bd1083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e23ac904af369721590c9d476e902561", "guid": "bfdfe7dc352907fc980b868725387e988d1d07d4d030e34bc188408ae511ff35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98735123abf93442db428383b1c7ad35d2", "guid": "bfdfe7dc352907fc980b868725387e98916daebd0844f613fe1f88e2f8e7c6d6"}], "guid": "bfdfe7dc352907fc980b868725387e984f71974877910e654a868ff58ead4e19", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a15b41da5b9cf00eaf9e8a12fb6fc89a", "guid": "bfdfe7dc352907fc980b868725387e98f365307a60e6ebefe400d34a07472b42"}], "guid": "bfdfe7dc352907fc980b868725387e9826f6baf48ad554ae59a661c3573353c3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e01e22d3721c1602087dfc3e30601fcd", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e98a2a9a8a2b19df0d350cbdc9de35f77c0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}