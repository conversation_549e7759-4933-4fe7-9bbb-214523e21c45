import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:flutter/material.dart';

class ConnectivityExample extends StatefulWidget {
  const ConnectivityExample({Key? key}) : super(key: key);

  @override
  State<ConnectivityExample> createState() => _ConnectivityExampleState();
}

class _ConnectivityExampleState extends State<ConnectivityExample> {
  bool _isLoading = false;
  String _connectionStatus = 'Unknown';
  final InternetConnectivity _connectivity = InternetConnectivity();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(EkubLocalization.of(context)!.translate('title')),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Connection Status: $_connectionStatus',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isLoading ? null : _checkConnection,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Text(EkubLocalization.of(context)!.translate('try_again')),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isLoading ? null : _checkDetailedConnection,
              child: const Text('Check Detailed Connection'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkConnection() async {
    setState(() {
      _isLoading = true;
      _connectionStatus = 'Checking...';
    });

    try {
      final isConnected = await _connectivity.checkInternetConnectivty(
        context,
        true, // Show error message if not connected
      );

      if (mounted) {
        setState(() {
          _connectionStatus = isConnected ? 'Connected' : 'Not Connected';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _connectionStatus = 'Error: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _checkDetailedConnection() async {
    setState(() {
      _isLoading = true;
      _connectionStatus = 'Checking...';
    });

    try {
      final result = await _connectivity.checkDetailedConnectivity();

      if (mounted) {
        setState(() {
          if (result.isConnected) {
            _connectionStatus = 'Connected';
          } else {
            _connectionStatus = 'Error: ${result.errorType.toString()}';
            if (result.errorDetails != null) {
              _connectionStatus += '\nDetails: ${result.errorDetails}';
            }
          }
          _isLoading = false;
        });

        if (!result.isConnected) {
          // Show error message manually
          _showErrorMessage(result);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _connectionStatus = 'Error: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorMessage(ConnectionCheckResult result) {
    if (result.isConnected) return;

    String message;
    
    switch (result.errorType) {
      case ConnectivityErrorType.noConnection:
        message = "${EkubLocalization.of(context)!.translate("no_connection")}!";
        break;
      case ConnectivityErrorType.unstableConnection:
        message = "${EkubLocalization.of(context)!.translate("connection_msg")}!";
        break;
      case ConnectivityErrorType.serverUnreachable:
        message = "${EkubLocalization.of(context)!.translate("internet_connection")}!";
        break;
      case ConnectivityErrorType.timeout:
        message = EkubLocalization.of(context)!.translate("timeout_message");
        break;
      case ConnectivityErrorType.unknown:
      default:
        message = EkubLocalization.of(context)!.translate("error_message");
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      backgroundColor: Colors.red,
      content: Text(message),
      duration: const Duration(seconds: 3),
    ));
  }
}
