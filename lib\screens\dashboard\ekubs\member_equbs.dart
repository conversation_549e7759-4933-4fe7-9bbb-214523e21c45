// ignore_for_file: no_logic_in_create_state, must_be_immutable, must_call_super, empty_catches, use_build_context_synchronously

import 'dart:async';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/exports/screens.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/models/user.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/language.dart'; // Uncommented for language selection
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/admin/actions/add_ekub.dart';
import 'package:ekub/screens/dashboard/admin/actions/completed_ekubs.dart';
import 'package:ekub/screens/dashboard/admin/actions/equb_subtypes.dart';
import 'package:ekub/screens/dashboard/admin/tabs/member_tabs.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/ui_kits/bottom_sheet.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/device.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../../models/ekub.dart';
import '../../../models/equb_type.dart';
import '../../../repository/equb_repos.dart';
import '../../../utils/tools.dart';
import '../../settings/profile.dart';
import '../../themes/ThemeProvider.dart';
import '../../ui_kits/app_bar.dart';
import '../../ui_kits/list_view.dart';
import '../../ui_kits/loading_indicator.dart';
import '../../ui_kits/no_internet_connection_found.dart';

class MemberEqubs extends StatefulWidget {
  static const routeName = '/member_equbs';
  MemberEqubsArgs args;
  MemberEqubs({super.key, required this.args});

  @override
  State<MemberEqubs> createState() => _MemberEqubsState(args);
}

class _MemberEqubsState extends State<MemberEqubs>
    with AutomaticKeepAliveClientMixin<MemberEqubs> {
  @override
  bool get wantKeepAlive => true;
  MemberEqubsArgs args;
  _MemberEqubsState(this.args);
  bool isConnected = true;
  bool proLoaded = false;
  bool isActive = true;
  bool isLoading = true;
  bool loading = true;
  int? totalEqubs = 0;
  int selectedTab = 0;
  Uint8List? image;
  Member? member;
  List<EqubType> _items = [];
  EqubType? selctedType;
  int selectedIndex = 0;
  List<String> equbTypes = [];
  Uint8List? equbImage;
  List<Uint8List?> images = [];
  // Language selection variables
  List<String> languages = ["English", "አማርኛ", "Oromic", "ትግሪኛ", "Somaali"];
  String selectedLanguage = "Language";
  String language = "";
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      gender: "loading",
      email: "loading",
      role: "loading",
      memberId: "loading");
  List<Equb>? items = [];
  List<Equb>? completedEqubs = [];

  var myMenuItems = <String>[];
  final _appBar = GlobalKey<FormState>();
  late ThemeProvider themeProvider;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  late AppLanguage appLanguage;
  final dropdownControl = TextEditingController();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    appLanguage = Provider.of<AppLanguage>(context, listen: false);
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.white,
      statusBarIconBrightness: Brightness.dark,
    ));

    // Add debug dialog
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('Member Equbs Debug Info'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    'Current Context Locale: ${Localizations.localeOf(context)}'),
                Text('Selected Language: $selectedLanguage'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    });

    myMenuItems.add("Add Equb");
    widget.args.isAdmin == true ? myMenuItems.add("Delete") : null;
    _fetchLocale(); // Initialize language selection
    _loadProfile();

    super.initState();
  }

  void loadEqubs() async {
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        setState(() {
          isConnected = false;
          isLoading = false;
        });
        return;
      }

      // Add debug dialog when loading equbs
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('Loading Equbs Debug Info'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    'Current Context Locale: ${Localizations.localeOf(context)}'),
                Text('Selected Language: $selectedLanguage'),
                Text('Member ID: ${args.member.id}'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }

      setState(() {
        isLoading = true;
      });
      member = await Provider.of<MemberProvider>(context, listen: false)
          .getMember(context, args.member.id.toString());
      setState(() {
        isActive = false;
      });

      member == null ? null : loadEqubType();

      await Provider.of<EqubDataProvider>(context, listen: false)
          .loadMemberEqub(context, 0, 1, args.member.id!);
      await Provider.of<MemberProvider>(context, listen: false)
          .loadCompletedMemberEqub(context, args.member.id!.toString());
      setState(() {
        isLoading = false;
        isConnected = true;
      });
      print("Loading profile picture...");
      image = await Provider.of<MemberProvider>(context, listen: false)
          .getProfilePicture(context, args.member.id.toString());
    } catch (e) {
      if (e is TimeoutException) {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("time_out"),
              message:
                  EkubLocalization.of(context)!.translate("timeout_message"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            Navigator.pop(context);
            loadEqubs();
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              message: e.toString(),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            if (mounted) {
              loadEqubs();
              Navigator.pop(context);
            }
          }, onTapCancel: () {
            setState(() {
              isLoading = false;
            });
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());

    try {
      // _fetchLocale(); // Removed call
      await auth.getUserData().then((value) => {
            setState(() {
              user = value;
              proLoaded = true;
            }),
            loadEqubs(),
          });
    } catch (e) {}
  }

  _fetchLocale() async {
    await appLanguage.fetchLocale();
    language = appLanguage.appLocale.languageCode.toLowerCase();

    print("Current language code: $language"); // Debug print

    if (language == "fr") {
      setState(() {
        selectedLanguage = "Oromic";
      });
    } else if (language == "es") {
      setState(() {
        selectedLanguage = "ትግሪኛ";
      });
    } else if (language == "am") {
      setState(() {
        selectedLanguage = "አማርኛ";
      });
    } else if (language == "tl") {
      setState(() {
        selectedLanguage = "Somaali";
      });
    } else if (language == "en") {
      setState(() {
        selectedLanguage = "English";
      });
    }
  }

  _deleteMember(String id) {
    setState(() {
      isLoading = true;
    });
    var sender = MemberProvider(httpClient: http.Client());
    var res = sender.deleteMember(context, id);
    res
        .then((value) => {
              if (value["code"] == 200)
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                    imagePath: successDialogIcon,
                    panaraDialogType: PanaraDialogType.success,
                  ),
                  setState(() {
                    isLoading = false;
                  })
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    isLoading = false;
                  })
                }
            })
        .onError((error, stackTrace) {
      PanaraConfirmDialog.show(context,
          // title: "Request Timeout",
          message: error.toString(),
          confirmButtonText:
              EkubLocalization.of(context)!.translate("try_again"),
          cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
          onTapConfirm: () {
        Navigator.pop(context);
        _deleteMember(id);
      }, onTapCancel: () {
        Navigator.pop(context);
      }, panaraDialogType: PanaraDialogType.error);
      return {};
    });
  }

  _addEqubPage() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => EqubSubtypeScreen(
                  args: AddEqubArgs(
                      isOnline: true,
                      member: args.member,
                      isAdmin: true,
                      user: user),
                )));
    // Navigator.pushNamed(context, AddEqub.routeName,
    //     arguments: AddEqubArgs(
    //         isOnline: true, member: args.member, isAdmin: true, user: user));
  }

  _onSelected(item) {
    switch (item) {
      case 'Add Equb':
        member!.status == "Active"
            ? _addEqubPage()
            : PanaraInfoDialog.show(context,
                message:
                    EkubLocalization.of(context)!.translate("inactive_member"),
                buttonText: EkubLocalization.of(context)!.translate("okay"),
                onTapDismiss: () {
                Navigator.pop(context);
              }, panaraDialogType: PanaraDialogType.normal);

        break;

      case 'Delete':
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("delete"),
            message:
                "${EkubLocalization.of(context)!.translate("confirm_delete")} ${args.member.fullName}?",
            confirmButtonText:
                EkubLocalization.of(context)!.translate("delete"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            imagePath: warningDialogIcon, onTapConfirm: () {
          Navigator.pop(context);
          _deleteMember(args.member.id.toString());
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.warning);

        break;
    }
  }

  _openProfile() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => ProfilePage(
                  image: image,
                  member: member,
                )));
  }

  void loadEqubType() async {
    int index = 0;
    setState(() {
      loading = true;
    });
    try {
      equbTypes = [];
      _items = await Provider.of<EqubDataProvider>(context, listen: false)
          .loadEqubTypes(context, 0, 1);
      setState(() {
        images.length = _items.length;
        selctedType = _items.isEmpty ? null : _items[0];
        for (var element in _items) {
          if (element.status == "Active") {
            equbTypes.add(element.name.toString());
            getImage(element.id.toString(), index);
          }
          index += 1;
        }
        loading = false;
      });
    } catch (e) {
      if (e is TimeoutException) {
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("time_out"),
            message: EkubLocalization.of(context)!.translate("timeout_message"),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);

          loadEqubType();
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      } else {
        PanaraConfirmDialog.show(context,
            message: e.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          if (mounted) {
            loadEqubType();

            Navigator.pop(context);
          }
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        loading = false;
      });
    }
  }

  getImage(String id, index) async {
    try {
      equbImage = await Provider.of<EqubDataProvider>(context, listen: false)
          .getEqubtypePicture(context, id);
      setState(() {
        images[index] = equbImage;
      });
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // Use EkubLocalization directly
    return Builder(
      builder: (context) {
        // Access localization within this context
        var language = EkubLocalization.of(context)!;
        // Access AppLanguage for language selection
        appLanguage = Provider.of<AppLanguage>(context, listen: false);
        image = Provider.of<MemberProvider>(context, listen: true).image;
        member =
            Provider.of<MemberProvider>(context, listen: true).memberDetail;
        items =
            Provider.of<EqubDataProvider>(context, listen: true).equbs.equbs;
        totalEqubs = Provider.of<EqubDataProvider>(context, listen: true)
            .equbs
            .totalMember;
        completedEqubs = Provider.of<MemberProvider>(context, listen: true)
            .completedEqubs
            .equbs;

        if (isLoading) {
          return searchLoading();
        }

        if (!isConnected) {
          return NoConnectionWidget(
            fun: loadEqubs,
            isLoading: isLoading,
          );
        }

        // Wrap the main content in an error boundary
        return ErrorBoundary(
          onError: (error, stackTrace) {
            print('Error in member equbs: $error');
            print('Stack trace: $stackTrace');
            return Scaffold(
              backgroundColor: ColorProvider.backgroundColor,
              appBar: EkubAppBar(
                key: _appBar,
                title: toCamelCase(args.member.fullName ?? "Member Equbs"),
                widgets: [
                  // Language Settings Button
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.language,
                      color: themeProvider.getColor,
                      size: 25,
                    ),
                    tooltip: 'Language Settings',
                    onSelected: (value) {
                      setState(() {
                        selectedLanguage = value;
                        if (selectedLanguage == "English") {
                          appLanguage.changeLanguage(const Locale("en"));
                        } else if (selectedLanguage == "አማርኛ") {
                          appLanguage.changeLanguage(const Locale("am"));
                        } else if (selectedLanguage == "Oromic") {
                          appLanguage.changeLanguage(const Locale("fr"));
                        } else if (selectedLanguage == "Somaali") {
                          appLanguage.changeLanguage(const Locale("tl"));
                        } else if (selectedLanguage == "ትግሪኛ") {
                          appLanguage.changeLanguage(const Locale("es"));
                        }
                      });
                    },
                    itemBuilder: (BuildContext context) =>
                        <PopupMenuEntry<String>>[
                      const PopupMenuItem<String>(
                        value: 'English',
                        child: Text('English'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'አማርኛ',
                        child: Text('አማርኛ'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'ትግሪኛ',
                        child: Text('ትግሪኛ'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'Oromic',
                        child: Text('Oromic'),
                      ),
                      const PopupMenuItem<String>(
                        value: 'Somaali',
                        child: Text('Somaali'),
                      ),
                    ],
                  ),
                  IconButton(
                    onPressed: () {
                      _loadProfile();
                    },
                    icon: const Icon(Icons.refresh),
                  ),
                ],
              ),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline,
                        size: 48, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      'An error occurred. Please try again.',
                      style: TextStyle(color: themeProvider.getColor),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        _loadProfile();
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            );
          },
          child: Scaffold(
            backgroundColor: ColorProvider.backgroundColor,
            appBar: EkubAppBar(
              key: _appBar,
              title: toCamelCase(
                  args.member.fullName ?? language.translate("equbs")),
              widgets: [
                // Language Settings Button
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.language,
                    color: themeProvider.getColor,
                    size: 25,
                  ),
                  tooltip: 'Language Settings',
                  onSelected: (value) {
                    setState(() {
                      selectedLanguage = value;
                      if (selectedLanguage == "English") {
                        appLanguage.changeLanguage(const Locale("en"));
                      } else if (selectedLanguage == "አማርኛ") {
                        appLanguage.changeLanguage(const Locale("am"));
                      } else if (selectedLanguage == "Oromic") {
                        appLanguage.changeLanguage(const Locale("fr"));
                      } else if (selectedLanguage == "Somaali") {
                        appLanguage.changeLanguage(const Locale("tl"));
                      } else if (selectedLanguage == "ትግሪኛ") {
                        appLanguage.changeLanguage(const Locale("es"));
                      }
                    });
                  },
                  itemBuilder: (BuildContext context) =>
                      <PopupMenuEntry<String>>[
                    const PopupMenuItem<String>(
                      value: 'English',
                      child: Text('English'),
                    ),
                    const PopupMenuItem<String>(
                      value: 'አማርኛ',
                      child: Text('አማርኛ'),
                    ),
                    const PopupMenuItem<String>(
                      value: 'ትግሪኛ',
                      child: Text('ትግሪኛ'),
                    ),
                    const PopupMenuItem<String>(
                      value: 'Oromic',
                      child: Text('Oromic'),
                    ),
                    const PopupMenuItem<String>(
                      value: 'Somaali',
                      child: Text('Somaali'),
                    ),
                  ],
                ),
                IconButton(
                  onPressed: () {
                    _loadProfile();
                  },
                  icon: const Icon(Icons.refresh),
                ),
                user.role == "admin"
                    ? PopupMenuButton<String>(
                        onSelected: _onSelected,
                        itemBuilder: (BuildContext context) {
                          return myMenuItems.map((String choice) {
                            return PopupMenuItem<String>(
                              value: choice,
                              child: Text(
                                choice,
                                style: const TextStyle(color: Colors.black),
                              ),
                            );
                          }).toList();
                        })
                    : !isLoading &&
                            user.role == "equb_collector" &&
                            member!.status == "Active"
                        ? IconButton(
                            onPressed: () {
                              _addEqubPage();
                            },
                            icon: const Icon(Icons.add))
                        : Container(),
              ],
            ),
            body: _buildContent(),
            drawer: user.role != "member" ? null : _drawer(context, language),
          ),
        );
      },
    );
  }

  Drawer _drawer(BuildContext context, EkubLocalization language) {
    return Drawer(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.only(top: 30),
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                      top: 30.0, left: 20, bottom: 25, right: 20),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => UpdateAccount(
                                  args: UpdateAccountArgs(
                                      isOnline: true, user: member!),
                                  user: member!),
                            ),
                          );
                        },
                        child: CircleAvatar(
                          radius: 30,
                          backgroundColor: themeProvider.getLightColor,
                          backgroundImage:
                              image != null ? MemoryImage(image!) : null,
                          child: image != null
                              ? Container()
                              : Text(
                                  user.fullName!.substring(0, 1).toUpperCase(),
                                  style: const TextStyle(
                                      fontSize: 30.0, color: Colors.white),
                                ),
                        ),
                      ),
                      const SizedBox(
                        width: 15,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              toCamelCase(member!.fullName ?? ""),
                              style: TextStyle(
                                  color: themeProvider.getColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: fontMedium),
                            ),
                            Text(
                              member!.phone ?? "",
                              style: TextStyle(
                                  color: Colors.grey.shade500,
                                  fontWeight: FontWeight.bold,
                                  fontSize: fontMedium),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(
                  color: Colors.grey.shade400,
                ),
                const SizedBox(
                  height: 15,
                ),

                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.home_outlined,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    language.translate("home"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    loadEqubs();
                    Navigator.pop(context);
                  },
                ),
                // ListTile(
                //   leading: IconButton(
                //       onPressed: () {
                //         appLanguage.changeLanguage(Locale("en"));
                //       },
                //       icon: const Icon(Icons.language)),
                // ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.add_circle_outline,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("join_ekub"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => EqubSubtypeScreen(
                                  args: AddEqubArgs(
                                      isOnline: true,
                                      member: member!,
                                      isAdmin: false,
                                      user: user),
                                )));
                  },
                ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.list_alt_outlined,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("completed_ekub"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => CompletedEqubs(
                                  memberId: member!.id.toString(),
                                )));
                  },
                ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.person_outline,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("my_profile"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _openProfile();
                  },
                ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.lock_outline,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("change_password"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.pushNamed(context, ChangePassword.routeName,
                        arguments: ChangePasswordArgs(
                            isOnline: true, role: user.role, fromDrawer: true));
                  },
                ),
                ListTile(
                    leading: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 5, vertical: 2),
                      height: 35,
                      width: 35,
                      decoration: BoxDecoration(
                          color: ColorProvider.primary.withOpacity(.1),
                          shape: BoxShape.circle),
                      child: Icon(
                        Icons.contact_emergency_outlined,
                        color: ColorProvider.primary,
                        size: 25,
                      ),
                    ),
                    onTap: () {
                      contactUsWidget(context);
                    },
                    title: Text(
                      EkubLocalization.of(context)!.translate("contact_us"),
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: fontMedium),
                    )),
              ],
            ),
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.logout_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("logout"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              PanaraConfirmDialog.show(
                context,
                title: EkubLocalization.of(context)!.translate("warning"),
                message:
                    EkubLocalization.of(context)!.translate("confirm_logout"),
                confirmButtonText:
                    EkubLocalization.of(context)!.translate("confirm"),
                cancelButtonText:
                    EkubLocalization.of(context)!.translate("cancel"),
                onTapCancel: () {
                  Navigator.pop(context);
                },
                onTapConfirm: () {
                  gotoSignIn(context);
                },
                imagePath: warningDialogIcon,
                panaraDialogType: PanaraDialogType.warning,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (isLoading) {
      return searchLoading();
    }

    if (!isConnected) {
      return NoConnectionWidget(
        fun: loadEqubs,
        isLoading: isLoading,
      );
    }

    return DefaultTabController(
      length: 2,
      child: RefreshIndicator(
        onRefresh: _loadProfile(),
        key: _refreshIndicatorKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            Expanded(
              child: user.role == "member"
                  ? _buildMemberView()
                  : _buildAdminView(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return user.role == "member"
        ? Container(
            // alignment: Alignment.center,
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.only(bottom: 10, left: 20),
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20))),
            child: Text(
              items!.isEmpty
                  ? EkubLocalization.of(context)!.translate("select_ekub_type")
                  : '${EkubLocalization.of(context)!.translate("ongoing")} ${EkubLocalization.of(context)!.translate("equbs")}',
              style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 25),
            ),
          )
        : Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20))),
            child: TabBar(
                labelStyle: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
                unselectedLabelColor: Colors.black,
                indicatorWeight: 3.0,
                dividerHeight: 0,
                indicatorSize: TabBarIndicatorSize.tab,
                indicator: BoxDecoration(
                  color: themeProvider.getColor,
                  borderRadius: BorderRadius.circular(5),
                ),
                tabs: [
                  Tab(
                      child: Text(
                    items!.isEmpty
                        ? EkubLocalization.of(context)!
                            .translate("select_ekub_type")
                        : '${EkubLocalization.of(context)!.translate("ongoing")} ${EkubLocalization.of(context)!.translate("equbs")}',
                  )),
                  Tab(
                    child: Text(EkubLocalization.of(context)!
                        .translate("completed_ekub")),
                  ),
                ]),
          );
  }

  Widget _buildMemberView() {
    return Expanded(
      child: SizedBox(
          height: Device.body(context),
          child: listHolder(items, themeProvider.getColor, member, false)),
    );
  }

  Widget _buildAdminView() {
    return Expanded(
      child: TabBarView(children: [
        SizedBox(
            height: Device.body(context),
            child: listHolder(items, themeProvider.getColor, member, false)),
        SizedBox(
            height: Device.body(context),
            child: listHolder(
                completedEqubs, themeProvider.getColor, member, true)),
      ]),
    );
  }

  Widget listHolder(items, theme, Member? member, bool completed) {
    return items.isEmpty && completed
        ? Center(
            child: Text(
            EkubLocalization.of(context)!.translate("ekub_empty"),
            style:
                TextStyle(color: bodyTextColor, fontWeight: normalFontWeight),
          ))
        : items.isNotEmpty && !isLoading
            ? Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                        itemCount: items.length,
                        padding: const EdgeInsets.all(0.0),
                        itemBuilder: (context, index) {
                          return _buildListItems(
                              context, items[index], index, theme, completed);
                        }),
                  ),
                ],
              )
            : isLoading
                ? searchLoading()
                : Container(
                    // height: MediaQuery.of(context).size.height,
                    margin: const EdgeInsets.symmetric(horizontal: 15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 30),
                        Expanded(
                            child: _items.isEmpty
                                ? const Center(
                                    child: Text("No equbs found to subscribe"),
                                  )
                                : ListView.builder(
                                    itemCount: _items.length,
                                    itemBuilder: (context, index) {
                                      return GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              selectedIndex =
                                                  index; // Update selected index on tap
                                              selctedType = _items[index];
                                            });
                                            Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                    builder: (context) =>
                                                        AddEqub(
                                                            slectedEqubType:
                                                                selctedType!,
                                                            args: AddEqubArgs(
                                                                isOnline: true,
                                                                member: member!,
                                                                isAdmin: false,
                                                                user: user))));
                                          },
                                          child: roleContainer(
                                              context,
                                              _items[index],
                                              selectedIndex == index,
                                              themeProvider,
                                              images[index] != null &&
                                                      images[index]!.isNotEmpty
                                                  ? images[index]
                                                  : null));
                                    })),
                        const SizedBox(height: 20),
                      ],
                    ),
                  );
  }

  Widget _buildListItems(
      BuildContext context, Equb equb, int item, theme, bool completed) {
    return GestureDetector(
      onTap: () {
        MemberETabsArgs arg = MemberETabsArgs(
            completed: completed,
            equb: equb,
            isOnline: true,
            role: user.role,
            index: item,
            memberId: user.id,
            member: member!);
        Navigator.pushNamed(context, MemberTabs.routeName, arguments: arg);
      },
      child: _listUi(theme, equb),
    );
  }

  _listUi(Color theme, Equb equb) {
    // Prepare round value with fallback
    String roundValue = "N/A";
    if (equb.equbType != null) {
      roundValue = equb.equbType!.round ?? "N/A";
      if (roundValue == "null" || roundValue.isEmpty) {
        roundValue = "Round ${equb.id}"; // Fallback to a generated round name
      }

      // Log only in debug mode and only once per session
      // debugPrint("Equb ID: ${equb.id}");
      // debugPrint("Equb Type: ${equb.equbType!.toJson()}");
      // debugPrint("Equb Start Date: ${equb.startDate}");
    }

    return Padding(
      padding: const EdgeInsets.only(left: 15, right: 15.0, top: 10),
      child: EqubCard(
        status: equb.status ?? "loading...",
        round: roundValue,
        amount: equb.amount ?? "loading...",
        icon: Icons.person,
        equbType: equb.equbType?.name ?? "loading...",
        totalAmount: equb.totalAmount ?? "loading...",
        startDate: equb.startDate ?? "loading...",
        endDate: equb.endDate ?? "loading...",
        lotteryDate: equb.lotteryDate ?? "loading...",
        totalPayment: equb.totalPayment ?? "loading...",
        remainingPayment: equb.remainingPayment ?? "loading...",
        remainingLotteryDate: equb.remainingLotteryDate ?? "loading...",
        theme: themeProvider,
      ),
    );
  }

  Scaffold inactiveUser(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            onPressed: () {
              loadEqubs();
            },
            icon: const Icon(Icons.refresh)),
        backgroundColor: Colors.black,
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Row(
              children: [
                Text(
                  EkubLocalization.of(context)!.translate("logout"),
                  style: const TextStyle(
                      color: Colors.red,
                      fontSize: fontMedium,
                      fontWeight: normalFontWeight),
                ),
                IconButton(
                    onPressed: () {
                      PanaraConfirmDialog.show(
                        context,
                        title:
                            EkubLocalization.of(context)!.translate("warning"),
                        message: EkubLocalization.of(context)!
                            .translate("confirm_logout"),
                        confirmButtonText:
                            EkubLocalization.of(context)!.translate("confirm"),
                        cancelButtonText:
                            EkubLocalization.of(context)!.translate("cancel"),
                        onTapCancel: () {
                          Navigator.pop(context);
                        },
                        onTapConfirm: () {
                          gotoSignIn(context);
                        },
                        imagePath: warningDialogIcon,
                        panaraDialogType: PanaraDialogType.warning,
                      );
                    },
                    icon: const Icon(
                      Icons.logout,
                      color: Colors.red,
                      size: 20,
                    )),
              ],
            ),
          )
        ],
      ),
      body: SafeArea(
        child: isLoading
            ? searchLoading()
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Image(
                    height: 150,
                    image: AssetImage(
                      "assets/icons/inactive_user.png",
                    ),
                    fit: BoxFit.cover,
                  ),
                  Center(
                    child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 30),
                        child: Text(
                          EkubLocalization.of(context)!
                              .translate("inactive_user"),
                          style: TextStyle(
                              color: bodyTextColor,
                              fontWeight: normalFontWeight),
                        )),
                  ),
                ],
              ),
      ),
    );
  }

  roleContainer(
      BuildContext context, EqubType type, bool isSelected, theme, equbImage) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.only(bottom: 10),
      height: 100,
      // width: 150,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5), //color of shadow
              spreadRadius: .1, //spread radius
              blurRadius: 2, // blur radius
              offset: const Offset(0, 2), // changes position of shadow
            ),
          ],
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(15))),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.center,
            width: 55,
            height: 55,
            // margin: const /.only(bottom: 15),
            decoration: BoxDecoration(
                shape: BoxShape.circle, color: themeProvider.getLightColor),
            child: Text(
              type.name!.substring(0, 1),
              style: TextStyle(
                  color: themeProvider.getColor,
                  fontWeight: FontWeight.w900,
                  fontSize: fontBig),
            ),
          ),
          const SizedBox(
            width: 15,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                type.name!,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                    color:
                        // isSelected ? Colors.white :
                        Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: fontBig,
                    overflow: TextOverflow.ellipsis),
              ),
              const SizedBox(
                height: 5,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${EkubLocalization.of(context)!.translate("round")} : ',
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color:
                                // isSelected ? Colors.white :
                                Colors.grey[400],
                            fontWeight: FontWeight.normal,
                            fontSize: fontSmall,
                            overflow: TextOverflow.ellipsis),
                      ),
                      Text(
                        "${type.round}",
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                            color:
                                // isSelected ? Colors.white :
                                Colors.black,
                            fontWeight: FontWeight.bold,
                            fontSize: fontMedium,
                            overflow: TextOverflow.ellipsis),
                      ),
                    ],
                  ),
                  const SizedBox(
                    width: 30,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${EkubLocalization.of(context)!.translate("type")} : ',
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color:
                                // isSelected ? Colors.white :
                                Colors.grey[400],
                            fontWeight: FontWeight.normal,
                            fontSize: fontSmall,
                            overflow: TextOverflow.ellipsis),
                      ),
                      Text("${type.type}",
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              color:
                                  // isSelected ? Colors.white :
                                  Colors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: fontMedium,
                              overflow: TextOverflow.ellipsis)),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Add ErrorBoundary widget at file level
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace stackTrace) onError;

  const ErrorBoundary({
    super.key,
    required this.child,
    required this.onError,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? error;
  StackTrace? stackTrace;

  @override
  Widget build(BuildContext context) {
    if (error != null) {
      return widget.onError(error!, stackTrace!);
    }

    ErrorWidget.builder = (FlutterErrorDetails details) {
      setState(() {
        error = details.exception;
        stackTrace = details.stack;
      });
      return widget.onError(error!, stackTrace!);
    };
    return widget.child;
  }
}
