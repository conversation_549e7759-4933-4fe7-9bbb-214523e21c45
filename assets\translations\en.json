{"title": "Virtual Equb", "collected_today": "Collected Today", "today": "Today", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "biweekly": "Biweekly", "collected": "Collected", "expected": "Expected", "home": "Home", "subscribe_ekub": "Subscribe Equb", "join_ekub": "<PERSON><PERSON>", "join": "Join", "add_ekub": "<PERSON><PERSON>", "my_profile": "My Profile", "edit_profile": "Edit Profile", "ekubs": "<PERSON><PERSON><PERSON><PERSON>", "etb": "ETB", "male": "Male", "female": "Female", "members": "Members", "status": "Status", "remaining_payment": "Remaining payment", "remaining_lottery_date": "Till lottery", "remaining_days": "Till end", "remaining_amount": "Remaining Amount", "paid_with": "Paid with", "paid_by": "Paid by", "days": "Days", "started": "Started", "ends": "Ends", "paid": "Paid", "of": "of", "active": "Active", "payment": "Payment", "lottery": "Lottery", "not_paid": "Not paid yet!", "select_payment_method": "Method", "method_empty": "Please select method", "cash": "Cash", "check": "Check", "bank transfer": "Bank transfer", "other": "Other", "pay": "Pay", "proceed": "Proceed", "no_record": "No record found", "payment_history": "Payment History", "to": "To", "balance": "Balance", "credit": "Credit", "no_payment_history": "No payment history found", "round": "Round", "rote": "<PERSON><PERSON>", "start_date": "Start Date", "end_date": "End Date", "amount": "Amount", "terms": "Terms and Conditions on", "accept_terms_conditions": "I agree to the terms and conditions", "no_terms": "At the moment, there are no terms and conditions available", "subscribe": "Subscribe", "user_information": "User Information", "role": "Role", "phone_number": "Phone Number", "user_settings": "User Settings", "select_theme": "Select Theme", "developed_by": "Developed By", "vintage_technologies": "Vintage Technologies", "contact": "Contact", "skip": "<PERSON><PERSON>", "address_detail": "Address Details", "submit": "SUBMIT", "selected_city": "Selected city", "registration": "Registration", "login": "<PERSON><PERSON>", "login_subtitle": "Please sign in to continue", "logout": "Logout", "register": "Register", "forget_password": "Forgot Password", "confirm_code": "Confirm Code", "reset_password": "Reset Password", "change_password": "Change Password", "update_account": "Update Account", "full_name": "Full Name", "email": "Email", "city": "City", "sub_city": "Sub-city", "woreda": "<PERSON><PERSON><PERSON>", "house_number": "House Number", "specific_location": "Work Location", "update": "Update", "change": "CHANGE", "old_password": "Old Password", "new_password": "New Password", "confirm_password": "Confirm Password", "phone": "Phone", "password": "Password", "blocked": "Locked", "blocked_msg": "Your account has been temporarily locked due to multiple incorrect attempts. Please try again after a 5-minute waiting period", "unlocked": "Unlocked. ", "unlocked_msg": "Your account has been successfully unlocked. Please attempt to log in using the correct credentials", "don't_have_account": "Don't have an account", "sign_up": "Sign Up", "error_load_equbtype": "Unable to load Equb types, Please try again ", "error_load_data": "Unable to load data, please try again.", "verify_phone_number": "Verify Phone Number", "verify_phone_message": "Please provide your phone number so that we can send you a verification code to confirm your number", "send_code": "SEND CODE", "have_account": "Already have an account", "sign_in": "Sign In", "unknown_phone": "The provided phone number is not registered in our system.", "registered_phone": "It seems that there is an account associated with the provided phone number", "enter_code": "Enter Your Verification Code", "verify": "Verify", "Resending": "Resending", "resend": "Resend", "code_not_sent": "Didn't get the code?", "reset": "Reset", "add_ekub_type": "Add Equb Type", "add_member": "Add Member", "name": "Name", "select_rote": "Select Rote", "select_type": "Select type", "remark": "Remark", "add": "ADD", "automatic": "Automatic", "manual": "Manual", "select_city": "Select city", "add_new_member": "Add new member", "edit_member": "Edit member", "edit": "Edit", "delete": "Delete", "confirm_delete": "Are you certain that you want to proceed with the deletion?", "edit_ekub_type": "Edit Equb Type", "warning": "Warning", "go_back": "Would you like to go back?", "confirm": "Confirm", "cancel": "Cancel", "equbs": "<PERSON><PERSON><PERSON><PERSON>", "otp_title": "Enter Your Verification Code", "otp_message": "Enter the 6-digit code we have sent to", "resend_otp": "Request resend code in", "login_failed": "Login Failed!", "invalid_otp_message": "Invalid code. Please double-check the code and try again", "resending_code": "Resending Code", "request_code": "REQUEST CODE", "time_out": "Request Timeout", "timeout_message": "The request took too long! Please try again", "try_again": "Try Again", "error": "Error", "winner_msg": "Congratulations! You have been selected as today's winner.", "select_city_error": "Please select city", "type": "Type", "error_message": "Something went wrong! Please try again.", "select_ekub_type": "Select Equb Type", "type_loading": "Equb types loading. Please wait", "lottery_date": "Lottery Date", "subscribe_ekub_msg": "You have subscribed successfully.", "success": "Success", "user_credential": "User Credential", "winner": "Today's winner is", "loading": "Loading", "view_detail": "View Detail", "lottery_date_is": "Lottery date is on", "details": "Details", "unavailable": "Unavailable", "confirm_payment": "Confirm Payment", "confirm_payment_msg": "Are you sure you want to proceed?", "payment_unsuccessfull": "The payment was not made successfully.", "payment_successful": "Congratulations! You have paid", "chapa": "<PERSON><PERSON>", "enter_name": "Enter name or phone number", "confirm_logout": "Would you like to log out?", "no_subscribtion_found_member": "The member has not subscribed to any Equb yet.", "no_subscribtion_found_admin": "You haven't subscribed to any Equb yet", "inactive_user": "Your account is currently awaiting approval. You will receive an SMS notification as soon as it is approved.", "unable_load_member": "Unable to load members! Please try again", "no_member_found": "No members have been registered yet", "no_equbtypes_found": "No equb types have been registered yet", "check_from": "Check from", "check_detail": "Check detail", "no_connection": "No internet connection found", "connection_msg": "Please verify your internet connection", "internet_connection": "Please ensure that you have a stable internet connection and try again", "email_empty_member": "Please Enter Your Email", "email_empty": "Please Enter Email", "email_invalid": "Please Enter <PERSON><PERSON> Email Address", "name_empty": "Please Enter Full Name", "name_empty_member": "Please Enter Your Full Name", "name_invalid": "Please Enter Valid Name", "name_length": "Full Name length must be less than 50", "phone_empty": "Please Enter Phone Number", "phone_empty_member": "Please Enter Your Phone Number", "phone_length": "Phone Number length must not be less than 9", "valid_field": "Please Enter", "field_length": "Length must be less than 50", "subscribe_equb_msg": "You have subscribed successfully!", "password_empty": "Please Enter Your Password", "password_length": "Password length must not be less than 6", "password_limit": "Password length must not be greater than 25", "confirm_password_empty": "Please Confirm Your Password", "mismatch_password": "The entered password does not match", "code_empty": "Please Enter Code", "code_length": "Code length must not be less than 6", "select_ekub_type_error": "Please select equb type", "automatic_equb_msg1": "This is Automatic equb", "automatic_equb_msg2": "You are only expected to enter amount to subscribe.", "and": "and", "next": "NEXT", "starts": "Starts", "upload_image": "Upload payment slip or screenshot", "image_empty_title": "Payment proof is needed!", "image_empty_msg": "Please provide a slip or screenshot of your payment for confirmation", "manual_equb_msg": "This is Manual equb type. You are only expected to select the start date, timeline and amount to subscribe.", "timeline_error": "Please select timeline", "payment_error_title": "Please provide a slip or screenshot of your payment for confirmation", "payment_error_msg": "Send image to confirm your payment", "select_timeline": "Timeline", "select_timeline_error": "Please select timeline", "okay": "Okay", "general_settings": "General Settings", "passed": "Passed", "rate": "Rate", "rate_empty": "Please enter rate", "rate_lenght": "Rate must be between 0 and 10", "rate_member": "Rate member", "rate_title": "What do you rate this member?", "ekub_empty": "No completed equbs found", "no_passed_equb": "No passed equbs found", "inactive_ekub": "Equb is inactive", "completed_ekub": "Completed Equbs", "passed_equb": "Passed Eq<PERSON>s", "completed": "Completed", "unpaid": "unpaid", "ongoing": "Ongoing", "contact_us": "Contact Us", "call_us": "Call us now", "inactive_member": "This member is inactive", "delete_account": "Delete account", "account_deletion_request": "Account deletion request", "warning_message": "Do you want to contact our customer service team to proceed with your account deletion?", "not_safe_device_message": "Your device is rooted or running on an unsafe environment. For security reasons, this app cannot run on rooted devices or simulators. Please use a secure, non-rooted device to access this app.", "more_info": "If you wish to inquire about the lottery date of this equb or have any questions, please feel free to contact our customer service at +************. We are here to assist you.", "more_info_title": "More info", "date_of_birth": "Date of Birth", "please_select_date_of_birth": "Please select your date of birth", "you_must_be_above_18": "You must be above 18 years old", "select_main_equb": "Select Main Equb", "payment_not_allowed": "Payment not allowed", "payment_not_allowed_msg": "Payment cannot be made until the start date", "update_successful": "Update successful", "payment_options": "Payment Options", "no_winners": "No winners found", "email_already_taken": "Email already taken", "available_equbs": "Available Equbs"}