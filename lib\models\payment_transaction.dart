class PaymentTransaction {
  final int? id;
  final int? memberId;
  final int? equbId;
  final String? paymentType;
  final double? amount;
  final double? credit;
  final double? balance;
  final int? collector;
  final String? status;
  final DateTime? deletedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? paidDate;
  final String? transactionNumber;
  final String? note;
  final String? paymentProof;
  final String? msisdn;
  final DateTime? tradeDate;
  final String? tradeNo;
  final String? tradeStatus;

  PaymentTransaction({
    this.id,
    this.memberId,
    this.equbId,
    this.paymentType,
    this.amount,
    this.credit,
    this.balance,
    this.collector,
    this.status,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.paidDate,
    this.transactionNumber,
    this.note,
    this.paymentProof,
    this.msisdn,
    this.tradeDate,
    this.tradeNo,
    this.tradeStatus,
  });

  // Factory method for creating an instance from JSON
  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    return PaymentTransaction(
      id: json['id'] as int?,
      memberId: json['member_id'] as int?,
      equbId: json['equb_id'] as int?,
      paymentType: json['payment_type'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      credit: (json['creadit'] as num?)?.toDouble(),
      balance: (json['balance'] as num?)?.toDouble(),
      collector: json['collecter'] as int?,
      status: json['status'] as String?,
      deletedAt: json['deleted_at'] != null ? DateTime.parse(json['deleted_at']) : null,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      paidDate: json['paid_date'] != null ? DateTime.parse(json['paid_date']) : null,
      transactionNumber: json['transaction_number'] as String?,
      note: json['note'] as String?,
      paymentProof: json['payment_proof'] as String?,
      msisdn: json['msisdn'] as String?,
      tradeDate: json['tradeDate'] != null ? DateTime.parse(json['tradeDate']) : null,
      tradeNo: json['tradeNo'] as String?,
      tradeStatus: json['tradeStatus'] as String?,
    );
  }

  // Method to convert the instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'member_id': memberId,
      'equb_id': equbId,
      'payment_type': paymentType,
      'amount': amount,
      'creadit': credit,
      'balance': balance,
      'collecter': collector,
      'status': status,
      'deleted_at': deletedAt?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'paid_date': paidDate?.toIso8601String(),
      'transaction_number': transactionNumber,
      'note': note,
      'payment_proof': paymentProof,
      'msisdn': msisdn,
      'tradeDate': tradeDate?.toIso8601String(),
      'tradeNo': tradeNo,
      'tradeStatus': tradeStatus,
    };
  }
}
