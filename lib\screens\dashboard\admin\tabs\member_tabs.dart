// ignore_for_file: empty_catches, use_build_context_synchronously, depend_on_referenced_packages

import 'dart:async';

import 'package:ekub/models/ekub.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/equb_repos.dart';
import 'package:ekub/screens/dashboard/admin/tabs/automatic_equb_taker.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/screens/ui_kits/app_bar.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;

import '../../../../models/user.dart';
import '../../../../repository/member_repos.dart';
import '../../../../repository/user_repos.dart';
import '../../../../routs/shared.dart';
import '../../../settings/constant.dart';
import 'member_payments.dart';
import 'member_takers.dart';

class MemberTabs extends StatefulWidget {
  static const routeName = '/member_tabs';
  final MemberETabsArgs args;

  const MemberTabs({super.key, required this.args});

  @override
  State<MemberTabs> createState() => _MemberTabsState();
}

class _MemberTabsState extends State<MemberTabs> {
  Equb? equb;
  String rate = "";
  List<Equb>? items = [];
  bool proLoaded = false;
  bool isSubmitted = false;
  bool isLoading = false;
  bool loading = true;
  var myMenuItems = <String>['Payment'];
  late ThemeProvider themeProvider;
  final _formkey = GlobalKey<FormState>();
  final formkey = GlobalKey<FormState>();
  final _appBarKey = GlobalKey<FormState>();
  TextEditingController rateControl = TextEditingController();
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      gender: "loading",
      email: "loading",
      role: "loading",
      memberId: "loading");
  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _loadProfile();
    loadEqubs(false);
    rateControl = TextEditingController(text: widget.args.member.rating);
    super.initState();
  }

  _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());

    try {
      await auth.getUserData().then((value) => {
            setState(() {
              user = value;
              proLoaded = true;
            })
          });
    } catch (e) {}
  }

  void loadEqubs(bool refresh) async {
    setState(() {
      setState(() {
        loading = true;
      });
    });
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        setState(() {
          loading = false;
        });
        return;
      }

      await Provider.of<EqubDataProvider>(context, listen: false)
          .loadMemberEqub(context, 0, 1, widget.args.member.id!);
      widget.args.completed
          ? await Provider.of<MemberProvider>(context, listen: false)
              .loadCompletedMemberEqub(
                  context, widget.args.member.id!.toString())
          : await Provider.of<MemberProvider>(context, listen: false)
              .getPayments(context, widget.args.member.id!.toString(),
                  widget.args.equb!.id.toString(), 1, 0, refresh, false);
    } catch (e) {
      if (e is TimeoutException) {
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("time_out"),
            message:
                "${EkubLocalization.of(context)!.translate("timeout_message")}!",
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          loadEqubs(refresh);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      } else {
        // PanaraConfirmDialog.show(context,
        //     message: "Something went wrong! Please try again.",
        //     confirmButtonText: "Try Again",
        //     cancelButtonText: "Cancel", onTapConfirm: () {
        //   loadEqubs(refresh);
        //   Navigator.pop(context);
        // }, onTapCancel: () {
        //   Navigator.pop(context);
        // }, panaraDialogType: PanaraDialogType.error);
      }
    }

    setState(() {
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    items = widget.args.completed
        ? Provider.of<MemberProvider>(context, listen: false)
            .completedEqubs
            .equbs
        : Provider.of<EqubDataProvider>(context, listen: true).equbs.equbs;
    if (items!.isNotEmpty) {
      equb = items![widget.args.index];
    }

    var language = EkubLocalization.of(context)!;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
          backgroundColor: ColorProvider.backgroundColor,
          appBar: EkubAppBar(
            key: _appBarKey,
            title: equb!.equbType?.name ?? language.translate("equbs"),
            widgets: [
              IconButton(
                  onPressed: () {
                    loadEqubs(true);
                  },
                  icon: const Icon(Icons.refresh)),
            ],
          ),
          body: Column(
            children: [
              Container(
                  padding: const EdgeInsets.all(20),
                  decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(40),
                          bottomRight: Radius.circular(40))),
                  child: _tabBar(language)),
              Expanded(
                child: TabBarView(
                    physics: const ClampingScrollPhysics(),
                    key: _formkey,
                    children: [
                      loading
                          ? searchLoading()
                          : MemberPayments(
                              role: widget.args.role,
                              equb: equb,
                              index: widget.args.index,
                              member: widget.args.member,
                              remainingAmount: widget.args.remainingAmount),
                      loading
                          ? searchLoading()
                          : equb!.equbType!.type == "Manual"
                              ? EqubTakersScreen(
                                  member: widget.args.member,
                                  index: widget.args.index,
                                  isFromAutomatic: false,
                                  completed: widget.args.completed,
                                )
                              : AutomaticEqubTaker(
                                  completed: widget.args.completed,
                                  index: widget.args.index,
                                  equb: equb!,
                                  member: widget.args.member,
                                ),
                    ]),
              ),
            ],
          ),
          floatingActionButton: proLoaded && user.role == "admin"
              ? FloatingActionButton.extended(
                  elevation: 10,
                  label: Text(
                    // EkubLocalization.of(context)!.translate("rate_member"),
                    rateControl.text == "" ? "0" : "${rateControl.text} stars",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: fontBig,
                        color: themeProvider.getColor),
                  ),
                  icon: const Icon(
                    Icons.star,
                    color: Colors.orange,
                  ),
                  onPressed: () {
                    showModalBottomSheet(
                        elevation: 5,
                        backgroundColor: Colors.transparent,
                        isScrollControlled: true,
                        context: context,
                        builder: (BuildContext bc) {
                          return StatefulBuilder(builder: (context, setState) {
                            return SingleChildScrollView(
                              child: Container(
                                height: 500,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(25.0),
                                    topRight: Radius.circular(25.0),
                                  ),
                                ),
                                child: Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 30),
                                  child: Column(
                                    children: [
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      Text(
                                        EkubLocalization.of(context)!
                                            .translate("rate_title"),
                                        style: const TextStyle(
                                          fontSize: fontMedium,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 15,
                                      ),
                                      Form(
                                          key: formkey,
                                          autovalidateMode: isSubmitted
                                              ? AutovalidateMode
                                                  .onUserInteraction
                                              : AutovalidateMode.disabled,
                                          child: Container(
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 20),
                                              child: rateField(
                                                  context, setState))),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      GestureDetector(
                                        onTap: () async {
                                          setState(() {
                                            isSubmitted = true;
                                          });
                                          if (formkey.currentState!
                                              .validate()) {
                                            if (!await InternetConnectivity()
                                                .checkInternetConnectivty(
                                                    context, true)) {
                                              return;
                                            } else {
                                              setState(() {
                                                isLoading = true;
                                              });
                                              rateMember();
                                            }
                                          }
                                          return;
                                        },
                                        child: Container(
                                          height: 50,
                                          alignment: Alignment.center,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 20, vertical: 20),
                                          decoration: BoxDecoration(
                                              color: themeProvider.getColor,
                                              borderRadius:
                                                  BorderRadius.circular(18)),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 10),
                                            child: isLoading
                                                ? const Center(
                                                    child:
                                                        CircularProgressIndicator(
                                                      color: Colors.white,
                                                    ),
                                                  )
                                                : Text(
                                                    EkubLocalization.of(
                                                            context)!
                                                        .translate("rate"),
                                                    style: buttonText),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          });
                        });
                  })
              : null),
    );
  }

  TextFormField rateField(BuildContext context, StateSetter setState) {
    return TextFormField(
        autofocus: true,
        style: lableStyle,
        controller: rateControl,
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
            border: const OutlineInputBorder(
                borderSide: BorderSide(style: BorderStyle.solid)),
            prefixIcon: const Icon(
              Icons.star,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: const BorderSide(color: Colors.white),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: BorderSide(color: themeProvider.getColor),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: Colors.red,
              ),
              borderRadius: BorderRadius.circular(18),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            fillColor: Colors.grey[100],
            filled: true,
            labelText: EkubLocalization.of(context)!.translate("rate"),
            labelStyle: lableStyle),
        onChanged: (value) {
          setState(() {
            rate = value;
          });
        },
        validator: (value) {
          if (value!.isEmpty) {
            return EkubLocalization.of(context)!.translate("rate_empty");
          }
          if (double.parse(value) < 0 || double.parse(value) > 10) {
            return EkubLocalization.of(context)!.translate("rate_lenght");
          }
          return null;
        });
  }

  _tabBar(EkubLocalization language) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      height: 50,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: ColorProvider.backgroundColor),
      child: TabBar(
        labelStyle:
            const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        unselectedLabelColor: Colors.black,

        indicatorWeight: 3.0,
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: BoxDecoration(
          color: themeProvider.getColor,
          borderRadius: BorderRadius.circular(10),
        ),
        // dividerColor: Colors.black,
        dividerHeight: 0,
        indicatorPadding: const EdgeInsets.symmetric(horizontal: 0),
        // unselectedLabelStyle: TextStyle(
        //     color: themeProvider.getColor, fontWeight: FontWeight.bold),
        tabs: [
          Tab(
            text: language.translate("payment"),
          ),
          Tab(
            text: language.translate("lottery"),
          )
        ],
      ),
    );
  }

  rateMember() async {
    try {
      await Provider.of<MemberProvider>(context, listen: false)
          .rateMember(context, widget.args.member.id!.toString(),
              rate.toString().toString())
          .then((value) {
        setState(() {
          isLoading = false;
        });
        if (value["code"] == 200) {
          PanaraInfoDialog.show(
            context,
            message: value["message"],
            buttonText: EkubLocalization.of(context)!.translate("okay"),
            imagePath: successDialogIcon,
            onTapDismiss: () {
              Navigator.pop(context);

              Navigator.pop(context);
            },
            panaraDialogType: PanaraDialogType.success,
          );
        } else {
          PanaraInfoDialog.show(
            context,
            message: value["message"],
            buttonText: EkubLocalization.of(context)!.translate("okay"),
            imagePath: errorDialogIcon,
            onTapDismiss: () {
              Navigator.pop(context);

              Navigator.pop(context);
            },
            panaraDialogType: PanaraDialogType.error,
          );
        }
      });
    } catch (e) {
      if (e is TimeoutException) {
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("time_out"),
            message:
                "${EkubLocalization.of(context)!.translate("timeout_message")}!",
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () async {
          Navigator.pop(context);
          await Provider.of<MemberProvider>(context, listen: false).rateMember(
              context,
              widget.args.member.id!.toString(),
              rate.toString().toString());
        }, onTapCancel: () {
          setState(() {
            isLoading = false;
          });
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              message: e.toString(),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () async {
            Navigator.pop(context);
            await Provider.of<MemberProvider>(context, listen: false)
                .rateMember(context, widget.args.member.id!.toString(),
                    rate.toString().toString());
          }, onTapCancel: () {
            setState(() {
              isLoading = false;
            });
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }
}
