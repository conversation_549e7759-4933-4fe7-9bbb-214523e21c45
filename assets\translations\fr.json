{"title": "Virtual Equb", "collected_today": "<PERSON><PERSON><PERSON><PERSON> <PERSON> qabame", "today": "Har'a", "daily": "<PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON> to<PERSON>n", "monthly": "Ji'a ji'aan", "yearly": "<PERSON><PERSON><PERSON><PERSON> waggaa<PERSON><PERSON>", "biweekly": "<PERSON><PERSON>", "collected": "<PERSON><PERSON><PERSON> q<PERSON>", "expected": "Kan eegame", "home": "<PERSON><PERSON>", "subscribe_ekub": "Equb Subscribe <PERSON><PERSON><PERSON>", "join_ekub": "Equb Subscribe <PERSON><PERSON><PERSON>", "join": "Join", "add_ekub": "<PERSON><PERSON><PERSON> <PERSON>", "my_profile": "<PERSON><PERSON><PERSON><PERSON>", "edit_profile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ekubs": "<PERSON><PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON>", "etb": "ETB", "male": "<PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "members": "<PERSON><PERSON><PERSON><PERSON>", "remaining_payment": "<PERSON><PERSON><PERSON><PERSON><PERSON> hafe", "remaining_lottery_date": "<PERSON><PERSON> lootarii", "remaining_days": "<PERSON><PERSON>i", "remaining_amount": "Remaining Amount", "paid_with": "<PERSON><PERSON><PERSON><PERSON><PERSON> waliin", "paid_by": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "days": "Guyyoota", "started": "J<PERSON><PERSON>bame", "ends": "<PERSON><PERSON><PERSON>", "paid": "<PERSON><PERSON><PERSON><PERSON>", "of": "kan", "active": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "payment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lottery": "<PERSON><PERSON><PERSON><PERSON>", "not_paid": "Hanga ammaatti hin kaffala<PERSON>in!", "select_payment_method": "Mala", "method_empty": "Ma<PERSON>o mala filadhu", "cash": "Callaa", "check": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bank transfer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> baan<PERSON>i", "other": "Kan biraa", "pay": "Pay", "proceed": "<PERSON><PERSON> fufi", "no_record": "<PERSON><PERSON><PERSON><PERSON> hin argamne", "payment_history": "<PERSON><PERSON><PERSON>", "to": "<PERSON><PERSON>", "balance": "<PERSON><PERSON><PERSON><PERSON>", "credit": "Liqaa", "no_payment_history": "<PERSON><PERSON><PERSON> ka<PERSON>tii hin argamne", "round": "Marsaa", "rote": "<PERSON><PERSON>", "start_date": "<PERSON><PERSON><PERSON>", "end_date": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "terms": "Ha<PERSON> fi Dam<PERSON>wwan irratti", "accept_terms_conditions": "Ha<PERSON> fi tumaalee irratti walii gala", "no_terms": "<PERSON><PERSON> ammaa kana haalawwanii fi dambiiwwan jiran hin jiran", "subscribe": "Subscribe", "user_information": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "Ga'ee", "phone_number": "Lakkoofsa Bilbilaa", "user_settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select_theme": "<PERSON>", "developed_by": "<PERSON><PERSON>", "vintage_technologies": "Vintage Technologies", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skip": "<PERSON><PERSON> da<PERSON>", "address_detail": "<PERSON><PERSON><PERSON>", "submit": "GALCHUU", "selected_city": "<PERSON><PERSON><PERSON><PERSON> filat<PERSON>", "registration": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "login_subtitle": "Please sign in to continue", "logout": "Ba'uu", "register": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forget_password": "Pa<PERSON><PERSON>", "confirm_code": "<PERSON><PERSON><PERSON>", "reset_password": "<PERSON><PERSON>", "change_password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update_account": "Akkaawuntii Fooyyessi", "full_name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>", "sub_city": "<PERSON><PERSON><PERSON><PERSON> xiqqaa", "woreda": "Wordaa", "house_number": "Lakkoofsa Manaa", "specific_location": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dhi<PERSON>a", "change": "JIJJIIRUU", "old_password": "<PERSON><PERSON>", "new_password": "<PERSON><PERSON>", "confirm_password": "<PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "blocked": "<PERSON><PERSON><PERSON>", "blocked_msg": "A<PERSON>aw<PERSON>iin keessan sababa yaalii sirrii hin taane dachaa ta'een yeroodhaaf cufameera Mee yeroo eegaa daqiiqaa 5 booda irra deebi'aa yaalaa", "unlocked": "<PERSON>n <PERSON><PERSON><PERSON><PERSON>", "unlocked_msg": "Akkaaw<PERSON>iin kee milkaa'inaan banameera Maaloo mirkaneessitoota sirrii fayyadamu<PERSON> seenuuf yaali", "don't_have_account": "Akkaawuntii Hin <PERSON>a", "sign_up": "Mallatt<PERSON><PERSON><PERSON>", "error_load_equbtype": "<PERSON><PERSON><PERSON><PERSON> fe'uu hin dande<PERSON>, <PERSON><PERSON><PERSON> irra deebi'ii yaali ", "error_load_data": "<PERSON><PERSON><PERSON> fe'uu hin dandeenye, maaloo irra deebi'ii yaali", "verify_phone_number": "Lakkoofsa Bilbilaa Mirkaneessi", "verify_phone_message": "Lakkoofsa bilbila keessan nuuf kennaa akka nuti lakkoofsa keessan mirkanees<PERSON>uf koodii mirkaneessaa isiniif erginuuf", "send_code": "KOODII ERGAA", "have_account": "<PERSON><PERSON><PERSON>", "sign_in": "Sign In", "unknown_phone": "Lakkoofsi bilbilaa kenname sirna keenya keessatti hin galmaa'u", "registered_phone": "Lakkoofsa bilbilaa kenname waliin akkaawuntiin walqabatu waan jiru fakkaata", "enter_code": "<PERSON><PERSON><PERSON>", "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Resending": "<PERSON><PERSON> deeb<PERSON>'ee erguu", "resend": "<PERSON>rra deebi'ii ergi", "code_not_sent": "<PERSON><PERSON><PERSON> hin arganne", "reset": "Reset", "add_ekub_type": "<PERSON><PERSON><PERSON><PERSON>", "add_member": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "select_rote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> filadhu", "select_type": "<PERSON><PERSON><PERSON><PERSON> filadhu", "remark": "<PERSON><PERSON>", "add": "IDA'UU", "automatic": "<PERSON><PERSON><PERSON> kan hojjetu", "manual": "<PERSON>n ha<PERSON><PERSON>i", "select_city": "<PERSON><PERSON><PERSON><PERSON> filadhu", "add_new_member": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>a da<PERSON>i", "edit_member": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "confirm_delete": "Haqa<PERSON>u sana itti fufuu akka barbaaddu mirkanee<PERSON>?", "edit_ekub_type": "<PERSON><PERSON><PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "go_back": "<PERSON><PERSON><PERSON> deebi/'uu ni barba<PERSON>daa?", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "equbs": "<PERSON><PERSON><PERSON><PERSON>", "otp_title": "<PERSON><PERSON><PERSON>", "otp_message": "Koodii dijiitii 6 itti ergine galchi", "resend_otp": "<PERSON><PERSON><PERSON> irra deebi'ee erguu gaa<PERSON>dhu", "login_failed": "Seensiin <PERSON>!", "invalid_otp_message": "<PERSON><PERSON>ii sirrii hin taane Maaloo koodii dachaa lama ilaalii irra deebi'ii yaali", "resending_code": "<PERSON><PERSON><PERSON> I<PERSON>ee <PERSON>", "request_code": "KOODII GAAFFII", "time_out": "Gaaffiin yeroo dheeraa fudhate! Maaloo irra deebi'ii yaalaa", "timeout_message": "Gaaffiin yeroo dheeraa fudhate! Maaloo irra deebi'ii yaalaa", "try_again": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "Dogoggora", "winner_msg": "Baga gammaddan! Mo'ataa har'aa ta'uun filatam<PERSON>ittu", "select_city_error": "<PERSON><PERSON><PERSON> maga<PERSON>a filadhu", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error_message": "Wanti tokko dogoggora ta'e! Maaloo irra deebi'ii yaalaa", "select_ekub_type": "<PERSON><PERSON><PERSON><PERSON>", "type_loading": "Equb types loading Mee eegaa", "lottery_date": "<PERSON><PERSON><PERSON>", "subscribe_ekub_msg": "<PERSON><PERSON><PERSON><PERSON>aan subscribe gootani<PERSON>u", "success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user_credential": "<PERSON><PERSON><PERSON><PERSON>", "winner": "<PERSON><PERSON><PERSON><PERSON><PERSON> har'aa", "loading": "<PERSON><PERSON><PERSON><PERSON> jira", "view_detail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lottery_date_is": "<PERSON><PERSON><PERSON> lootarii irra jira", "details": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unavailable": "<PERSON>n a<PERSON><PERSON>", "confirm_payment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm_payment_msg": "Itti fufuu akka barba<PERSON>du mirka<PERSON>?", "payment_unsuccessfull": "<PERSON><PERSON><PERSON><PERSON><PERSON> kun milkaa'inaan hin raawwatamne", "payment_successful": "Baga gammaddan! Kaffaltaniittu", "chapa": "<PERSON><PERSON>", "enter_name": "Maqaa ykn lakkoofsa bilbilaa galchaa", "confirm_logout": "Ba'uu barba<PERSON>daa?", "no_subscribtion_found_member": "Misee<PERSON>i kun hanga ammaatti <PERSON><PERSON><PERSON> kami<PERSON>yuu hin mallatteessine", "no_subscribtion_found_admin": "<PERSON><PERSON> amma<PERSON>i <PERSON><PERSON><PERSON> subscribe hin goone", "inactive_user": "<PERSON><PERSON><PERSON><PERSON><PERSON>n keessan yeroo ammaa raggaasifamuu eegaa jira Akkuma mirkanaa'een beeksisni ergaa gabaabaa siif dhufa", "unable_load_member": "Miseensota fe'uu hin dandeenye! Maaloo irra deebi'ii yaalaa", "no_member_found": "<PERSON>a ammaatti miseensi galmaa'e hin jiru", "no_equbtypes_found": "<PERSON>a ammaatti gosti equb hin galmoofne", "check_from": "<PERSON><PERSON><PERSON> sa<PERSON>i", "check_detail": "<PERSON><PERSON> b<PERSON>'<PERSON><PERSON> il<PERSON>", "no_connection": "<PERSON><PERSON><PERSON><PERSON><PERSON> hin argamne", "connection_msg": "<PERSON>e qunnamtii interneetii k<PERSON>", "internet_connection": "<PERSON><PERSON><PERSON> interneetii tasgabbaa'aa qabaachuu keessan mirka<PERSON><PERSON>a irra deebi'aa yaalaa", "email_empty_member": "<PERSON><PERSON>", "email_empty": "<PERSON><PERSON>", "email_invalid": "<PERSON><PERSON><PERSON>", "name_empty": "<PERSON><PERSON><PERSON>", "name_empty_member": "<PERSON><PERSON><PERSON>", "name_invalid": "<PERSON><PERSON><PERSON>", "name_length": "<PERSON><PERSON><PERSON><PERSON>utuu 50 gadi ta'uu qaba", "phone_empty": "Lakkoofsa Bilbilaa Galchaa", "phone_empty_member": "Ma<PERSON>o Lakkoofsa Bilbilaa Keessan Gal<PERSON>a", "phone_length": "<PERSON><PERSON><PERSON><PERSON>sa Bilbilaa 9 gadi ta'uu hin qabu", "valid_field": "<PERSON><PERSON>", "field_length": "<PERSON><PERSON><PERSON><PERSON> 50 gadi ta'uu qaba", "subscribe_equb_msg": "<PERSON><PERSON><PERSON><PERSON><PERSON> subscribe gootaniittu!", "password_empty": "<PERSON><PERSON> <PERSON><PERSON>", "password_length": "<PERSON><PERSON><PERSON><PERSON> jecha icci<PERSON>i 6 gadi ta'uu hin qabu", "password_limit": "<PERSON><PERSON><PERSON><PERSON> jecha icciitii 25 ol ta'uu hin qabu", "confirm_password_empty": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "mismatch_password": "<PERSON><PERSON> icciitii galfame wal hin simne", "code_empty": "<PERSON><PERSON> Galchi", "code_length": "<PERSON><PERSON><PERSON><PERSON> kood<PERSON> 6 gadi ta'uu hin qabu", "select_ekub_type_error": "<PERSON><PERSON><PERSON> gosa equb filadhu", "automatic_equb_msg1": "<PERSON>n Automatic equb dha", "automatic_equb_msg2": "<PERSON><PERSON><PERSON><PERSON> maallaqa galchuu qofatu si irraa eegama subscribe gochuuf", "and": "fi", "next": "KAN ITTI AANU", "starts": "Jalqaba", "upload_image": "Silippii kaffaltii ykn suuraa iskiriinii olk<PERSON>'i", "image_empty_title": "Ragaa kaffaltii barba<PERSON>!", "image_empty_msg": "Mirkaneessuuf slip ykn screenshot ka<PERSON><PERSON><PERSON><PERSON> keessan nuuf kennaa", "manual_equb_msg": "Kun gosa <PERSON> equb ti <PERSON>, sarara yeroo fi hanga maallaqa maallaqa galchuuf filachuu qofa si irraa eegama", "timeline_error": "<PERSON><PERSON><PERSON> sarara yeroo filadhu", "payment_error_title": "Mirkaneessuuf slip ykn screenshot ka<PERSON><PERSON><PERSON><PERSON> keessan nuuf kennaa", "payment_error_msg": "Mirkaneessuuf slip ykn screenshot ka<PERSON><PERSON><PERSON><PERSON> keessan nuuf kennaa", "select_timeline": "<PERSON><PERSON> filadhu", "select_timeline_error": "<PERSON><PERSON><PERSON> sarara yeroo filadhu", "okay": "<PERSON><PERSON>", "general_settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "passed": "<PERSON><PERSON> jira", "rate": "Sadarkaa", "rate_empty": "<PERSON>e sad<PERSON>aa gal<PERSON>a", "rate_lenght": "Sadarkaan 0 fi 10 giddu<PERSON>i ta’uu qaba.", "rate_member": "Sadarkaa itti kenni", "rate_title": "Miseensa kana akka<PERSON>ti mad<PERSON>?", "ekub_empty": "<PERSON><PERSON><PERSON><PERSON> xumurame hin argamne", "inactive_ekub": "<PERSON><PERSON><PERSON><PERSON> kun activi mitti", "completed_ekub": "Qaruuraa xumurame", "completed": "<PERSON><PERSON><PERSON><PERSON>", "ongoing": "itti fufee jira", "contact_us": "<PERSON>u qunnamaa", "call_us": "<PERSON><PERSON><PERSON>", "inactive_member": "Miseensi kun activi mitti", "delete_account": "Akkaawuntii haquu", "account_deletion_request": "<PERSON><PERSON><PERSON><PERSON> herrega haquu", "warning_message": "<PERSON>are<PERSON> taja<PERSON> ma<PERSON>ootaa keenya qunnamuun akkaawuntii keessan haquu itti fufuu barbaadduu?", "not_safe_device_message": "Your device is rooted or running on an unsafe environment. For security reasons, this app cannot run on rooted devices or simulators. Please use a secure, non-rooted device to access this app.", "more_info": "If you wish to inquire about the lottery date of this equb or have any questions, please feel free to contact our customer service at +************. We are here to assist you.", "more_info_title": "More info", "enter_age": "Enter your age", "age_required": "Age is required", "invalid_age": "Invalid age", "must_be_18": "You must be 18 or older", "please_select_date_of_birth": "<PERSON><PERSON><PERSON> dhaloota kee filachuu", "select_main_equb": "<PERSON><PERSON><PERSON>i guddaa filadhu", "payment_not_allowed": "<PERSON><PERSON><PERSON><PERSON> hin e<PERSON><PERSON>ne", "payment_not_allowed_msg": "<PERSON>a guy<PERSON>a jalqa<PERSON> e<PERSON>, ka<PERSON><PERSON><PERSON><PERSON> hin raawwatamu", "update_successful": "<PERSON><PERSON><PERSON><PERSON><PERSON> guutuu ta'ee jira", "no_passed_equb": "<PERSON><PERSON><PERSON><PERSON> darbee hin jiru", "unpaid": "<PERSON>n ka<PERSON>", "passed_equb": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "payment_options": "<PERSON><PERSON><PERSON><PERSON>", "email_already_taken": "<PERSON><PERSON>"}