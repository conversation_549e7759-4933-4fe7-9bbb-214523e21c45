// ignore_for_file: use_build_context_synchronously

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/repository/language.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

import '../exports/screens.dart';

class LanguageSelectionScreen extends StatefulWidget {
  const LanguageSelectionScreen({super.key});

  @override
  State<LanguageSelectionScreen> createState() =>
      _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  late ThemeProvider themeProvider;
  List<String> languages = ["English", "አማርኛ", "ትግሪኛ", "Oromiffa", "Somali"];

  String selectedLanguage = "Language";
  String language = "";
  final dropdownControl = TextEditingController();
  AppLanguage appLanguage = AppLanguage();
  @override
  void initState() {
    super.initState();
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    appLanguage = Provider.of<AppLanguage>(context, listen: false);

    fetchLanguage();
  }

  fetchLanguage() async {
    await appLanguage.fetchLocale();
    language = appLanguage.appLocale.languageCode.toLowerCase();

    if (language == "fr") {
      setState(() {
        selectedLanguage = "Oromiffa";
      });
    } else if (language == "es") {
      setState(() {
        selectedLanguage = "ትግሪኛ";
      });
    } else if (language == "am") {
      setState(() {
        selectedLanguage = "አማርኛ";
      });
    } else if (language == "tl") {
      setState(() {
        selectedLanguage = "Somali";
      });
    } else if (language == "en") {
      setState(() {
        selectedLanguage = "English";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    appLanguage = Provider.of<AppLanguage>(context, listen: true);

    return Scaffold(
      backgroundColor: Colors.grey.shade300,
      body: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * .8,
          height: 300,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              const Text(
                'Select Language',
                style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 25),
              ),
              Container(
                margin: const EdgeInsets.only(right: 10),
                width: MediaQuery.of(context).size.width * .5,
                child: CustomDropdown<String>(
                  onChanged: (value) {
                    setState(() {
                      selectedLanguage = value!;
                      if (selectedLanguage == "English") {
                        appLanguage.changeLanguage(const Locale("en", "US"));
                      } else if (selectedLanguage == "አማርኛ") {
                        appLanguage.changeLanguage(const Locale("am", "ET"));
                      } else if (selectedLanguage == "Oromiffa") {
                        appLanguage.changeLanguage(const Locale("fr"));
                      } else if (selectedLanguage == "Somali") {
                        appLanguage.changeLanguage(const Locale("tl"));
                      } else if (selectedLanguage == "ትግሪኛ") {
                        appLanguage.changeLanguage(const Locale("es"));
                      }
                    });
                  },
                  hintText: selectedLanguage,
                  decoration: CustomDropdownDecoration(
                    closedBorder: Border.all(
                      color: Colors.grey.shade400,
                    ),
                    hintStyle: TextStyle(
                      color: themeProvider.getColor,
                      fontWeight: FontWeight.normal,
                      fontSize: fontMedium,
                    ),
                    closedSuffixIcon: Icon(
                      Icons.language,
                      color: bodyTextColor,
                      size: 25,
                    ),
                  ),
                  // fillColor: Colors.grey.shade100,
                  // hintText: selectedLanguage,
                  //
                  // hintStyle: TextStyle(
                  //     color: themeProvider.getColor, fontWeight: boldFont),
                  // selectedStyle: TextStyle(
                  //     color: themeProvider.getColor, fontWeight: boldFont),
                  items: languages,
                  // controller: dropdownControl,
                  excludeSelected: true,
                ),
              ),
              InkWell(
                onTap: () {
                  _setLanguageAndNavigate(context, selectedLanguage);
                },
                child: Container(
                  width: 200,
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  decoration: BoxDecoration(
                      color: themeProvider.getColor,
                      borderRadius: BorderRadius.circular(10)),
                  child: const Text(
                    "Continue",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: fontMedium,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  _setLanguageAndNavigate(BuildContext context, String language) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', language); // Store language preference
    Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => kIsWeb
                ? LoginScreen(args: LoginScreenArgs(isOnline: true))
                : LoginScreen(args: LoginScreenArgs(isOnline: true))));
  }
}
