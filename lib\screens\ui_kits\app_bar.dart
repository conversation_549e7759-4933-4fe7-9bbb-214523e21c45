import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/device.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../themes/ThemeProvider.dart';

class EkubAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Color backgroundColor = Colors.red;
  final String title;
  final TabBar? bottom;
  final AppBar? appBar;
  final List<Widget> widgets;

  /// you can add more fields that meet your needs

  const EkubAppBar(
      {required Key key,
      this.bottom,
      this.appBar,
      required this.title,
      required this.widgets})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    var themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    return AppBar(
      iconTheme: IconThemeData(color: themeProvider.getColor),
      elevation: 0,
      backgroundColor: Colors.white,
      bottom: bottom,
      title: Text(
        title,
        style: TextStyle(
            color: themeProvider.getColor,
            fontSize: fontMedium,
            fontWeight: FontWeight.bold),
      ),
      actions: [
        for (var iconButton in widgets)
          Padding(
            padding: const EdgeInsets.all(3.0),
            child: iconButton,
          )
      ],
    );
  }

  @override
  Size get preferredSize {
    if (appBar != null) {
      return Size.fromHeight(appBar?.preferredSize.height ?? 0.0);
    } else {
      return Size.fromHeight(Device.appBarSize().height);
    }
  }
}
