import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

contactUsWidget(BuildContext cxt) {
  return showModalBottomSheet(
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      context: cxt,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(40), topRight: Radius.circular(40)),
            ),
            height: 250,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
            child: Column(children: [
              Row(
                children: [
                  Text(
                    EkubLocalization.of(context)!.translate("contact_us"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontBig),
                  ),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          openSocialMedia(websiteLink);
                        },
                        child: reactions("assets/icons/link.svg", "", false),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          email();
                        },
                        child: reactions("assets/icons/mail.svg", "", false),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          openSocialMedia(facebookContact);
                        },
                        child:
                            reactions("assets/icons/facebookk.svg", "", false),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          openSocialMedia(linkedinContact);
                        },
                        child:
                            reactions("assets/icons/linkedinn.svg", "", false),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          openSocialMedia(tiktokContact);
                        },
                        child: reactions("assets/icons/tiktok.svg", "", false),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          openSocialMedia(telegramContact);
                        },
                        child: reactions("assets/icons/send.svg", "", false),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          openSocialMedia(instagramContact);
                        },
                        child:
                            reactions("assets/icons/instagramm.svg", "", false),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          openSocialMedia(twitterContact);
                        },
                        child: reactions("assets/icons/twitter.svg", "", false),
                      ),
                    ],
                  )
                ],
              ),
              InkWell(
                onTap: () => makePhoneCall(),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 10, top: 15),
                  alignment: Alignment.center,
                  height: 56,
                  decoration: BoxDecoration(
                      color: ColorProvider.primary,
                      borderRadius: const BorderRadius.all(Radius.circular(5))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.phone_outlined,
                        color: Colors.white,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Text(
                        EkubLocalization.of(context)!.translate("call_us"),
                        style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: fontMedium),
                      ),
                    ],
                  ),
                ),
              )
            ]),
          );
        });
      });
}

reactions(String image, String title, bool selected) {
  return Column(children: [
    Container(
      height: 35,
      width: 40,
      decoration: BoxDecoration(
        color: selected ? ColorProvider.primary : Colors.grey.shade300,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: SvgPicture.asset(
          height: 20,
          width: 20,
          image,
          colorFilter: ColorFilter.mode(
              selected ? Colors.white : ColorProvider.primary, BlendMode.srcIn),
        ),
      ),
    ),
    Text(
      title,
      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: fontSmall),
    ),
  ]);
}
