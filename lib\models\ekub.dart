import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'equb_type.dart';

class Equb {
  int? id;
  String? memberId;
  String? equbTypeId;
  String? amount;
  String? totalAmount;
  String? startDate;
  String? lotteryDate;
  String? endDate;
  String? status;
  String? createdAt;
  String? updatedAt;
  EqubType? equbType;
  Payments? payments;
  EqubTakers? equbTakers;

  String? totalPayment;
  String? remainingPayment;
  String? remainingLotteryDate;
  Equb(
      {this.id,
      this.memberId,
      this.equbTypeId,
      this.totalAmount,
      this.startDate,
      this.endDate,
      this.lotteryDate,
      this.amount,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.equbType,
      this.payments,
      this.totalPayment,
      this.remainingPayment,
      this.remainingLotteryDate,
      this.equbTakers});
  factory Equb.fromJson(Map<String, dynamic> json) {
    try {
      // No need to validate json == null in Dart 2.12+ with sound null safety
      // The parameter is already non-nullable

      // Parse equbType with null check
      EqubType? equbType;
      if (json["equb_type"] != null) {
        try {
          equbType = EqubType.fromJson(json["equb_type"]);
        } catch (e) {
          debugPrint("Error parsing equb_type: $e");
          equbType = null;
        }
      }

      // Parse payments with null check
      Payments? payments;
      if (json["payments"] != null) {
        try {
          payments = Payments.fromStringObject(json["payments"]);
        } catch (e) {
          debugPrint("Error parsing payments: $e");
          payments = null;
        }
      }

      // Parse equbTakers with null check
      EqubTakers? equbTakers;
      if (json["equb_takers"] != null) {
        try {
          equbTakers = EqubTakers.fromStringObject(json["equb_takers"]);
        } catch (e) {
          debugPrint("Error parsing equb_takers: $e");
          equbTakers = null;
        }
      }

      return Equb(
          id: json["id"] is String ? int.tryParse(json["id"]) : json["id"],
          memberId: json["member_id"]?.toString(),
          equbTypeId: json["equb_type_id"]?.toString(),
          totalAmount: json['total_amount']?.toString(),
          amount: json['amount']?.toString(),
          startDate: json['start_date']?.toString(),
          endDate: json['end_date']?.toString(),
          lotteryDate: json['lottery_date']?.toString(),
          status: json["status"]?.toString(),
          createdAt: json["created_at"]?.toString(),
          updatedAt: json["updated_at"]?.toString(),
          totalPayment: json["total_payment"]?.toString(),
          remainingPayment: json["remaining_payment"]?.toString(),
          remainingLotteryDate: json["remaining_lottery_date"]?.toString(),
          equbType: equbType,
          payments: payments,
          equbTakers: equbTakers);
    } catch (e, stackTrace) {
      debugPrint("Error parsing Equb from JSON: $e");
      debugPrint("Stack trace: $stackTrace");
      debugPrint("JSON data: $json");
      rethrow;
    }
  }
}

class Equbs {
  List<Equb>? equbs;
  int? totalMember;
  Equbs({required this.totalMember, required this.equbs});

  Equbs.fromJson(List<Equb> parsedJson, this.totalMember) {
    equbs = parsedJson;
  }
  Equbs.fromStringObject(List<dynamic> parsedJson) {
    equbs = parsedJson.map((i) => Equb.fromJson(i)).toList();
  }
  String toJson() {
    String data = jsonEncode(equbs?.map((i) => i.toString()).toList());
    return data;
  }
}

class Payment {
  int? id;
  String? memberId;
  String? equbId;
  String? amount;
  String? paymentType;
  String? credit;
  String? balance;
  String? collector;

  String? status;
  String? createdAt;
  String? updatedAt;
  Payment(
      {this.id,
      this.memberId,
      this.equbId,
      this.amount,
      this.paymentType,
      this.credit,
      this.collector,
      this.balance,
      this.status,
      this.createdAt,
      this.updatedAt});
  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
        id: json["id"],
        memberId: json["member_id"]?.toString() ?? '',
        equbId: json["equb_id"]?.toString() ?? '',
        paymentType: json['payment_type']?.toString() ?? '',
        amount: json['amount']?.toString() ?? '0',
        credit: json['creadit']?.toString() ?? '0',
        collector: json['collecter'] != null
            ? json['collecter']['name']?.toString() ?? 'Unknown'
            : 'Unknown',
        balance: json['balance']?.toString() ?? '0',
        status: json["status"]?.toString() ?? 'pending',
        createdAt: json["created_at"]?.toString() ?? '',
        updatedAt: json["updated_at"]?.toString() ?? '');
  }
  // This method is not used and was incorrectly implemented
  // It should return a list of Payment objects, not Equb objects
  static List<Payment> fromStringObject(List<dynamic> parsedJson) {
    return parsedJson.map((i) => Payment.fromJson(i)).toList();
  }
}

class Payments {
  List<Payment>? payments;
  Payments({required this.payments});

  Payments.fromJson(List<Payment> parsedJson) {
    payments = parsedJson;
  }
  Payments.fromStringObject(List<dynamic> parsedJson) {
    payments = Payment.fromStringObject(parsedJson);
  }
  String toJson() {
    String data = jsonEncode(payments?.map((i) => i.toString()).toList());
    return data;
  }
}

class EqubTaker {
  int? id;
  String? memberId;
  String? equbId;
  String? lotteryAmount;
  String? paymentType;
  String? remainingAmount;
  String? paidBy; /*
  String? totalPayment;
  String? remainingPayment;*/
  String? chequeAmount;
  String? chequeBankName;
  String? chequeDescription;

  String? status;
  String? createdAt;
  String? updatedAt;
  EqubTaker(
      {this.id,
      this.memberId,
      this.equbId,
      this.lotteryAmount,
      this.paymentType,
      this.remainingAmount,
      //this.totalPayment,
      this.paidBy,
      this.chequeAmount,
      this.chequeBankName,
      this.chequeDescription,
      this.status,
      this.createdAt,
      this.updatedAt});
  factory EqubTaker.fromJson(Map<String, dynamic> json) {
    return EqubTaker(
        id: json["id"],
        memberId: json["member_id"]?.toString() ?? '',
        equbId: json["equb_id"]?.toString() ?? '',
        paymentType: json['payment_type']?.toString() ?? '',
        lotteryAmount: json['amount']?.toString() ?? '0',
        remainingAmount: json['remaining_amount']?.toString() ?? '0',
        //totalPayment: json['collector'],
        chequeAmount: json['cheque_amount']?.toString() ?? '0',
        chequeBankName: json['cheque_bank_name']?.toString(),
        chequeDescription: json['cheque_description']?.toString(),
        paidBy: json['paid_by']?.toString() ?? 'Unknown',
        status: json["status"]?.toString() ?? 'pending',
        createdAt: json["created_at"]?.toString() ?? '',
        updatedAt: json["updated_at"]?.toString() ?? '');
  }
  // This method is not used and was incorrectly implemented
  // It should return a list of EqubTaker objects, not Equb objects
  static List<EqubTaker> fromStringObject(List<dynamic> parsedJson) {
    return parsedJson.map((i) => EqubTaker.fromJson(i)).toList();
  }
}

class EqubTakers {
  List<EqubTaker>? equbTakers;
  EqubTakers({required this.equbTakers});

  EqubTakers.fromJson(List<EqubTaker> parsedJson) {
    equbTakers = parsedJson;
  }
  EqubTakers.fromStringObject(List<dynamic> parsedJson) {
    equbTakers = EqubTaker.fromStringObject(parsedJson);
  }
  String toJson() {
    String data = jsonEncode(equbTakers?.map((i) => i.toString()).toList());
    return data;
  }
}

class TodayLottery {
  int? id;
  String? memberId;
  String? equbTypeId;
  String? amount;
  String? totalAmount;
  String? startDate;
  String? lotteryDate;
  String? endDate;
  String? status;
  String? createdAt;
  String? updatedAt;
  EqubType? equbType;
  Payments? payments;
  EqubTakers? equbTakers;

  String? totalPayment;
  String? remainingPayment;
  String? remainingLotteryDate;
  TodayLottery(
      {this.id,
      this.memberId,
      this.equbTypeId,
      this.totalAmount,
      this.startDate,
      this.endDate,
      this.lotteryDate,
      this.amount,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.equbType,
      this.payments,
      this.totalPayment,
      this.remainingPayment,
      this.remainingLotteryDate,
      this.equbTakers});
  factory TodayLottery.fromJson(Map<String, dynamic> json) {
    return TodayLottery(
        id: json["id"],
        memberId: json["member_id"],
        equbTypeId: json["equb_type_id"],
        totalAmount: json['total_amount'],
        amount: json['amount'],
        startDate: json['start_date'],
        endDate: json['end_date'],
        lotteryDate: json['lottery_date'],
        status: json["status"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        totalPayment: json["total_payment"],
        remainingPayment: json["remaining_payment"].toString(),
        remainingLotteryDate: json["remaining_lottery_date"].toString(),
        equbType: EqubType.fromJson(json["equb_type"]),
        payments: Payments.fromStringObject(json["payments"]),
        equbTakers: EqubTakers.fromStringObject(json["equb_takers"]));
  }
}
