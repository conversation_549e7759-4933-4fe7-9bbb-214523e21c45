class TelePack {
  String message;
  int code;
  String? totalAmount;
  String? appId;
  String? receiverName;
  String? shortCode;
  String? subject;
  String? h5PaymentUrl;
  String? returnUrl;
  String? notifyUrl;
  String? timeoutExpress;
  String? appKey;
  String? publicKey;
  String? outTradeNumber;
  String? inAppPaymentUrl;

  TelePack(
      {required this.message,
      required this.code,
      this.totalAmount,
      this.appId,
      this.receiverName,
      this.shortCode,
      this.subject,
      this.returnUrl,
      this.notifyUrl,
      this.h5PaymentUrl,
      this.timeoutExpress,
      this.appKey,
      this.publicKey,
      this.inAppPaymentUrl,
      this.outTradeNumber});

  factory TelePack.fromJson(Map<String, dynamic> json) => TelePack(
        message: json["message"] ?? "notii",
        code: json["code"],
        totalAmount: json["totalAmount"]?.toString(),
        // totalAmount: "1",
        appId: json["appId"].toString(),
        receiverName: json["receiverName"].toString(),
        shortCode: json["shortCode"].toString(),
        subject: json["subject"].toString(),
        returnUrl: json["returnUrl"].toString(),
        h5PaymentUrl: json["h5PaymentUrl"].toString(),
        notifyUrl: json["notifyUrl"].toString(),
        timeoutExpress: json["timeoutExpress"].toString(),
        appKey: json["appKey"].toString(),
        publicKey: json["publicKey"].toString(),
        outTradeNumber: json["outTradeNo"].toString(),
        inAppPaymentUrl: json["inAppPaymentUrl"]?.toString(),
      );

  @override
  String toString() => ' TelePack {'
      'code: $code,'
      ' message: $message,'
      ' totalAmount: $totalAmount,'
      ' appId: $appId,'
      ' receiverName: $receiverName,'
      ' shortCode: $shortCode,'
      ' subject: $subject,'
      ' returnUrl: $returnUrl,'
      ' notifyUrl: $notifyUrl,'
      ' timeoutExpress: $timeoutExpress,'
      ' appKey: $appKey,'
      ' publicKey: $publicKey,'
      ' outTradeNumber: $outTradeNumber,'
      ' inAppPaymentUrl: $inAppPaymentUrl}';

  Map<String, dynamic> toJson() => {
        'code': '$code',
        'message': message,
        'totalAmount': '$totalAmount',
        'appId': '$appId',
        'receiverName': '$receiverName',
        'shortCode': '$shortCode',
        'subject': '$subject',
        'returnUrl': '$returnUrl',
        'notifyUrl': '$notifyUrl',
        'timeoutExpress': '$timeoutExpress',
        'appKey': '$appKey',
        'publicKey': '$publicKey',
        'outTradeNumber': '$outTradeNumber',
        'inAppPaymentUrl': '$inAppPaymentUrl'
      };
}
