import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:ekub/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../utils/constants.dart';

class PrivacyScreen extends StatefulWidget {
  const PrivacyScreen({super.key});

  @override
  State<PrivacyScreen> createState() => _PrivacyScreenState();
}

class _PrivacyScreenState extends State<PrivacyScreen> {
  WebViewController _controller = WebViewController();
  late ThemeProvider themeProvider;
  int loadingPercentage = 0;

  initializeWebWiew(BuildContext context) async {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (mounted) {
              setState(() {
                loadingPercentage = progress;
              });
            }
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) async {
            if (mounted) {
              setState(() {
                loadingPercentage = 100;
              });
            }
          },
          onWebResourceError: (WebResourceError error) {
            // Handle web resource error
          },
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith(
                'https://test.virtualekubdash.com/terms-and-conditions')) {
              return NavigationDecision.navigate;
            }
            return NavigationDecision.prevent;
          },
        ),
      )
      ..loadRequest(
          Uri.parse('https://test.virtualekubdash.com/terms-and-conditions'));
  }

  @override
  void initState() {
    super.initState();
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    initializeWebWiew(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: themeProvider.getColor),
        title: Text(
          "Terms and conditions",
          style: TextStyle(
              color: themeProvider.getColor,
              fontSize: fontMedium,
              fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: loadingPercentage < 100
          ? Column(
              children: [
                if (loadingPercentage < 100) const CustomLoadingIndicator(),
              ],
            )
          : Container(
              margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              child: WebViewWidget(
                controller: _controller,
              ),
            ),
    );
  }
}
