// ignore_for_file: unused_element, use_build_context_synchronously

import 'dart:async';
import 'dart:io';

import 'package:ekub/init/language_select.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

import '../exports/screens.dart';
import '../exports/models.dart';

import '../repository/user_repos.dart';
import '../utils/tools.dart';

class SplashScreen extends StatefulWidget {
  static const routeName = "/change_password";

  const SplashScreen({super.key});
  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late User user;
  late ThemeProvider themeProvider;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  bool majorChangeDetected = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: themeProvider.getColor,
      statusBarIconBrightness: Brightness.light,
    ));
  }

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );
    _progressAnimation =
        Tween(begin: 0.0, end: 1.0).animate(_progressController);
    _progressController.forward();
    Future.microtask(() {
      listenForFirestoreUpdates();
    });
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  void listenForFirestoreUpdates() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String currentVersion = packageInfo.version; // Get current app version

    FirebaseFirestore.instance
        .collection('virtual')
        .doc('version')
        .snapshots()
        .listen((DocumentSnapshot snapshot) {
      if (snapshot.exists) {
        final data = snapshot.data() as Map<String, dynamic>;
        String firestoreVersion = data['version'] ?? '0.0.0+0';

        print(
            'Current Version: $currentVersion, Firestore Version: $firestoreVersion');

        if (_isMajorChange(currentVersion, firestoreVersion)) {
          majorChangeDetected = true;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => AlertDialog(
                title: Text('Update Required'),
                content: Text(
                    'A major update is available. Please update the app to continue.'),
                actions: [
                  TextButton(
                    onPressed: () async {
                      const url =
                          'https://play.google.com/store/apps/details?id=com.vintechplc.virtualequb&pcampaignid=web_share';
                      if (await canLaunch(url)) {
                        await launch(url);
                      } else {
                        throw 'Could not launch $url';
                      }
                      Navigator.of(context).pop();
                    },
                    child: Text('Update Now'),
                  ),
                ],
              ),
            );
          });
        } else {
          _proceedWithLogin();
        }
      } else {
        print('Document does not exist');
      }
    }, onError: (error) {
      print('Error listening to Firestore: $error');
    });
  }

  bool _isMajorChange(String currentVersion, String firestoreVersion) {
    List<String> currentParts = currentVersion.split('.');
    List<String> firestoreParts = firestoreVersion.split('.');

    int currentMajor = int.parse(currentParts[0]);
    int firestoreMajor = int.parse(firestoreParts[0]);

    return firestoreMajor > currentMajor;
  }

  Future<void> _loadProfile() async {
    if (!majorChangeDetected) {
      bool isSafeDevice = await isSecureDevice();
      if (!isSafeDevice) {
        // Show a warning dialog instead of exiting the app
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text("Development Environment Detected"),
              content: const Text(
                "This app is running in a development environment (emulator or simulator). "
                "Some features may not work as expected. Please use a secure, non-rooted device "
                "for production use.",
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text("Continue Anyway"),
                  onPressed: () {
                    Navigator.of(context).pop();
                    _proceedWithLogin();
                  },
                ),
              ],
            );
          },
        );
      } else {
        _proceedWithLogin();
      }
    }
  }

  void _proceedWithLogin() {
    var auth = AuthDataProvider(httpClient: http.Client());
    auth.getUserData().then((value) => {
          setState(() {
            user = value;
            if (value.email != null &&
                value.role != null &&
                value.token != null) {
              _openHomeScreen(value.role == "admin", value.role ?? "none");
            } else {
              _openLoginScreen();
            }
          })
        });
  }

  startTimer() async {
    var duration = const Duration(seconds: 5);
    return Timer(duration, _loadProfile);
  }

  @override
  Widget build(BuildContext context) {
    return initScreen(context);
  }

  bool _isUserLoggedIn() {
    return user != null;
  }

  Future<bool> _checkLanguagePreference() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.containsKey('language'); // Check if language preference is set
  }

  _openLoginScreen() async {
    bool selected =
        await _checkLanguagePreference(); // Check if language preference is set

    if (selected == true) {
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (context) => kIsWeb
                  ? WelcomeScreen(args: LoginScreenArgs(isOnline: true))
                  : LoginScreen(args: LoginScreenArgs(isOnline: true))));
    } else {
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (context) =>
                  const LanguageSelectionScreen())); // If language preference is not set, show language selection screen
    }
  }

  _openHomeScreen(bool isAdmin, String role) {
    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);
    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }

  _openRegistration() {
    Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) =>
                LoginScreen(args: LoginScreenArgs(isOnline: true))));
  }

  _waitSec() async {
    await Future.delayed(const Duration(seconds: 5));
  }

  initScreen(BuildContext context) {
    return Scaffold(
      backgroundColor: themeProvider.getColor,
      body: Container(
        constraints: const BoxConstraints.expand(),
        decoration: const BoxDecoration(),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Image.asset(
              "assets/icons/logo_dark.png",
              width: 300,
            ),
            const SizedBox(height: 40),
            Text(
              'Let\'s make our dreams',
              style:
                  TextStyle(color: themeProvider.getLightColor, fontSize: 20),
            ),
            const SizedBox(height: 5),
            Text(
              'come true together!',
              style:
                  TextStyle(color: themeProvider.getLightColor, fontSize: 20),
            ),
            const SizedBox(height: 30),
            SizedBox(
              width: 200,
              child: AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return LinearProgressIndicator(
                    value: _progressAnimation.value,
                    backgroundColor:
                        themeProvider.getLightColor.withOpacity(0.3),
                    valueColor: AlwaysStoppedAnimation<Color>(
                        themeProvider.getLightColor),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
