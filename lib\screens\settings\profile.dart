// ignore_for_file: prefer_typing_uninitialized_variables, unused_element, deprecated_member_use

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/exports/screens.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/account/change_password.dart';
import 'package:ekub/screens/account/login_screen.dart';
import 'package:ekub/screens/account/update_account.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

import '../../models/user.dart';
// import '../../repository/language.dart'; // Commented out import
import '../../repository/user_repos.dart';
import '../../utils/colors.dart';
import '../../utils/tools.dart';
import '../themes/ThemeProvider.dart';
import 'constant.dart';

class ProfilePage extends StatefulWidget {
  static const routeName = "/profile";
  final image;
  final Member? member;
  const ProfilePage({super.key, this.image, required this.member});
  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      email: "loading",
      role: "loading");
  List<String> languages = ["English", "አማርኛ", "Oromic", "ትግሪኛ", "Somaali"];
  String selectedLanguage = "Language";
  String language = "";
  late ThemeProvider themeProvider;
  late AuthDataProvider authDataProvider;
  late ScrollController _scrollController;
  final dropdownControl = TextEditingController();
  // AppLanguage appLanguage = AppLanguage(); // Removed

  double top = 0;
  var proLoaded = false;

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _scrollController = ScrollController();

    _scrollController.addListener(() {
      setState(() {});
    });
    _loadProfile();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // var appLanguage = Provider.of<AppLanguage>(context); // Removed
    var language = EkubLocalization.of(context)!;

    return Scaffold(
        backgroundColor: ColorProvider.backgroundColor,
        body: Stack(
          children: [
            CustomScrollView(
              controller: _scrollController,
              slivers: [
                SliverAppBar(
                  pinned: true,
                  stretch: true,
                  iconTheme: IconThemeData(color: themeProvider.getColor),
                  actionsIconTheme:
                      IconThemeData(color: themeProvider.getColor),
                  expandedHeight: 100,
                  backgroundColor: Colors.white,
                  flexibleSpace: LayoutBuilder(builder: (ctx, cons) {
                    top = cons.biggest.height;
                    return FlexibleSpaceBar(
                      centerTitle: true,
                      background: Container(
                        color: Colors.white,
                      ),
                      title: AnimatedOpacity(
                        duration: const Duration(milliseconds: 300),
                        opacity: top <= 200 ? 1.0 : 0.0,
                        child: Row(
                          children: [
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                //crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      CircleAvatar(
                                        backgroundColor:
                                            themeProvider.getLightColor,
                                        radius: 25,
                                        backgroundImage: widget.image != null
                                            ? MemoryImage(widget.image!)
                                            : null,
                                        child: widget.image != null
                                            ? Container()
                                            : Image.asset(
                                                "assets/icons/user.jpg",
                                                fit: BoxFit.cover,
                                              ),
                                      ),
                                      if (widget.member != null)
                                        Positioned(
                                          bottom: 0,
                                          right: 0,
                                          child: InkWell(
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      UpdateAccount(
                                                          args:
                                                              UpdateAccountArgs(
                                                                  isOnline:
                                                                      true,
                                                                  user: widget
                                                                      .member!),
                                                          user: widget.member!),
                                                ),
                                              );
                                            },
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 5,
                                                      vertical: 2),
                                              height: 25,
                                              width: 25,
                                              decoration: BoxDecoration(
                                                  color: themeProvider.getColor,
                                                  shape: BoxShape.circle),
                                              child: const Icon(
                                                Icons.edit_outlined,
                                                color: Colors.white,
                                                size: 15,
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(width: 12),
                                  Flexible(
                                    child: Text(
                                      toCamelCase(
                                          user.fullName ?? "Loading..."),
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          fontSize: fontMedium,
                                          color: themeProvider.getColor),
                                    ),
                                  ),
                                ],
                              ),
                              // const SizedBox(width: 12),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
                SliverToBoxAdapter(
                  child: Container(
                    color: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: ListView(
                      primary: false,
                      shrinkWrap: true,
                      children: [
                        // User Information
                        // const _userTileHeightSpace(height: 15),

                        _userTileText(
                            text: language.translate("user_information")),
                        const _userTileHeightSpace(height: 5),
                        _userListTile(
                          lIcon: Icons.person_outline,
                          color: Colors.redAccent.shade100,
                          title: language.translate("role"),
                          subTitle: user.role ?? "loading...",
                        ),
                        _userListTile(
                          lIcon: Icons.call_outlined,
                          color: Colors.green.shade700,
                          title: language.translate("phone_number"),
                          subTitle: user.phoneNumber ?? "loading...",
                          onTap: () {},
                        ),

                        user.email == " "
                            ? _userListTile(
                                lIcon: Icons.email_outlined,
                                color: Colors.yellow.shade700,
                                title: language.translate("email"),
                                subTitle: user.email ?? "loading...",
                                onTap: () {},
                              )
                            : Container(),

                        _userTileText(
                            text: language.translate("user_settings")),
                        const _userTileHeightSpace(height: 5),

                        _userListTile(
                            lIcon: Icons.lock_outline,
                            color: Colors.purple,
                            title: language.translate("change_password"),
                            onTap: () {
                              Navigator.pushNamed(
                                  context, ChangePassword.routeName,
                                  arguments: ChangePasswordArgs(
                                      isOnline: true,
                                      role: user.role,
                                      fromDrawer: false));
                            }),

                        _userListTile(
                          lIcon: Icons.logout,
                          color: Colors.red,
                          title: language.translate("logout"),
                          onTap: () async {
                            PanaraConfirmDialog.show(
                              context,
                              title: EkubLocalization.of(context)!
                                  .translate("warning"),
                              message: EkubLocalization.of(context)!
                                  .translate("confirm_logout"),
                              confirmButtonText: EkubLocalization.of(context)!
                                  .translate("confirm"),
                              cancelButtonText: EkubLocalization.of(context)!
                                  .translate("cancel"),
                              onTapCancel: () {
                                Navigator.pop(context);
                              },
                              onTapConfirm: () {
                                gotoSignIn(context);
                              },
                              imagePath: warningDialogIcon,
                              panaraDialogType: PanaraDialogType.warning,
                              barrierDismissible:
                                  false, // optional parameter (default is true)
                            );
                          },
                        ),

                        _userTileText(
                            text: language.translate("general_settings")),
                        const _userTileHeightSpace(height: 5),
                        // const _userTileHeightSpace(height: 10),

                        // const _userTileText(text: 'Language'),
                        Card(
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 3, vertical: 5),
                                  height: 35,
                                  width: 35,
                                  decoration: BoxDecoration(
                                      color:
                                          ColorProvider.primary.withOpacity(.1),
                                      shape: BoxShape.circle),
                                  child: Icon(
                                    Icons.language_outlined,
                                    size: 20,
                                    color: ColorProvider.primary,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: CustomDropdown<String>(
                                  onChanged: (value) {
                                    setState(() {
                                      selectedLanguage = value!;
                                      // if (selectedLanguage == "English") {
                                      //   appLanguage
                                      //       .changeLanguage(const Locale("en"));
                                      // } else if (selectedLanguage == "አማርኛ") {
                                      //   appLanguage.changeLanguage(
                                      //       const Locale("am_ET"));
                                      // } else if (selectedLanguage == "Oromic") {
                                      //   appLanguage
                                      //       .changeLanguage(const Locale("fr"));
                                      // } else if (selectedLanguage ==
                                      //     "Somaali") {
                                      //   appLanguage
                                      //       .changeLanguage(const Locale("tl"));
                                      // } else if (selectedLanguage == "ትግሪኛ") {
                                      //   appLanguage
                                      //       .changeLanguage(const Locale("es"));
                                      // }
                                      // appLanguage
                                      //     .changeLanguage(const Locale("en"));
                                    });
                                  },
                                  decoration: CustomDropdownDecoration(
                                    closedFillColor: Colors.white,
                                    closedSuffixIcon: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 3, vertical: 5),
                                      height: 10,
                                      width: 10,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.backgroundColor,
                                          shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.keyboard_arrow_down,
                                        size: 25,
                                      ),
                                    ),

                                    // selectedStyle: TextStyle(
                                    //     color: themeProvider.getColor, fontWeight: boldFont),
                                    closedBorderRadius:
                                        BorderRadius.circular(20),
                                    hintStyle: const TextStyle(
                                        fontSize: fontMedium,
                                        color: Colors.black),
                                    // selectedStyle: const TextStyle(
                                    //     fontSize: fontMedium,
                                    //     color: Colors.black),
                                  ),
                                  items: languages,
                                  hintText: selectedLanguage,
                                  excludeSelected: true,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Card(
                        //   child: ListTile(
                        //     onTap: () {
                        //       // Navigator.push(
                        //       //     context,
                        //       //     MaterialPageRoute(
                        //       //         builder: (context) =>
                        //       //             const WhishlistPage()));
                        //     },
                        //     title: Row(
                        //       crossAxisAlignment: CrossAxisAlignment.center,
                        //       children: [
                        //         Padding(
                        //           padding: const EdgeInsets.all(3.0),
                        //           child: Text(language.translate("select_theme"),
                        //               style: const TextStyle(
                        //                   fontSize: fontMedium,
                        //                   color: Colors.black)),
                        //         ),
                        //         Padding(
                        //           padding: const EdgeInsets.all(3.0),
                        //           child: GestureDetector(
                        //             onTap: () {
                        //               themeProvider.changeTheme(0);
                        //               setState(() {});
                        //             },
                        //             child: Container(
                        //               //color: ColorProvider().primaryDeepOrange,
                        //               height: 40,
                        //               width: 40,
                        //               decoration: BoxDecoration(
                        //                 color: ColorProvider().primaryDeepOrange,
                        //                 shape: BoxShape.circle,
                        //               ),
                        //             ),
                        //           ),
                        //         ),
                        //         Padding(
                        //           padding: const EdgeInsets.all(3.0),
                        //           child: GestureDetector(
                        //             onTap: () {
                        //               themeProvider.changeTheme(1);
                        //               setState(() {});
                        //             },
                        //             child: Container(
                        //               //color: ColorProvider().primaryDeepBlue,
                        //               height: 40,
                        //               width: 40,
                        //               decoration: BoxDecoration(
                        //                 color: ColorProvider().primaryDeepBlue,
                        //                 shape: BoxShape.circle,
                        //               ),
                        //             ),
                        //           ),
                        //         ),
                        //         Padding(
                        //           padding: const EdgeInsets.all(3.0),
                        //           child: GestureDetector(
                        //             onTap: () {
                        //               themeProvider.changeTheme(2);
                        //               setState(() {});
                        //             },
                        //             child: Container(
                        //               //color: ColorProvider().primaryDeepTeal,
                        //               height: 40,
                        //               width: 40,
                        //               decoration: BoxDecoration(
                        //                 color: ColorProvider().primaryDeepRed,
                        //                 shape: BoxShape.circle,
                        //               ),
                        //             ),
                        //           ),
                        //         ),
                        //         Padding(
                        //           padding: const EdgeInsets.all(3.0),
                        //           child: GestureDetector(
                        //             onTap: () {
                        //               themeProvider.changeTheme(3);
                        //               setState(() {});
                        //             },
                        //             child: Container(
                        //               //color: ColorProvider().primaryDeepTeal,
                        //               height: 40,
                        //               width: 40,
                        //               decoration: BoxDecoration(
                        //                 color: ColorProvider().primaryDeepTeal,
                        //                 shape: BoxShape.circle,
                        //               ),
                        //             ),
                        //           ),
                        //         ),
                        //       ],
                        //     ),
                        //   ),
                        // ),

                        _userTileText(text: language.translate("developed_by")),
                        const _userTileHeightSpace(height: 5),

                        // _userListTile(
                        //   lIcon: Icons.developer_board,
                        //   color: const Color.fromARGB(255, 213, 92, 16),
                        //   title: language.translate("vintage_technologies"),
                        //   subTitle: "+251916772303",
                        // ),

                        _userTileText(text: language.translate("contact")),
                        Card(
                          child: ListTile(
                              iconColor: Colors.grey[600],
                              title: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  InkWell(
                                    onTap: () => makePhoneCall(),
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 0, vertical: 0),
                                      height: 35,
                                      width: 35,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.primary
                                              .withOpacity(.1),
                                          shape: BoxShape.circle),
                                      child: Icon(
                                        Icons.phone_outlined,
                                        color: ColorProvider.primary,
                                        size: 25,
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () => openSocialMedia(websiteLink),
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 0, vertical: 0),
                                      height: 35,
                                      width: 35,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.primary
                                              .withOpacity(.1),
                                          shape: BoxShape.circle),
                                      child: Icon(
                                        Icons.link_outlined,
                                        color: ColorProvider.primary,
                                        size: 25,
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () => email(),
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 0, vertical: 0),
                                      height: 35,
                                      width: 35,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.primary
                                              .withOpacity(.1),
                                          shape: BoxShape.circle),
                                      child: Icon(
                                        Icons.email_outlined,
                                        color: ColorProvider.primary,
                                        size: 25,
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () =>
                                        openSocialMedia(facebookContact),
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 0, vertical: 0),
                                      height: 35,
                                      width: 35,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.primary
                                              .withOpacity(.1),
                                          shape: BoxShape.circle),
                                      child: Icon(
                                        Icons.facebook_outlined,
                                        color: ColorProvider.primary,
                                        size: 25,
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () =>
                                        openSocialMedia(telegramContact),
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 0, vertical: 0),
                                      height: 35,
                                      width: 35,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.primary
                                              .withOpacity(.1),
                                          shape: BoxShape.circle),
                                      child: Icon(
                                        Icons.telegram_outlined,
                                        color: ColorProvider.primary,
                                        size: 25,
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () =>
                                        openSocialMedia(instagramContact),
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 2, vertical: 0),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 2, vertical: 8),
                                      height: 35,
                                      width: 35,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.primary
                                              .withOpacity(.1),
                                          shape: BoxShape.circle),
                                      child: SvgPicture.asset(
                                          "assets/icons/instagramm.svg",
                                          height: 25,
                                          colorFilter: ColorFilter.mode(
                                              ColorProvider.primary,
                                              BlendMode.srcIn)),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () => openSocialMedia(tiktokContact),
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 2, vertical: 0),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 2, vertical: 8),
                                      height: 35,
                                      width: 35,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.primary
                                              .withOpacity(.1),
                                          shape: BoxShape.circle),
                                      child: SvgPicture.asset(
                                        "assets/icons/tiktok.svg",
                                        height: 25,
                                        color: ColorProvider.primary,
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () =>
                                        openSocialMedia(twitterContact),
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 2, vertical: 0),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 2, vertical: 8),
                                      height: 35,
                                      width: 35,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.primary
                                              .withOpacity(.1),
                                          shape: BoxShape.circle),
                                      child: SvgPicture.asset(
                                          "assets/icons/twitter.svg",
                                          height: 25,
                                          colorFilter: ColorFilter.mode(
                                              ColorProvider.primary,
                                              BlendMode.srcIn)),
                                    ),
                                  )
                                ],
                              )),
                        ),
                        const _userTileHeightSpace(height: 15),
                        _userTileText(
                            text: language.translate("delete_account")),
                        const _userTileHeightSpace(height: 15),

                        GestureDetector(
                            //  decoration: BoxDecoration(),
                            child: Container(
                                alignment: Alignment.center,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                color: Colors.red,
                                child: Text(
                                  language
                                      .translate("account_deletion_request"),
                                  style: const TextStyle(
                                    color: Colors.white,
                                  ),
                                )),
                            onTap: () {
                              PanaraConfirmDialog.show(
                                context,
                                title: EkubLocalization.of(context)!
                                    .translate("warning"),
                                message: EkubLocalization.of(context)!
                                    .translate("warning_message"),
                                confirmButtonText: EkubLocalization.of(context)!
                                    .translate("proceed"),
                                cancelButtonText: EkubLocalization.of(context)!
                                    .translate("cancel"),
                                onTapCancel: () {
                                  Navigator.pop(context);
                                },
                                onTapConfirm: () {
                                  Navigator.pop(context);
                                  openSocialMedia(
                                      "https://kabbatransport.com/support.html");
                                },
                                imagePath: warningDialogIcon,

                                panaraDialogType: PanaraDialogType.warning,
                                barrierDismissible:
                                    false, // optional parameter (default is true)
                              );
                            }),
                        const _userTileHeightSpace(height: 15),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  // _loadProfile() async {
  Future<void> _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());
    auth.getUserData().then((value) => {
          setState(() {
            user = value;
            proLoaded = true;
          })
        });
    // Removed AppLanguage fetch and language setting logic
    // await appLanguage.fetchLocale();
    // language = appLanguage.appLocal.languageCode.toLowerCase();

    // Removed language setting based on appLanguage
    // if (language == "fr") {
    //   setState(() {
    //     selectedLanguage = "Oromic";
    //   });
    // }
    // if (language == "es") {
    //   setState(() {
    //     selectedLanguage = "ትግሪኛ";
    //   });
    // }
    // if (language == "am") {
    //   setState(() {
    //     selectedLanguage = "አማርኛ";
    //   });
    // } else if (language == "tl") {
    //   setState(() {
    //     selectedLanguage = "Somaali";
    //   });
    // } else if (language == "en") {
    //   setState(() {
    //     selectedLanguage = "English";
    //   });
    // }
  }

  void gotoSignIn(BuildContext context) {
    var auth = AuthDataProvider(httpClient: http.Client());
    auth.logOut();
    LoginScreenArgs argument = LoginScreenArgs(isOnline: true);
    Navigator.pushNamedAndRemoveUntil(
        context,
        kIsWeb ? WelcomeScreen.routeName : LoginScreen.routeName,
        (Route<dynamic> route) => false,
        arguments: argument);
  }
}

// ignore: camel_case_types
class _userTileHeightSpace extends StatelessWidget {
  final double height;
  const _userTileHeightSpace({
    super.key,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(height: height);
  }
}

// ignore: camel_case_types
class _userTileText extends StatelessWidget {
  final String text;
  const _userTileText({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      ' $text',
      style: const TextStyle(
        fontSize: fontBig,
        fontWeight: boldFont,
        // decoration: TextDecoration.underline,
      ),
    );
  }
}

// ignore: camel_case_types
class _userListTile extends StatelessWidget {
  final IconData lIcon;
  final Color color;
  final String title;
  final String? subTitle;
  final IconData? tIcon;
  final VoidCallback? tIconCallBack;
  final VoidCallback? onTap;
  const _userListTile({
    this.subTitle,
    this.tIcon,
    this.tIconCallBack,
    this.onTap,
    super.key,
    required this.lIcon,
    required this.color,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5), //color of shadow
            spreadRadius: .1, //spread radius
            blurRadius: 2, // blur radius
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
          height: 35,
          width: 35,
          decoration: BoxDecoration(
              color: ColorProvider.primary.withOpacity(0.1),
              shape: BoxShape.circle),
          child: Icon(
            lIcon,
            size: 20,
            color: ColorProvider.primary,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(fontSize: fontMedium, color: Colors.black),
        ),
        subtitle: subTitle == null
            ? null
            : Text(
                subTitle!,
                style:
                    TextStyle(fontSize: fontSmall, color: Colors.grey.shade500),
              ),
        onTap: onTap,
        trailing: IconButton(
          icon: Icon(tIcon),
          onPressed: tIconCallBack,
        ),
      ),
    );
  }
}
