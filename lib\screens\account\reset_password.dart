// ignore_for_file: unused_field, must_be_immutable

import 'dart:async';

import 'package:ekub/exports/screens.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/validator.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../repository/ekub_localization.dart';
import '../../utils/network.dart';
import '../themes/ThemeProvider.dart';

class ResetPassword extends StatefulWidget {
  static const routeName = "/reset_password";
  ResetPasswordArgs args;
  ResetPassword({super.key, required this.args, required this.userId});
  int? userId;
  @override
  State<ResetPassword> createState() => _ResetPasswordState();
}

class _ResetPasswordState extends State<ResetPassword> {
  var invisible = false;
  bool isSubmitted = false;
  bool _onProcess = false;
  bool isOffline = false;
  final _resetFormKey = GlobalKey<FormState>();
  late ThemeProvider themeProvider;
  late StreamSubscription _connectionChangeStream;
  final passwordControl = TextEditingController();
  final password2Control = TextEditingController();

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  @override
  void initState() {
    _onProcess = false;
    NetworkManager connectionStatus = NetworkManager.getInstance();
    _connectionChangeStream =
        connectionStatus.connectionChange.listen(connectionChanged);
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    super.initState();
  }

  _resetPassword() {
    var sender = AuthDataProvider(httpClient: http.Client());

    var res = sender.resetPassword(
        context, widget.userId, password2Control.text, widget.args.otp);
    res
        .then((value) => {
              setState(() {
                _onProcess = false;
              }),
              if (value.code == "200")
                {
                  if (mounted)
                    {
                      PanaraInfoDialog.show(
                        context,
                        message: value.message,
                        buttonText:
                            EkubLocalization.of(context)!.translate("okay"),
                        onTapDismiss: () {
                          Navigator.pop(context);
                          Navigator.pushReplacement(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => LoginScreen(
                                      args: LoginScreenArgs(isOnline: true))));
                        },
                        imagePath: successDialogIcon,
                        panaraDialogType: PanaraDialogType.success,
                      ),
                    }
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value.message,
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                  })
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          _resetPassword();
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        _onProcess = false;
      });
      return {};
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorProvider.backgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.white,
          title: Text(
            EkubLocalization.of(context)!.translate("reset_password"),
            style: const TextStyle(
              fontSize: fontBig,
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          elevation: 0,
          iconTheme: IconThemeData(color: ColorProvider.primary),
        ),
        body: Form(
            key: _resetFormKey,
            autovalidateMode: isSubmitted
                ? AutovalidateMode.onUserInteraction
                : AutovalidateMode.disabled,
            child: Stack(
              children: <Widget>[
                Align(
                  alignment: Alignment.bottomCenter,
                  child: ListView(
                    children: <Widget>[
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20)),
                        margin: const EdgeInsets.fromLTRB(30, 20, 30, 10),
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                        child: Column(
                          children: <Widget>[
                            // const SizedBox(
                            //   height: 15,
                            // ),
                            Container(
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 10),
                              child: const Image(
                                image: AssetImage(
                                  "assets/icons/lock.png",
                                ),
                                fit: BoxFit.cover,
                                height: 200,
                              ),
                            ),
                            // const SizedBox(
                            //   height: 20,
                            // ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Text(
                                    EkubLocalization.of(context)!
                                        .translate("new_password"),
                                    style: TextStyle(
                                        color: themeProvider.getColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: fontMedium),
                                  ),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                TextFormField(
                                  style: lableStyle,
                                  controller: passwordControl,
                                  obscureText: invisible,
                                  decoration: InputDecoration(
                                      prefixIcon: Container(
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 5),
                                        height: 25,
                                        width: 30,
                                        decoration: BoxDecoration(
                                            color: themeProvider.getColor
                                                .withOpacity(0.1),
                                            shape: BoxShape.circle),
                                        child: Icon(
                                          Icons.lock_outlined,
                                          size: 20,
                                          color: themeProvider.getColor,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(18),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(18),
                                        borderSide: BorderSide(
                                            color: themeProvider.getColor),
                                      ),
                                      fillColor: Colors.white,
                                      filled: true,
                                      hintText: '******',
                                      hintStyle: hintStyle,
                                      errorBorder: OutlineInputBorder(
                                        borderSide: const BorderSide(
                                          color: Colors.red,
                                        ),
                                        borderRadius: BorderRadius.circular(18),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(18),
                                        borderSide: const BorderSide(
                                            color: Colors.red, width: 2),
                                      ),
                                      suffix: GestureDetector(
                                        onTap: changeSate,
                                        child: Icon(
                                          invisible
                                              ? Icons.visibility
                                              : Icons.visibility_off,
                                          color: themeProvider.getColor,
                                        ),
                                      )),
                                  validator: (value) => Sanitizer()
                                      .isPasswordValid(value!, context),
                                  onSaved: (value) {},
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Text(
                                    EkubLocalization.of(context)!
                                        .translate("confirm_password"),
                                    style: TextStyle(
                                        color: themeProvider.getColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: fontMedium),
                                  ),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                TextFormField(
                                  style: lableStyle,
                                  controller: password2Control,
                                  obscureText: invisible,
                                  decoration: InputDecoration(
                                    prefixIcon: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 5),
                                      height: 25,
                                      width: 30,
                                      decoration: BoxDecoration(
                                          color: themeProvider.getColor
                                              .withOpacity(0.1),
                                          shape: BoxShape.circle),
                                      child: Icon(
                                        Icons.lock_outlined,
                                        size: 20,
                                        color: themeProvider.getColor,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: BorderSide(
                                          color: Colors.grey.shade400),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: BorderSide(
                                          color: themeProvider.getColor),
                                    ),
                                    fillColor: Colors.white,
                                    filled: true,
                                    errorBorder: OutlineInputBorder(
                                      borderSide: const BorderSide(
                                        color: Colors.red,
                                      ),
                                      borderRadius: BorderRadius.circular(18),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: const BorderSide(
                                          color: Colors.red, width: 2),
                                    ),
                                    hintText: '*******',
                                    hintStyle: hintStyle,
                                    suffix: GestureDetector(
                                      onTap: changeSate,
                                      child: Icon(
                                        invisible
                                            ? Icons.visibility
                                            : Icons.visibility_off,
                                        color: themeProvider.getColor,
                                      ),
                                    ),
                                  ),
                                  validator: (value) => Sanitizer()
                                      .isPasswordMatch(passwordControl.text,
                                          value!, context),
                                  onSaved: (value) {},
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 40,
                            ),
                            ElevatedButton(
                              onPressed: _onProcess
                                  ? null
                                  : () async {
                                      final form = _resetFormKey.currentState;
                                      setState(() {
                                        isSubmitted = true;
                                      });
                                      if (form!.validate()) {
                                        setState(() {
                                          _onProcess = true;
                                        });
                                        form.save();
                                        if (!await InternetConnectivity()
                                            .checkInternetConnectivty(
                                                context, true)) {
                                          setState(() {
                                            _onProcess = false;
                                          });
                                          return;
                                        } else {
                                          _resetPassword();
                                        }
                                      }
                                    },
                              child: Container(
                                height: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  color: themeProvider.getColor,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Spacer(),
                                    Text(
                                        EkubLocalization.of(context)!
                                            .translate("reset"),
                                        style: const TextStyle(
                                            color: Colors.white)),
                                    const Spacer(),
                                    Align(
                                      widthFactor: 2,
                                      alignment: Alignment.centerRight,
                                      child: _onProcess
                                          ? const Padding(
                                              padding: EdgeInsets.all(8.0),
                                              child: SizedBox(
                                                height: 20,
                                                width: 20,
                                                child:
                                                    CircularProgressIndicator(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            )
                                          : Container(),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            )));
  }
}
