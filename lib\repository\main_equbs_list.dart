import 'dart:typed_data';
import 'package:ekub/models/equb_type.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/screens/dashboard/admin/actions/equb_frequency_selection.dart';
import 'package:ekub/screens/dashboard/admin/actions/equb_list_page.dart';
import 'package:ekub/screens/dashboard/admin/actions/manual_equb_list_page.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:provider/provider.dart';
import 'package:ekub/screens/dashboard/ekubs/equb_provider.dart'; // Import the provider
import 'package:ekub/screens/dashboard/admin/actions/equb_subtypes.dart';
import 'package:ekub/screens/comming_soon.dart';
import 'package:cached_network_image/cached_network_image.dart';
// import 'package:ekub/screens/dashboard/admin/actions/equb_subtypes.dart';
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:ekub/service/headers.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../../../routs/shared.dart';

// Define a StatefulWidget to display Equbs
class MainEqubsList extends StatefulWidget {
  final AddEqubArgs args;

  MainEqubsList({super.key, required this.args});

  @override
  _MainEqubsListState createState() => _MainEqubsListState();
}

class _MainEqubsListState extends State<MainEqubsList> {
  List<dynamic> equbs = []; // List to store the equbs data
  final _baseUrl = RequestHeader.baseApp;
  final _appBar = GlobalKey<FormState>();
  late ThemeProvider themeProvider;
  EqubType? selectedEqubType;
  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    super.initState();
    fetchEqubs();

    // Check the source and print a message
    if (widget.args.user == null) {
      print("User navigated from Member Equbs");
    } else {
      print("User has not navigated from Main Equbs");
    }
  }

  // Function to fetch equbs data from API
  Future<void> fetchEqubs() async {
    final url = Uri.parse('$_baseUrl/mainequb');
    try {
      // Get the authorized headers
      final headers = await RequestHeader().authorisedHeader();

      final response = await http.get(url, headers: headers);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        print("Raw equbs data: ${data['data']}");
        print("Data length: ${data['data'].length}");

        // Filter the equbs to include only those with active == 1
        final filteredEqubs =
            data['data'].where((equb) => equb['active'] == "1").toList();

        setState(() {
          equbs = filteredEqubs;
        });

        print("Filtered equbs list length: ${equbs.length}");
        print(
            "Equbs list: $equbs"); // Print the list of equbs to ensure it's populated
      } else {
        print('Failed to load equbs: ${response.statusCode}');
      }
    } catch (e) {
      print("Error occurred: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    // Add a default "Manual Equb" item
    final manualEqub = {
      'name': 'Manual Equb',
      'active': '1',
      'image_url': null,
    };

    // Combine the default item with the fetched equbs
    final displayedEqubs = [manualEqub, ...equbs];

    return Scaffold(
      appBar: AppBar(
        key: _appBar,
        title: Text(
          EkubLocalization.of(context)!.translate("select_main_equb"),
          style: TextStyle(fontSize: fontBig),
        ),
      ),
      body: Container(
        color: ColorProvider.backgroundColor,
        child: displayedEqubs.isEmpty
            ? Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10),
                    Expanded(child: Center(child: searchLoading())),
                  ],
                ),
              )
            : Padding(
                padding: const EdgeInsets.only(top: 10, left: 10, right: 10),
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                  ),
                  itemCount: displayedEqubs.length,
                  itemBuilder: (context, index) {
                    final equb = displayedEqubs[index];
                    return GestureDetector(
                      onTap: () {
                        if (equb['name'] == 'Manual Equb') {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              // builder: (context) => ManualEqubList(
                              //   equbType: EqubType(),
                              //   args: AddEqubArgs(
                              //     isOnline: true,
                              //     member: widget.args.member!,
                              //     isAdmin: false,
                              //     user: widget.args.user,
                              //   ),
                              // ),
                              // ),
                              builder: (context) => ComingSoonPage(),
                            ),
                          );
                        } else {
                          setState(() {
                            selectedEqubType = EqubType.fromJson(equb);
                          });
                          Provider.of<EqubProvider>(context, listen: false)
                              .setEqubId(equb['id']?.toString() ?? 'default');

                          if (equb['active'] == "0") {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ComingSoonPage(),
                              ),
                            );
                          } else {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => EqubFrequencySelection(
                                  onFrequencySelected: (frequency) {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => EqubListPage(
                                          equbType: selectedEqubType!,
                                          frequency: frequency,
                                          args: widget.args,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          }
                        }
                      },
                      child: roleContainer(
                        context,
                        equb,
                        false,
                        Theme.of(context),
                        null,
                      ),
                    );
                  },
                ),
              ),
      ),
    );
  }

  Widget roleContainer(BuildContext context, dynamic equb, bool isSelected,
      ThemeData theme, Uint8List? equbImage) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: .1,
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
        color: Colors.white,
        borderRadius: const BorderRadius.all(Radius.circular(15)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.center,
            width: 65,
            height: 65,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: themeProvider.getLightColor,
            ),
            child: equb['image_url'] != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: CachedNetworkImage(
                      imageUrl: equb['image_url'],
                      width: 55,
                      height: 55,
                      fit: BoxFit.cover,
                      placeholder: (context, url) =>
                          CircularProgressIndicator(),
                      errorWidget: (context, url, error) => Icon(
                        Icons.image_not_supported,
                        color: theme.primaryColor,
                      ),
                    ),
                  )
                : Icon(
                    Icons.image_not_supported,
                    color: theme.primaryColor,
                  ),
          ),
          const SizedBox(height: 10),
          Text(
            equb['name'],
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: fontMedium, // Adjust font size as needed
            ),
          ),
          const SizedBox(height: 5),
        ],
      ),
    );
  }
}
