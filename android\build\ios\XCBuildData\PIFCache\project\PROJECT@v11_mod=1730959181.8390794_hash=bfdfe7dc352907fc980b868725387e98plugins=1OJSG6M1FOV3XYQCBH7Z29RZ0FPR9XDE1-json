{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98814b7e2c3bac55ee99d78eaa8d1ec61e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c22f26ca3341c3062f2313dc737070d4", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9828903703a9fe9e3707306e58aab67b51", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8fdf73ba7ca97701c8e258a81229d20", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/Classes/AudioSessionPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ce0e71e821134dd4f7419544b7200e3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/Classes/AudioSessionPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ab14161d665a1b25c47fde3a7363501", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/Classes/DarwinAudioSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dbf0bc8d722ee27d85dfe899989867ea", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/Classes/DarwinAudioSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b28af27463480ed706d721b9df5c92e6", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9e49200b7bafb1c92a7dbbce6a60425", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e7503b0d48f9f8f4af9cd18736702c9", "name": "audio_session", "path": "audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810a385d58f8c3a3d5222cab8dfe30dba", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834071cba39621495f98fc89872af37cc", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cac8e00b079f0df9408b5b580bbfbeb8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c2f9306e9a855021fac1e144181cd99", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc7f834d6e578c7e4c0e0f53683d5126", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e014731940c46d9e3e6661e583923d7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988768cfc77061bd83e7f62fc2f09de1ed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6b12a857fd6d674badcc9459a0ea762", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a41b5fe616d861bcc75e3fadb10f5d06", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983931590e3e5ee135fd9fc221aca221a0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827a148b7519bfd118afea722bfd42d28", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989f376c515a222e75ace1fd9d544abc47", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/ios/audio_session.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9843e5c99b6b9bc9be71b29cdda84ce2e8", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.21/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b9f1c148f7ed45791cf8b88b442a2c0d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98be45d8123afe61a5a73acdd79349fd6c", "path": "audio_session.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98565b727b09bf9c85cabf3d50df695e0b", "path": "audio_session-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98efca7ca9097d4fdef9b0845112d9b86b", "path": "audio_session-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a460cbb9644e97a1b776ded4444df475", "path": "audio_session-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c3a9a01418cb93d88e99cdc9f630887", "path": "audio_session-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9881689fb7c1cc73140c69ce8bd3041313", "path": "audio_session.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9816d8f35d5f2a228744b437455a3f9e04", "path": "audio_session.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9821cf161dfea1b94c6bc4576680625dba", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf17862aa9ae2d9f67c6decd59ad91fe", "name": "audio_session", "path": "../.symlinks/plugins/audio_session/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986a1bb7eebc28590628a52008d21500da", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ea0d89253d4ea6eb0f7663d1f62e6341", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986eb28d55abb398c7fbe7ec855670e289", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d56f121cd8efe328e5c46b9ed7edd12c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b74336fa54628c462ff22ec5bc25d909", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98715277b8ecae22443121e73e1f974a84", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989afa4e851b56660dc2aa55e10d60dcfc", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6c3ae80d423a2be6653d7af62e7a7a1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e77fbaf21c2a5b5b10058133d65d54d4", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b9058fc1c6f37108dcb8336cfc1904d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877633f7ceff8ff05fc16e40317f61a73", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841d6e5eddcb183323e1cfa7ff297cb03", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869cff3e4ca6c763f592e4330ec196829", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98554be578eb9138cec2a4e1094f1cfd37", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPermissionUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9835560d3b97b03157450b9dcd7a942186", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98349ccdaf9c7037806c544102b676663e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/CameraProperties.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb13f1aec9d209c9ae1a1b593ba38a6a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/FLTCam.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bebd673349d94f55ccc194b7030a34dd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/FLTCamMediaSettingsAVWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f13cc457801ce8e3049d2740f882fe49", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/FLTSavePhotoDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824634659b8d553e67cc99d475eb0627e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/FLTThreadSafeEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e23ac904af369721590c9d476e902561", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98735123abf93442db428383b1c7ad35d2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/QueueUtils.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ad3ddea012ed61f3d9faa5b6c17601d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98539c997b490d0101aa67955bc097a8c0", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e654f07ad6003bbf30224807a26a79b0", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897b8f394a7ceda79601719d86ec6e9b0", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870db81092c1038e551102e66a6c68e2a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraProperties.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a0fdfb957699f2cf0134d7204ec133d4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCam.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c83f9629066675c8402d74d4b57cd30", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCam_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b93ffd806d85c2d2b1bf141a792caa7e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCamMediaSettingsAVWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874dfc5b970da839bbf6795ccd2c5454e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTSavePhotoDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98790b590ac74f356980abd0eb35ec37e8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTSavePhotoDelegate_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c28345b9e4006772864fd60b6a064d7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTThreadSafeEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b9c7715a71972cd0ac3ae1c00abf535", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98838cdb4e807bab8b542db04540954a68", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/QueueUtils.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983d017055805b14692051c0696951a013", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2aeb5e836c23ba8e0cdbf9774f57a31", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cec9ce96d85311293bd74e14d87cee7", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a98ecf7d4e0e0682b4eb48ed68a995c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825bd15d94c32864e37537040892606c4", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcbfdd5a57bfd33b0b810c941774ecc4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f962a0b40bbbefe991cbee3b804d9b5a", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d8045bad94922bdf4e8df29f3f112ca", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985903ac5e7860d063bd612ae9dab0cc38", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc7a2a49c4778c8f8d456755342f8702", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98608ab9d65b855f4b81824d64d4db1750", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f31c7479e1af2d43a3f635b118dc33e3", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa1af9edd6c15ca8516b46750545d283", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989784c086655e4c59418278ed9b1a113d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d903e1b960457469ac45aa3cabae151a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffbb8e852c1132b7b837cf66034802bd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea292a9b6fdb8923d37b3f43ccc30903", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984255f72140c4e537a4221e194cdf8d1d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bfe6da7e7b6c5ea8300b8ae8943ac9f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899a69a383ea5a2ff85845f8df548757a", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986c36621a0a2be04682d45c090053a832", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98de7c533c1f5d17dafd7f3651d03a7d10", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/CameraPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982aecc7b8701e54813162023546194aa4", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f9d01375465833314a5737fdda2ee519", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9890af32c2dc79a08cdcf4f1a39e87c993", "path": "camera_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9874fb849b7430bee757835f2732a5e3c8", "path": "camera_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98694047ac5de04ec4497f28bc86d906ef", "path": "camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae4b7b73dda5c736a1a01a8ab355279e", "path": "camera_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f75edb0b6e17a726ea2d9b7b7d42dfb0", "path": "camera_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9839bea6cb237dff03748e92e7be59c56b", "path": "camera_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9859a55f56d6912377a9c2d52b6cd44035", "path": "ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987c8fde9117c86a8f78b8abe17425a9ab", "name": "Support Files", "path": "../../../../Pods/Target Support Files/camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989cb9d407e74a484065b089451accd814", "name": "camera_avfoundation", "path": "../.symlinks/plugins/camera_avfoundation/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c3f803357998590741001528a375514", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/darwin/Classes/ConnectivityPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d7383b33575fa4bf2d92edd0a9b1af33", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/darwin/Classes/ConnectivityPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9855b02acdd814f4651104e8a2fe28739f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/darwin/Classes/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c6e1b286ecc996f45ee872db55fe4f2a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/darwin/Classes/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a63b3a327b2a28ca34ee86016720063c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/darwin/Classes/SwiftConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e7f651eec835ef108c63f46eb81dafde", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac5b12a484c44363785e5cfbd87c0991", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982aa3650f2512293b9ccf1e94d5c8e283", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856de75906f9c8bb28927ddc7d80e340c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b310aa43d6a0d9050d515a12387bc47d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6b0c0650ffe2fccc5b3c476989a728a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba57739414c2827d25246e5e6df4b19d", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8ee04aeb472901a0f69083eb50064e3", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a16aae53996389098817ac1253ef0c88", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98855166d0ae757b6983b062d8b3272bf6", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/darwin/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dd69f9ed8e18669f27485b7a655c26a5", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3eff3215e505fc7a705e14ca349d0ab", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d3b32a4002785f6f660a1d1cb0979bb", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ce100c7c415b886356eed1901d52e64", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a00098de15082ad3035489197f3943bd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98743d587cb489f754135ae5bb0287ac96", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98478595ab42ebb4091a6b84d17ca88d1c", "name": "<PERSON>", "path": "../<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b0213b7b70a82725c716751c5f1fef6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855234286d1cf758e8f251ca709fab6f1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c6349d58a25996bfc77daffb86ab1d0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98840a93f92fd5e94d5ff49d0c4e78ab76", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806fcc118e3c51e0e6a9ee711b65d3b99", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b7edbfa71dcfb941dc4dc37a233fbafb", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/darwin/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988079c20ff77d6a3d6f35e935b925b7ce", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.0.5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98637c28cf9b54ae37058fd69c3c3608a7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9872abed659da9814c6d74f58b9eb8f78f", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819d61db4bebf979d679614b59f9f4b29", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cf9fac80cd3ee734da9a5109aeb9b8c5", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980da2cce127c09b54a3d40e2cf6c867a2", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982313099f501f8733ab5d00d9b8264422", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9841d735e5fce434503ad8d2872adcc4ae", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9858b235686fa769fec1435675b9359737", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986a3b4a9ba803ac38b31a385b66bfbc46", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c3168e6ea78df5f5cecd1aac6c1db893", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c49b360c64c9d569d530d7edc2b060c8", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800a426b8624c1d07866761b0ef4f8e5c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9822dc401e7f42793a907c42f31061fbb9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ddbe23e7af17bd846c20df52cf40a025", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c800748d2029a003a3a0022768523da", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cbd069019408bfc328106e9daf6d4a2", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dfe81706c5e69ded62347d5bd719347", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e101f2874d502bd70c98ac20a8d5359b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835162a654cf4aac47eaf39622f0cf8db", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e567a00dde3d99f42b9b9ab8e1160c5e", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834d8c70bc53a05e897ead8b7f37c17d3", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984610c632dfad6434d7ce2c0e5655dfbc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9802cb51ff60b5324878f455b5e6034119", "path": "../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98819a179906eb8ce83438236338815b71", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839fe0e5722f0d020b68e023eee654440", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c29dbc769b9885dac3b391385ab2902b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984839e2b17af3b30d0dd38064e7f65ed7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986638008f377a8fd153bb60e6ca55ce19", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d9d664bb2ba6808b010a290cfb29a14", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835ba3786e4fb8130c510e5443b47a5a6", "name": "<PERSON>", "path": "../<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cac60afa1de4e166a69e11c9f09cc7d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c65119625d9acb04ede6d01b0f79ee8e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988631547e52c056ef3a187f6862e8d6ff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832aafb2c57c05240b8c3fd29a6752e31", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983353b21b6645ee4bd646d6d755b355ea", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a8668998504fb96ab4e876fe7a6d7e9d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/device_info_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983508b31801e2d9a5e0a779338b721446", "path": "../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d8903e3914ac5aa8f2c0cae251a4b52", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988fa7aaf96ec6908815793dc8e934c608", "path": "device_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a29720af8e4a98f6b426c63dd1b1c982", "path": "device_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9827dbc5982ad7fd47930e16713ab30b3d", "path": "device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984156d7383b827d5502747443ac373e32", "path": "device_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988db3df2bbdc4d99f0afff12d795b028e", "path": "device_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985b7aadf3b6e00353cff0408116c24475", "path": "device_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987418a3f2d52f764b8038b773504eff27", "path": "device_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986a97c372903e23139d6453678ee67deb", "path": "ResourceBundle-device_info_plus_privacy-device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d6068fa9ec7caa8bafa769eb6b9bd2b2", "name": "Support Files", "path": "../../../../Pods/Target Support Files/device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbc7d2309e662883f810ce37880adeb0", "name": "device_info_plus", "path": "../.symlinks/plugins/device_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d534c62325dcd05fde9606c9c12c3fec", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c2396bc63a07260d463d4df0f61db073", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986733008d04409ab42ff0b3ca05dce23b", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9812c839ee50c1f25a403103f3dbb1f404", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e7466528e89bcae2ed4fceafed42f146", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898e185bfa3878c4e6edd9c6814b879a6", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ff426bbbf20555beffb8a789f26f02f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressFileHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e4fd4e936271840b3b8cdac9694b1ef6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressFileHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98763dfdc6afca55c1123402b9f33909c3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b960d637d4b214d39f7abb7e8742332", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7356f80feea10919391cb00449a2b27", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressListHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac51d1d1defcf5bec128393e8b627e5c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressListHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d0f500d0a5446fee407d743aec48d33", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/ImageCompressPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ab2f32044690e04eca938d14bb0e93e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/ImageCompressPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862b6461674e83b98e043539b2dec8f37", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/UIImage+scale.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bcad7a47f7bb6eef029f769f0c5eb264", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/UIImage+scale.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832dc0dca01ceeebc0dad25408d74995b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/NSDictionary+SY.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988bf4df38d95b47ff930209928b56f704", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/NSDictionary+SY.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801ba6fbfcd68ad3e211f3515a4177a8e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983d20af97c430d6eefe4e108b3d6681a3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdf2733d180ab6b33f8741b78c9b5736", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadata8BIM.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0bf8d42d3f5d5c6c4b19da96102d083", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadata8BIM.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6a4fc62761178c2d7928faf5a33435f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataBase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ffbf0e3fdd0fffa1c3afd3afc268ccea", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataBase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888f299618cc24d1e8f9355df4eecba44", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataCIFF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b1f31a4b55fb0684173c175ad540d93", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataCIFF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985afe83f2c7e7a278aa8176537a290130", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataDNG.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9813d92f3ef3ec47af70e6e55aab4849e0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataDNG.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9235ba4fd32979879e6365dbb3bc736", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataExif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c524fabef1838a797869cb13ea727710", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataExif.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851d29f5ed90f7da40372ca7e5eb4243d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataExifAux.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e5850e22d09608d3b4f3145ccc56855a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataExifAux.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989fbc658a0663c8d46cf1fe937b27e9b7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataGIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987844550a5052efe013969c9799ef4a10", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataGIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be4a68f59511379962a3536c92c32c26", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataGPS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa76d4a575d624ebb8f8b74d81a0abe8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataGPS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98662d02c3f2b59c780245c31ece118a15", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataIPTC.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98618f0a3122a2fead7c68dd195dab2d8d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataIPTC.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1b1887b047a737433e10d8074da752c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataIPTCContactInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fe7b7fa3d2e8d4413b855324169151e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataIPTCContactInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984485cdbc16f3d8b07c9ef45253b2f6ed", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataJFIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984984e9a7458b327176fcfe4b6b0673fa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataJFIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989165bb0f657316952edcf1c02857178c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerCanon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820226a0195adf8d42bcaee8588608881", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerCanon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985732fadda53564c8cae5d8c137614838", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerFuji.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843b9a3684caa4bee75c32b13088c51cf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerFuji.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988314ac00c29d814b0d58ed45c76dda0f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerMinolta.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986600e3f7085285d6b5849680c2c3a758", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerMinolta.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b285f0a3556a40dd81e113817477f2e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerNikon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9814e6816a40b477a4cb8f7c522c1bc1db", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerNikon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859db256758f435dec9deb6012998ef7e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerOlympus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f3317ef85c6b94ad707f8d98144fb4c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerOlympus.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fcfe5ae5026aaea682e8313c550744d9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerPentax.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875cee4553b79354b6277e7e77b91ee70", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerPentax.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98636089998a5cc2ff1db0f8c7b2718bc9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataPNG.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d60c252f6cc534d01f16859cfad3498", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataPNG.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9803a7282f3152490bb90421a81ea37034", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataRaw.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9829eceb8ec0e3351a52e6876fc423e768", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataRaw.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877b6ba5f22492163c901a1ff4f3556e9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataTIFF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a43e27f7f3251157ddcae6e379c9fbc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataTIFF.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98902e1b27b1cc94ee5743460bbaff35a7", "name": "SYPictureMetadata", "path": "SYPictureMetadata", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806244851266106a1c19c7397bb48fb7b", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98527ef3f1f30359f9df79b8ca4783ca5c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccab27818b4dbc903add03b2bb2149bd", "name": "flutter_image_compress_common", "path": "flutter_image_compress_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981db48d4c12b27467d03c169199964aea", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825d7b6afc147b274aec2c8a33aae4bc7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2288525f05d90817f22dbc64b0520a2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98780d4eb098bb323f9feb708a217d5196", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805827f15c1984c86911b437d388f2713", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8f20e01c99b25f60700f25bd8ed9dba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aca99432840b09f8a827c68c04cbba34", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839606b442d29650ab7a079a1247b326b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844c7e02a99631b0c3ac16aab32a2489e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6e037f893b9aba2731ce4dff7a1038a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d02114969822ef61f1cd140ac1734464", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ca9e294c6e04f8baecf4619194b598e2", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/flutter_image_compress_common.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9865b11194ba403ca82fbd45adbe158450", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98df42921c8db52ede0e9c367046203018", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9860928632d86d033af95331b7ac2f7ac8", "path": "flutter_image_compress_common.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff925444f991a04426426a0204c2b058", "path": "flutter_image_compress_common-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98987e8fafae5771a030717fdca2d0d623", "path": "flutter_image_compress_common-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5f7c6b69b78812b024e63370fc16358", "path": "flutter_image_compress_common-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982586c28aca11b54f117941003c4bb709", "path": "flutter_image_compress_common-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989ad07f65af31b5fcd695b1d63ad46212", "path": "flutter_image_compress_common.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9869ed143612e17e763485740ab6949fd8", "path": "flutter_image_compress_common.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf7bd55fa23726edf93f4fb4dfd4966a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_image_compress_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e0185e19ff9a99450fe676b1c840c58", "name": "flutter_image_compress_common", "path": "../.symlinks/plugins/flutter_image_compress_common/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878fe3a309535bc547a343bab5ff30672", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/CredentialDatabase.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984bb8e35cde8f4cdee804c0221ba4993c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98176e3418b8d80975f9227429f347f478", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9861f8ed57e617bcb196cd888f7fc816e8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/ISettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f3818757a5d3218fe1d9afe458bef7c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/LeakAvoider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9839103d2ba387f035341ba531b57c8e34", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/MyCookieManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9806ec42fe897328646b4612960eb4fa85", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/MyWebStorageManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9863e7f1584787e02dc718d71f58d37bbb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PlatformUtil.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fcf9d69b829cfb43548dc1f21b1fe75d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SwiftFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9862400078746de46e561060ec9dd33386", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Util.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981306ddd125b3a8f822df74fa8690afc1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WKProcessPoolManager.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989294c01bac389f68d8dfbdb0671aaedd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b1e54c1b01972469d495d9268b382f6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e51b95abe3d153f78deee2a507ccfc2b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9827bbaba904124d1e94abe8547e0650fb", "name": "FindInteraction", "path": "FindInteraction", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983e55f2483f2ccad16110b27424fcf7d0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessInAppWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989f91eeb443cbe0a92d62f6f308744352", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessInAppWebViewManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c9d1e607579742000ff0049814fb6503", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessWebViewChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9852d74b2656ee1ccf903603aee5316fa9", "name": "HeadlessInAppWebView", "path": "HeadlessInAppWebView", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ff09c8dc4f8de6e40e5b60d405da15a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b93c1f5da4fa0fa24fd67043b9e8dd39", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e8a21d6ffa3d3989baa6b576bcb701f1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a0baa2c7df19aa551a4226cef462095", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserNavigationController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980fa49429edfeeb2e99d812ee7ee60123", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985a2601b42f8454d4e35cb5d1e7bab3cc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserWebViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986d27f48032095662338c84f9c8f10c80", "name": "InAppBrowser", "path": "InAppBrowser", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98275b2f804946f9dbec71b2064deb58e5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/ContextMenuSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9842b6feb426624aa524f8d1f88af85790", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/CustomSchemeHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986bd99ebf62815e0b7eb3ed69d0787713", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/FlutterWebViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a59c0535b524bb8da23e30cdb6f177a3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/FlutterWebViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985a29406bca1cae26969e1d54c45b08ba", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982f873618b50a70b5a67f74470ecd560a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebViewManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982c15a78b9984c4e5cf9beca66ef05cf7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebViewSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b6d4965f52760e83c68260763b10685e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebViewChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985ceae752a19e1273160f7eaba9be2221", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebViewChannelDelegateMethods.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986d200f5975a64496ce5c8a161408650f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageChannel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9890b57442770b0f6e60e557d1f9e3bb97", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageChannelChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e56a4f73af5b54ea21808d68791e9053", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d5118878e43970e42940c85dc7a81742", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageListenerChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9846c8b2522c3d9f6bad99fea204bfcee1", "name": "WebMessage", "path": "WebMessage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98721cb732c9db5cb6ea5b295f7b6a66cd", "name": "InAppWebView", "path": "InAppWebView", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98153f04ed1a536e12ab3f5f2cf626cdb6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/CallAsyncJavaScriptBelowIOS14WrapperJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98501bc60db4847440209f923c77cac2d0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/ConsoleLogJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878afdcd392bce58a2f34213441316f22", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/EnableViewportScaleJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9f157831a4f52695636d5423ad7d654", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/FindElementsAtPointJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98633504ad43abbea5793f1d0f51a135d6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/FindTextHighlightJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9877b4fd49702262e10594e6d9abdf388b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/InterceptAjaxRequestJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98af1daf160ee48f347bb44ff9dba992ba", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/InterceptFetchRequestJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b47b96c42b3cc7dc97e7842965c13ef8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/JavaScriptBridgeJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9844493acc950117bfe04af953f533dbbf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/LastTouchedAnchorOrImageJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987c88c6bb765b81315b3e34e4ab4d325c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnLoadResourceJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988b0611c6e950a02071d59ec481923d68", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnWindowBlurEventJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989606858adac2249eb951891965c87a4b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnWindowFocusEventJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9859b0f36a89f8d938ae83386fef5903f8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OriginalViewPortMetaTagContentJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9837692bb6b2a4e2b8f58c324da248ee1c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PluginScriptsUtil.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981185aefa2a835c58578c7be9e0469048", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PrintJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c76fb38e15c892add566d8ca915eb283", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PromisePolyfillJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d1f8367e7621a7bd9075c3483494116e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/SupportZoomJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fe36f7cfd5f26087eceb1b419a8aee28", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WebMessageChannelJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bda92abe9abc2c86f2c76103835a43f6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WebMessageListenerJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d7a49f7233cb0258f7b4b7bb952b48f6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WindowIdJS.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98389729f65fbc3131ae795159877133e8", "name": "PluginScriptsJS", "path": "PluginScriptsJS", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9817e4f8c0982cd665e012f555fe1afdb3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/CustomUIPrintPageRenderer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d77cb098401f3ce14a38273319821059", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintAttributes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98499830b29334afda75b683d2ec316b56", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2b88b0ea0ed054eb46a8650f46e8537", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d6d4567abbacd05dea7c2cbcfbfc27b3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986fe272707b8fbaf79298e2ac57a846a9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b56b2c248b0a606d0ff7af8b0cc54f2f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a54f2c7098efab9411423c9000952ea4", "name": "PrintJob", "path": "PrintJob", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981473d2f8d1d02f78e3ad1febbc06233d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e1bfb1924bb198c81d449591c9babbd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshControl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98422c152ddbc68ad33a35fb95a6db905f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9867b83ae891b4792a4fedc3eb0fd5a254", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c577cf406468954898c0ee4a8167cf34", "name": "PullToRefresh", "path": "PullToRefresh", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982ffcf5ece9808640059160f1d67dedc2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/ChromeSafariBrowserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f0c8d46d3a4f51a669a508efaac9cfd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/CustomUIActivity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986f25d68ea8042729e455b3b168062e82", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariBrowserSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987e61865b81ad2c3c0a2d0304d2c7da33", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e52d92489d6ea5ce6547c9acfc7cc807", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariViewControllerChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d704385e09dd520989f3ad385fc79a5", "name": "SafariViewController", "path": "SafariViewController", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98163b07a89cccee3d4d1fd0b604412b99", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ActivityButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c6d9ab852282bce3b4354dc63e9a4732", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/BaseCallbackResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fcb348d99bd0a493f8f1a4935d5af9b0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CallbackResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f8467cd4cbbe03835d123195869247f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CGRect.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b405c8d499b3a1596e6c4f4da30b0791", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CGSize.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a6fb09a59120968edbaef98fa185e75", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98768878c8a67340a7cebffcfb49246004", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ClientCertChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987367152dff5f4f4a1773a11efb1f77aa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ClientCertResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b9b8cc9c717eb1fd529500b644b52321", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CreateWindowAction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b031b7c4c741b15ce51e870b31312990", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CustomSchemeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d678882854dee87d6147036acdeae549", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/Disposable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983d9781abfdb0ef9ce60ea29ac6f7ed4f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/DownloadStartRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d6fa4551dc8414a48220ee774656283b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/FlutterMethodCallDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c048496e5e863b88a0cd4f5f433f29c6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/FlutterMethodChannel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b945db85e8ef9eead8f62486879a992", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HitTestResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984a4c8ff9f5b5b23f88d6095a795b5c9d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HttpAuthenticationChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d5bce5016ff554be87d524675d659bb1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HttpAuthResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985b2e4b30030f5f1bf9ca4de02d12dfe5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/InAppBrowserMenuItem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985763ad8ffde0a1e36af1ed3f8d72bfcb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsAlertResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec9d11a4964ba95124417b401c5c5977", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsConfirmResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cbb695c53a398c15899ebb4cffef2a23", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsPromptResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e808094de185612a622a508afbdca9df", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/MethodChannelResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988be14933be36a81ea7535e136bc16359", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/NSAttributedString.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9894a76a9fe5e6fc5f0f6720fbe830452a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PermissionRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b4a5343a01f90535a607396c8173a51a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PermissionResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c172b64d952a580812761aec91becf33", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PluginScript.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a065aa409f15e06c21b83a967f60daeb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SecCertificate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9837d786d0aca620bb05aac02171f366ee", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ServerTrustAuthResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989a9cbb09c4543d59cf2a979e7a06278d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ServerTrustChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98105245e59a75ef7289c90d9128d372eb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/Size2D.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98441fed8deeda4fadafd0f83125a7a032", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SslCertificate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9879c59ba22d8465b1dcad8794e9f7d553", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SslError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a9dd73e46e1bc8cc6bcf6536bf8bb67", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/StringOrInt.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9839b70d11a3fc02e27bfe19ac704d892c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIColor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d604b8a4d75bbc82fdc86dcd214090bd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIEdgeInsets.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981a2e1c39d16dd8e4dd34c8fa13c1f18d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIEventAttribution.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989123c737026eeea712146f9126a7b493", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIFindSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9885ae85770684b5caf9380f9b5673a407", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988394814105a2532bf5c21ceffe93bb45", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLAuthenticationChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98106548f6959a854965896b24ff9c9bff", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2446137e971e93561a7389a7206f03e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLProtectionSpace.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878e5509928804dea50594f747a4ad6bc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9844665c0532b55cc1eb959211e5d295a0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ecdf2b7a69ac0070c27377290073b87", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UserScript.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98511295fc1b107f9a10dbd8deb84c3ad0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebMessage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987eb7cd230ca111bd47636ea2ee71bcbc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebMessagePort.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d70912aaf15f54b5e014f9f72074b125", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986caf80161da98e13cc511d366f2d7e10", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981cde792bab94fc950b65725ca5d3c7eb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98830109ea6af048d77069d26d6a80ba02", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebViewTransport.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98656d61e14d073cc2db5a086dc95512c3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKContentWorld.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db30ea194f564333e7a692a1d00e477d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKFrameInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9820a1fbce9fe76b51711c972cca880a5f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKNavigationAction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857b932486fe9d28f71212dd31bb46202", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKNavigationResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c28fed56e49640297cc4f09b74303cbf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKSecurityOrigin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e82808ad4254b49bfbdd19f0061a03cf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKUserContentController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec3a8ab2b760a353fe0b37e7bca0cfd5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKWindowFeatures.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9822896b858e723cb5d4b27e018c2c6324", "name": "Types", "path": "Types", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98526f7daaaff3623b91dac3bf902a2cc8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/UIApplication/VisibleViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9824cd3e35b99d634d02bd7a124853db48", "name": "UIApplication", "path": "UIApplication", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fdf9db9160404e1fdf871158e111188e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9893318a5fcd4f6c328c4cdef6991ae2e7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982f24923fab49b9e05c924843b0f3f15b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e7a97734a58a93320685aa6dddf0897b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98040355d6792708c4fa3ed6123321a725", "name": "WebAuthenticationSession", "path": "WebAuthenticationSession", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd9b6db0bcf62d10527b2e147c4fcb81", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98932b88a39d374f3402b07c0c2a26df0f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9899489aff3a4db84058ed40e7dbf1a960", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "file.storyboard", "guid": "bfdfe7dc352907fc980b868725387e98686dea043a7bee3cca5a4a022e23dc5f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/Storyboards/WebView.storyboard", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b6ba0784d6853debca54f7c907a02ef9", "name": "Storyboards", "path": "Storyboards", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dc9d81ea1778f43866e7e83a2b75913", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4b2e1bdd597c0f3513f0d48cb2bbc89", "name": "flutter_inappwebview_ios", "path": "flutter_inappwebview_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e35b63182acbbc0712806be23824f947", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987881c9bf862333cf660d3dc72952cc42", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980913d656139f850f3e78f90609069a6a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837de8b92b0d38363bbe7f030e27fc07b", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872e4391559961fd69f068163e1d97046", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcc575b682fd219c67b0e175dcbc3578", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898a75e8c9dfa98da256b5a3825c6d91a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847c6a1762e00f8697069bdee45cafadd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5595ac93d8c133260eb7b078d3db9fe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d523b841aa3608c43b4d838de94e7da", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfc029088251377f8435c6506fbdb6ac", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9876b7f3069a986b50b2da26f71debaaf4", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/ios/flutter_inappwebview_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988087438934b44b8cfce2f1ee5d083065", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98886395f0ee78dfa2f1275f21344b2739", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987f12949b9c20d06108484d8143d3d57d", "path": "flutter_inappwebview_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ed226b223c10351d0ba6d0847ae9a07", "path": "flutter_inappwebview_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b69aca77ae9c3687558ad4b60f31590b", "path": "flutter_inappwebview_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbb28ded14b155f92c2956b22ee0d12c", "path": "flutter_inappwebview_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8e9283dc2973e6a0e1b59767fa4424c", "path": "flutter_inappwebview_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988069fa97d116fa670b79b62600c7d93f", "path": "flutter_inappwebview_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a40d151c0ec4df9f6df9b5f82f31b74f", "path": "flutter_inappwebview_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98135831009cf927b407bd21373bd521e3", "path": "ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980d11924faff850bef07fe3cd3daf19ba", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_inappwebview_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5cee3eb068edbedeafef97af4d233e1", "name": "flutter_inappwebview_ios", "path": "../.symlinks/plugins/flutter_inappwebview_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fedd0b0e1d01809da783c8a5efad157c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/ios/Classes/FlutterKeyboardVisibilityPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f019d3d88c54d20cef35204d76357380", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/ios/Classes/FlutterKeyboardVisibilityPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9840a86bce8df8a08d2b738323ed258dd4", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98750be102cb73bb443ba5673d18e360b2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a289188a4341bc99f71c81fa942287f5", "name": "flutter_keyboard_visibility", "path": "flutter_keyboard_visibility", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0aedb10afe30ca00ea8c3534a8c5fd9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98840ab547d69c4e4e99e38c9a191dabc0", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980316fddab9c16c5b889d88e0144546c2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afc71bdbade240d3b4f3d5d89ba76a7b", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985594a60697f12e9aff00cbb37413d0e7", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e18743c1554962fb7fa13a2668e6cb8a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983294d626af70649b20d7669f1a7b9bbb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d4ce237b37cf46f120b3160b4e9eb48", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cff3cb34bc42a1fa28e42184c00e34ee", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d697a09f9cef7ca389eb156bb4aa7c4e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881669be02df496cfae5daeb376c9d927", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f8ff90acb2bd49a3ce91c313033b8780", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/ios/flutter_keyboard_visibility.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f9f44293ff12bd08d0ce276478438d6d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ffbbf82320b4ad1749f76d4523450703", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98afe6d779193d04c43dd40b7754dcfde1", "path": "flutter_keyboard_visibility.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9833b38384f65246af4fcee9fa3a519429", "path": "flutter_keyboard_visibility-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9879adfb3afe60a9907323eef87952cf29", "path": "flutter_keyboard_visibility-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98edd158a6798ef8511c575c439f0057c0", "path": "flutter_keyboard_visibility-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f2b7c5f4a91aaf071f7c1126f398f1c", "path": "flutter_keyboard_visibility-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a84ee237d864286733b70148f3a60f02", "path": "flutter_keyboard_visibility.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9824d595c737b5ceda41d551501de9b300", "path": "flutter_keyboard_visibility.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b2a9ea54c87865331ca86d9705b800c4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_keyboard_visibility", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986772e769ed8f007b0c2cb4541bc94bca", "name": "flutter_keyboard_visibility", "path": "../.symlinks/plugins/flutter_keyboard_visibility/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985929f908acc7d37c94495f9e9258efe3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-8.1.0/ios/Classes/FlutterSecureStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98797dc74f0ec3b0eccf05ac56ea7110a7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-8.1.0/ios/Classes/FlutterSecureStoragePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d95e659e3dd19a081c47e0975c223c3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-8.1.0/ios/Classes/FlutterSecureStoragePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cf4408d8b9fab6bc527970a118914e76", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-8.1.0/ios/Classes/SwiftFlutterSecureStoragePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a193d33339b614d3d21f95ea1d96c661", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e6393fb55bb7fb69925bf2e2b7ffdfa", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b7a2e9c91527e57927329d0e2d29e0c", "name": "flutter_secure_storage", "path": "flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f469295c03fdc8cb5295b0e82a9feb91", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bdad31b8f0cfee37eb41722a954a8cea", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb1c9c5ccb8c4d834768fedefa23f7db", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d0c371b9f3416a9eb6d5cb6b1b8040a", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98709efa481051489bf47bce34f4978f64", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801c7c71c3c503d7be3767a3785440001", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856d5a6ddf9d429119ba997ab06b0fd1f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f54104697c224e5a53029637eeeeedd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98450e43c1747611e90dc59609915ac2be", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bad0e209be19dbfb2e134c79b76d2ddf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b1b0997795bc150ebd7a9026bb89ed2", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-8.1.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981e4d546dc7259e38112fb3d7cf499d10", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-8.1.0/ios/flutter_secure_storage.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98970814fe65c21f636cb95db7cdee7edb", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-8.1.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983db9f30de5f3a583466dc5965fcc1c95", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98deaedb1db7deea733b87022c1281518a", "path": "flutter_secure_storage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6615ff33c5cd9c1e855d90649df30e5", "path": "flutter_secure_storage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98afd2d1bff5b3e07772fb2b105c29157f", "path": "flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98872f33cbc6ca9a92fc540d1648e02cc1", "path": "flutter_secure_storage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9f8372964b6f5479f11263047323048", "path": "flutter_secure_storage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9887ea9d6ded49c33406067523778c8ede", "path": "flutter_secure_storage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a3e013c16f9707c56aa605e1221c706a", "path": "flutter_secure_storage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980ebd33b6a8ea6a27661f2ace851423b3", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98857beb2f4127bd4539187d450750b74e", "name": "flutter_secure_storage", "path": "../.symlinks/plugins/flutter_secure_storage/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986ce7c165e1753c8b4d3f47b2f9e2aa31", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a365c60af549d1a2c75c6cfbcff6ece1", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98143cbad3e3156932b5ecae6e96ed1fcd", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980478de34bf2df98cde365d5a5a78fa3e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983595d54cddfd729460a31688ad711854", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6a277ac6a435539108ac70e30b12797", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982599ce17512bed3d738e97faf4ade22d", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb657117777752aa8b69a2153baeddea", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809b0285f3bf520b43e5ffae29e77c653", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1eacf23124d9a499ed595f18224e3c9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98739c9ba2ab2ea6ee50ccd69775faf7b8", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ead0c1bc770ccf48a08f586d286a2d", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98baa0c292a6e7551d6b94efba6a2ff818", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a4747a2d065458329c1f905d7ec62e8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cfd8fc48231649067d7e0b9819723b75", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a6bc51bd7e2a20ab24242cfe3a006db3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98879c6fd1aeea93b65999d04ead9c0fa4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fac83c9c96ba5be9c6ceb02811af0be6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98de41ce27797fb1e287715c9bba6de24f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824664a477a050c5d1f0bf26e596a804c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d20d8ec972e3f75e861a926cf2b27fa8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d30479bdd9f6aeb6599b84c419ad015", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1049db24174ece4ef74e6830be187e8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98229bebf5a20d34491323f400fa0f3692", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e41bdfc12cad73890ada1be91bdbe3c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a0a506d9197fb471253c81864498ab15", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb84c9bf22fd7a292df07f8701649191", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985768bc641a67c489bf74eaaa1fb89288", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0f1eefc98cae1963d51097512eb88ca", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c1f703f49079560b5f16f6708ed1b43", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0708a2c991c3fbc868eea1961c1c7e0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d5526383ed03806dea2b31e20827f8a", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876c7e2379d54e3970dd1c367545123bd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898642d775b1103e9a50c1f76d692701e", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986617abcba70ae2b297d60126bcd8d5e4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8007deb9aa985c9902f7a3c8e45342e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab632d6d98c7ed35b939ca8e5b402255", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dda25950cade551d3e5ab3c17a6c0005", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c34c38d041ebdfe9a6fdd30bf9ea92d5", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841888ac91588521c6d72f52f662788b0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98895c18c32eb503f77d357d6f23992973", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980913c9779ee29595161938b6b2b02239", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f725db564c621e22efb332ab3e996347", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfc116b9af8ba70a7e7b888d12740eda", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c04a5ad63e1fd790a017c0d7af63f50a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbb11c8f397bb42cd569cb525886f535", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809fe8ba2498239ec54311956d657caa2", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98338fd087083e5c502ecffc22d033f4b9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986f3ba38aad23abafeaf94f87c6dd5d3a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98638b33dd4c0ee1bd1ad0bb7fe4b748d3", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a007caa58fea38cd2db75bdcc9ded379", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c90e687dabd480ad60135d1df9d1d449", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981814d323e02f8aeca5ee74e5b61c523b", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98574202d592be511bcb4b25d893416d44", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a6767eb396ba4e75ba44353666306af", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ee46ddeb1817d930a9d325853d29a93a", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98717c4f13dbd1ed907a3a99bb4386941e", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984a0d0c73fb8b21b4af084f3d90ac40c9", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98641f67bcc5da57e83d43a167f0446c2c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ead099668d328dd104e4fc25b22dabb", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d78d1a495f7852fff932683b8d29ee37", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/Classes/CydiaChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988066154f1d2bd2754371f5aca4a2026a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/Classes/FridaChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9831ae3c76724f4eb75eda71e9c6988f6b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/Classes/JailbreakRootDetection.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b52f2d300346d5b6e6965e58d6dadc4b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/Classes/JailbreakRootDetectionPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c86c710ce4931b3679196385f0eb92d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/Classes/JailbreakRootDetectionPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a24ed5f348fcc39ad34a72f2a48b971e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/Classes/ReverseEngineeringChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9808597182f25f6caee98d1cc73ff29d9f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/Classes/SwiftJailbreakRootDetectionPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b4d8c9953f6bfec2bf3d3ec0972ece4b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/Classes/Extension/UIDevice+JailBroken.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e3274001da76589fecd3164830a8a23b", "name": "Extension", "path": "Extension", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6c2044b089a077e4c3cddbb0d69e851", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b48ecc16bbef0e3ef706746c28b467ed", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815635717de6fe050d60e755fdc5219ff", "name": "jailbreak_root_detection", "path": "jailbreak_root_detection", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98208049a163ecb21c17a8ac3cf73c9b8e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4df9431af4e9607d37a9f46ab964835", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d7ee69d3e078e0280f8c0275c347e87", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8959cb55878d649654fad467b32e3da", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9a08c6d792d119a51d57c7efb7ad2f7", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d087f44e8559cd70c6b57542558a5c4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984823acc018653c5983c62e38287fa881", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a08085fdc8918cdc27b4f7e38366a61", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854c58919982bf2616d0472a8b31ace66", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98541584d6ebd8b8cd52c49fd94ccc7cdf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982866b622735e47e8fe822c7bfa88bcac", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9898ac7d790313e351d6d8d10b49f8830d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/ios/jailbreak_root_detection.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986595eadb3e9e053f2065d5f707ef17a2", "path": "../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d47c11dda612b20d4982c2bd6fae2c13", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98be0af5346f672a05bad4b9ddebe4b468", "path": "jailbreak_root_detection.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce33018be02ba5ec1fcaa2816885b7b0", "path": "jailbreak_root_detection-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98173e68024ef6bc3f125b7d17367cefaf", "path": "jailbreak_root_detection-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839d7e6ae311a2668a42fbbe6c5434b66", "path": "jailbreak_root_detection-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c73ed2b65e37224e723004dfb78fb9bc", "path": "jailbreak_root_detection-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dddedc137fb092d0ab5c6f149700ef3d", "path": "jailbreak_root_detection.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f0da793d6d935998164fa240dafc02cd", "path": "jailbreak_root_detection.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982ec00475feb437e6d24023bdcfeb740f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/jailbreak_root_detection", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8d69b191121219a2826cb869fbcf562", "name": "jailbreak_root_detection", "path": "../.symlinks/plugins/jailbreak_root_detection/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f34356c9c9e450759266ede08f904005", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/AudioPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98afe66efb916ef458bc376eee718420a7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/AudioPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982aa8c096085f0e21ae5dd691b2567387", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/AudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98998c9504279227a88a292b73ad61772c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/AudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857f70538118b9227d7a7124fffe1ef0b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/BetterEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bace933fd04f4b57377f0850c3f2d5cc", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/BetterEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f00dc51659f400d598a6fe0628a568d7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/ClippingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989235fa30ffb1f3303305f8a2d5166f73", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/ClippingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894ce2cd2ef85b038cb1cba526cbf552c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/ConcatenatingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1c67a310a392891dad8a431197f7da4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/ConcatenatingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b46bb9a1d8c5815225b8bdd95b67b593", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/IndexedAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815799cfe5f18599234099b4e96d6c94f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/IndexedAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d14fee9ee2162eae5c8985f65e797f1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/IndexedPlayerItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988186fbfb1cda4b0aeac61a08f9cc78e3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/IndexedPlayerItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989691b2aad3119aa5c75a636216b9d46a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/JustAudioPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984735cf585c793f82daa426a6dc08d051", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/JustAudioPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804118023cc88fdb149bbb44f4793a1f2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/LoadControl.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c1c25825faf02f954e2d46394ab65e5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/LoadControl.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840f374f418020ce9171113ea9f135748", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/LoopingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f1eb0db4d7b5034e34d424aad54fd9d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/LoopingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98871e51527d3157e73f95c3276e139fc1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/UriAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986763e9d0d1e9875e638dae9cb7b8eafb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/Classes/UriAudioSource.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9862d25709d7d3bbd417c873a7b19399f6", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881c5d827750a8f1cee8dc9237767861f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea48c44e735a108eab6cfc7578424a35", "name": "just_audio", "path": "just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aaff934109e07bff6e7505e4d5d0d5a6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98019a2f13ca60b8cd3bf95de27dba0d48", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2a802e3aa3577e77de44401ac1bcf1f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa350cef46551d7f0469dd5223a33366", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98554b2d1aa5b29ae93699e96c8debcd51", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a03c86ba00eb1a8f0a7630fbb4b3a131", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98093c30a6603c14d05e912959f0c88c92", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981332171eb077fc7320e2773a543e07fa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c01f0fd2c29aaa290fb39e758aba7ca", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e007ca55a1ff46b9e3b015777f6a6e0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d0c42aa9306daa871b13e1c51b2e187", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b6c8bab93d96315cdb57596b234d932a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/ios/just_audio.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d5ce71096cf0d222599a4432ef04fb17", "path": "../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.41/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98864c0f0c9f7b57b3edcdf73f67793258", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9814288191b95cf726369e1e15db4695b5", "path": "just_audio.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9813c55faf3494d83f35094e9fa141398a", "path": "just_audio-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a83015d3a35d2ad2480bf205668cbd1b", "path": "just_audio-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a3b14e5d702f504eb114d2da1c69cb8", "path": "just_audio-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836ff7594d7fcaa6c345d124239980a16", "path": "just_audio-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9808537e94303435bb54b9db3af37b4310", "path": "just_audio.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fffcd4dbfd8442f8d1e0b3cd29f2a285", "path": "just_audio.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98de9cd78d210939948af9bb7d47cbe379", "name": "Support Files", "path": "../../../../Pods/Target Support Files/just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857a706f34572b3d0e40b0884827f24bf", "name": "just_audio", "path": "../.symlinks/plugins/just_audio/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d7c47d8f8b06ba5e4875e6a96469b92", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.0.3/ios/Classes/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985917eda6b26f6446ee625dfbe2bdb53c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.0.3/ios/Classes/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986b27147224ded81ed6806489ad1bf0be", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab08b42b851b9ed9ab99d464e2acbdc5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d461a5965d8330fcf41e76651a9b90a", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982dcf08cfe2ad36c887078686336dc194", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879770474d6ebc2026fee09ca92e1631c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecfd42004ead5632a51a9e6023b8e9a2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b5c8d853194e91897d87a7d03f16a37", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae33cece03e23a26a89ffb913bb05f89", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b544a93cc27985dd2ff652625da1aef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9828bf46fccf5671a77ce30bb7c351b930", "path": "../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.0.3/ios/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ea51b775b70e7f6103ba9bebe2fe3578", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98307ae500b6686fb759534cf44fb1f61e", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4f6f5877e6b5b6be3571b99f4f59867", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882f40fd8e838fd157ef328f5ed52f334", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881df24533edc131210df1e01d4806a5b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d195892be756fef3172699cbc5f564c3", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1a7c91351de696614a510d8f1346ed9", "name": "<PERSON>", "path": "../<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820b310970153118022e4d5a1ad7ce236", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98039f94bc60421a65bedbd239c1e8afea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e7483c232ebbc0b521a54b23cd68d2b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cff2495425bacaab1571ad5f53d636e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ab12d046d7af52a2348950903ead048", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.0.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9884ecf4724fb3c305a4d5e908cce21365", "path": "../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.0.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9889b7f84027fe29ec1f84aa7e24d8d1a6", "path": "../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.0.3/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ba78dab48a81563afb901b43797b0dcf", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980e906e9d32135671dc4f15bd92cb3bf5", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1b99b760438bc4fff5ef7b285dc3bc2", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9837c6d698698f1407cd36d424cc2d4c2a", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d915b28d3f7a925407f64357da2cee31", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed50dd510ab462e43d509b5e72c8437a", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f813c51389deeda6c5b92ea8195d5d16", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a879d6e931d4c2e35e2a1a3867924d3b", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98256fd451b268058b55e6f7b525c052fd", "path": "ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98722f6a39b0944990844b291e13d29d93", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f60128e665aa5698e12fd0992542a69f", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e985cf94126cc210daf1309d18daca86750", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f67a46a5d14e91518ab9ef3f1c1b9ed3", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df12200c5b1df1aff716c2e64ffc9663", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811046891ad30b8feff7af47c2b5f1431", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a03d279c74655346bffe7a741000185a", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc6657dbc1a488d8a0ef7b89d178fcda", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cb68cfb688630bc3ee3df0628025941", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868a3501d5ccf3604e4047c0054db96e9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c2f156f2306475025ae97296fd4f512", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c37d7c39b65053458238812e590bfec7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887bae540eeb62afaa87887d95dc117b3", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98570a9d3c1965510b9b1464db7d3496a6", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a952e70ca5de33facf64588dc3fb1400", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9177ec93fa02484e769e35a25227764", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987e445328e812a96ca9b2b5a269483e5f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9849f4409a7ce6a93d56797e7d9fff9d67", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a83f841203779b467510bdfd32caad6c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98febbf9a9ce1b90a92666b611a5ebd3f5", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871597d20286e2592f045d1de4566ddc7", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6184a4a1f20c0f51d6321c161b83bcf", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d300b6efe13d3318b66b6b5e79b1f67", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fb61c23aa3dcd1d10a1a93754e9e779", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843cd4e15f6d3d1ead1ce45a57717b8f6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98277d37751dfa6b0c8d9e5e8e8ed02ac8", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac9fa9315ebe68cc3292c2941e36cbb9", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab3fb3d7c6cf025c38287d821437afbe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a316f7ec03d1221de3a8e134a86304bd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c35b9dc7e8385314d4d6093162405bf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98394819247f17856fa3bc8baf3640da41", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6712ada81c6cf860e560ab96b13911a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a49dfc0c475620d044bb59027d02d55b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984fcec29e4151e7cfebba5e149e6944fa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aed50cff06868af5ab88e4e234c2ce39", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fdc4ca0608d6c7cc4bddba4ebd9ded51", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b44ad9a28bb07b75af5a07ccb8884cdf", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a1d88c7e567243b7c7ab609a0f779786", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9860aa1e1fffabfbfd14c420485de6d627", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dfb0dbcf7184fdf0896998d6e27608f1", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988c1cbd017d5edf1f067459a3963e6d7e", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9ae9591de1e4a1c6a04a79b7f509dc3", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dfdbe2794cc64a23b5925e9789b4b5b", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98abc06c8180bf250102ab375dddad3c1b", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9816176fad04bb85bd938fc877fa0d6d58", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988bdabc37c4354aef0fd7508acc7f2f5a", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984f42026d63242eb257967af2cbdbca78", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853d2090cdf3141dd60261747184cd1ee", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9846f2704f5e8e3009c753ca607a4475a6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983e14ffc230528463f7e1d81c8f0b3e8d", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865830d404519218026508ae61e95e80e", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98692f557357b145d6bcd06c9df6011492", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d620205f8392f6bec9c4d6c02d1196bf", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7f73f76a72322af97bd043d227c42be", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aecfbd8c219c7f78b3522b602ccba028", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98040a0f886afacc785d0aa0c9499a0d22", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a73b7e871769f77241d24fe7f4f259d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb533189a387f4c32a55629d11f73745", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861b87fd8f90878b956e8416e6c14a269", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1bca90953797a6c6cdb509d86d2e36e", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b853638fc99434802de45c0bd8f7a28", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9866a4fc1df751057e66848839d04b4f7f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a053a08685335a05d6a0cc374e10549b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98de0cfd24f8c009329b9a4aed744b0d7e", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98196eefa125084d538a2470ee889de274", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870c19ac1083bb159a312438f689dab40", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814254fd86627f8f0df79502e447c5ae9", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987437fcfa65f68ea95d045b76218da1f8", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a43738443701ccfc7d8849c78881d684", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed4bbd1ef2bc7ebf274e4ed2a35c64df", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbdb6eca81ead56d2381f691284b0647", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98725f0e97d7ccf9c6d28081794d8270db", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a464d3510f088cc338f4bcc01816671a", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb6622355917e4040d05c9805bbc7515", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0d0279315c45be7156a1d195fc04479", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984de476012aaa7381922ba41e715004a7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985be646666880ef1bc9bca185b181e333", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b6e33dca6f8d08bafa2dc15e5dff349", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b54661f02f5de95ac7cd2406f118a567", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f53fd7dd4cdc247e98b4492f444a3df", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98559c46e36b10366f44aeaf911b4dc886", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a654e4e107fc1874d9eea66279abf539", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98593e35c393e63ad8e0abd8cc85ef4de7", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989d74c355e9c1adec80fc761ff74b187a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9830cadf1c9f50d5c6d7df29250bc430b9", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986e044a6e2615178a30416dd2cc685e77", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b2d557e8197bef4716a3d3e9da6b1fbf", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98086443469af9e40b90e25df05cf169fa", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874c8f37cf539201c0b020dfe418bd906", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6aceed396f98396668e680b27ed39dc", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982b6d71c98e3ae90a286ae15b6eeca414", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98994d9feddd19b3c5a030fa88e40bc5c4", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986fc78ef88e6a9782c83ffef80e163062", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838b0d1ef42da341a35919e4f68b55fdf", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989eab2f7ab3f33f8bdc27bea96ba72339", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9814d513e1f18247d7a4bf0d9bc8e4de02", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c8b0e3c6ed8f314eeda7a4d5be60aca", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e597b0d2b39d58bee5ce1cf8c6af532c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a4eb92d2f134553d8e43765a1199d87", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6e37f8c1835827c7e9468a340405182", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881f841d9e66487251ff1a465aed73cd2", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcf5a75c2ef8dbe15394da44d0f643ba", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ba8ec04c9fb0b6d97e45d400f243f38", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836128ce31ea2e6b1fcb441b5e591e07c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf5d0c62926dd14b04367562e4235741", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c704e3f9b3230a871c704ba591a2eb36", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1559dc24fc99e4d1536e334cad71edd", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c4d999e5632daacf18ab1bfbcaf0833c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98507863edf002d84db6c6448a1a1779b1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98007cba181cfaab851432283717820c37", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804f6bdc97fec50c4b785b5513d5e870c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980071a336a3dddc01870bf8023ee8e4dc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98419b3e2246604bcb15ef290bfb07425c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986261ea303a3ef394f38eaea7915d19d1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9814e4718e72548a29a8107cc48b51d405", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987708294a54824a7386fe44783d00450f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c45a463efa3cb7960e028a78314a7ab4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98771b445d6b311af715de444add43a84a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b87195f4b41b764eafe3cfe19588ba6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbc260b0fe696f878646be9af1c6f7c8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ba67827e05393df709aabe5f4ce18fe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889c6304675a34c2145a3611afab7027b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98daae2cee363926edb2298002ddb9425e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98afcb5ada25bbf950dac9b06e2ab7fbd2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a67664866d2ebfed315f57d2826c359d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b4d1b031ccb5024edc7734da601a1ad", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9861acb01d08734303785ea24b9baa0042", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868898419dda699ebbd68ef123311ecbc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984d53c21c9293e1b470045698c7347d00", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef7edbe8d6704b4b56177fefabbdde9b", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829a7712d127e1c3affd93da63694290b", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d514b35f8101296625fd4d8f58d66a6d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830136879345e5cdeb69083e5cbc5c203", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983842fb1277de99cd7503a9ea575d50d6", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984786bdc37f777877e119108a209d325d", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6596a333b42f69a00bf6cf93dccd92e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a691f9b452d4fd4aee34cbc6f68527ff", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ddc016d9545de61cc04a13063291a6a1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98036029eeee47b9ac2ffd75d08b1a7b32", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0d52df0f70391d97668cafec18cd3dc", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af942cc455b2bcbfee66ce0f7388cd2e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888950e4e9f13340aec9c7b1f55e05236", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d933cf6a588885f344ce69f6aee5de3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a22b2bb8a75bf7c4cb0b9a99d5e6735", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848f4559ddef5b79335e30ac13fd87351", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98156f423895181b839c5feaa9c2ecbde4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f99113d71d8a6fd03d8acfeeb02a28e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1ebfce54f86a0d1d4030f9128782ee3", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9878f638f53c6a9eb35ab87765d9c73e07", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e986532c075e02a5c4800bc6667751db863", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980857299298358dffd76753a5a0680b19", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853a3a9b40d316f40eef006e7623815f0", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c1434839078840cc09a93a37f37dfa47", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983df06f0d30c93f3b35531b8f9bbfe1d3", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fff8ecb4be912c913b7b3437959d7bc0", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9871e3f3fbb87add10b2d46922c959ca28", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894902471619fd54bd4235618b3ae61e5", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897c000044e981e8669d0b6d89a0af87c", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a3c1ca71646a312b8ce47e5c0fcdfa29", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9869873f5694a44e2370536e4c133717e2", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad81cee3aa5db011850db957a85071a7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982338138b93473015d8b52ab740c8c9c3", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e988d3d598bd23aadf5fbb80a4c5cf35a37", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd77fcb3d07c13edf6d6d7adcb063103", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805996f489334a5a3badc308d8122d329", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98323ef5a70504a4f01074949738299f94", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828423278bb6c59f18b5a3072bc8b40c0", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980630910a94170750b3f8949d0bb3f672", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ad172f06aa6a269c6b3e87c26b4e173", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894b728ba1f6883b8cbaff851328dfbf4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e6a38673991e3a8a2e2758018ece4e4", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb0c3c4c30e071e4f030d16102f5d76b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e64fa6befbfa6c9f9674ca1084e9341", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dd81e674e27929d820c3feccdcecfae", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2087db970e5e70b765e06aa61cd6688", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9889a29b15fd8304f3a4991943c079f870", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9813f584d81d3984874e2e8230b86cf1b2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9828a13125f0f0258348e08b8098ba1493", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9858fbeacbdec769aae3c6af2693645393", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c9d00a93794f2bc04ca859119a1a761", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841a60f81b1b869c10e4e6044d4a388da", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8736b113feced15d2f521e5749cd83f", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd6f93859483faba2ba37a549f83e388", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ddf4e716505b7a44e054e070e41d180", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d4fe821179c1612ec836da6b3908377", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898d52712b7cb3731bd3e7b44f32a0147", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982174b2278648b698f99b6ee3bed7acde", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c53358ca77f1319b733bd533ab4de4e", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981bd0e7f3354d0e21d329363b2bf0fc22", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c8bb0631de87e3289f60d3ac5addc6b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984639b7fc54b927dbeb0b7a2e00843b25", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b98c20706a56cecae868263bf549a294", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2cb77a0ee07a0c84d4f99d6c748a354", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fc542778e2bf18dbb16e99b03455cc0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848b829861d90459e0d9881e11d89d5f5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c3e32c92ee4647a23402159a5ff7440", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98504bab2d57f574cf31a479507e0abdc5", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9808bf2ffc73bad881b8a2d1e390896d6d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9847379eaa75e8bf45ab10759e91ce3adf", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98540f74cafcedcde1bec8abce9c211eb5", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ac702a75c34947f76db27f86848b5d57", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9894ad9262b051a39f30281868a2e5e1dc", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b94cda483202be432b8cf33bc14e45e6", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98131c1a47c00097fba3f88729e4f5e475", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac9ef7fe6c088a6eea7ddc470cdc63b6", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98380c351be0db8c96941a5f5cceff41fb", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9855a2e008b8467678034072bfc2ba09d0", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c4ed2c143145bf3f19e614da2f2ab19d", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9858584387227f094bf982ae5e1544b407", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d251ff20e8ae4a70c16381ff03e047d1", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987e3315753ce7dbd58e4e5f936a41399d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c4ef602d601e817d881582f84d6c0e4d", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6a04d09517d04219be2e079b805dbe6", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825f1203674d0f1bc66d300ed645ca54c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820213c2efe6cdb2939fb8daf93f0a075", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98475f663e06fc087afdb52465ace674bf", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816090e93ce4024721548923ff861659b", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b4c2e92a124493adae14cad6b4f7cba", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98309dcaa6eb25251aaae8c7dfe22ccf3d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98806a2c1d514460a53fe9159fca54f9eb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c306e87a21ca3625dfca527a1b27a25", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab60e9ac0095315be7b40435a1c0dae3", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853ac8e6406078e5e99e492600eff32e9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa308ec184cdc8367e35a90bba514b68", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef3eca49d9c4381c107b4632c71e5713", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986186583669e757a17c01a4efea274114", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980264756c64055d018a0576fc8d7758a0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b089e2ce5a68b2aed7f272bbcfc1fb34", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec1ed08e1f59b1d0f45a7f2130c6ac53", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890bb2eae7a00b07a8576bd3eb8d136d1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c27fe4584877e51aed84e95e42c8087", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f39934aa76085ae5b93ce6efcc3847c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b42a792b0ce6dd09a47bbf6628f7056e", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d7df0994bdca9402765002a3e5fb90b", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf7ed840787cc8b49611541d22635356", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98390ee904f2f9d28af56a5e0d1de242ad", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPDisplayLink.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c35c84bd7d3f5b76d330646c6d37c39", "name": "video_player_avfoundation_ios", "path": "video_player_avfoundation_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988df1356f7fa0f975ee0b3e235863a544", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98590c42fc815c879341dc075577e55930", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca0cafdebc917f7d3571ffe11bf78303", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cdec78d535a0d02e0f5fec7df976c596", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4d5536a860fec2f6591157be4648e9a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aab1eed547fcc766b6963a01ddd10756", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2b5594691dc5b25cd84f70af8e80366", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f76bccecb54dca7ad41431ce327dbda1", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98087e03d312d6fbfdb9866864d166395c", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff7617f9acf3762759fc91d82b702023", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d54a47ed592bc4535421b1773a50d2a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0f7e0849499fd0205387c4a7d52ed1b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb3c3c00127750c30fd84854c62f9e3f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989938b05314bab5fdc265c8ccea392a33", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98807af3d56ced641bcae2b364ad60ae80", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98287f4cb551a37736e777e7cbe30e55fc", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98220489128803ca49139204d4a38f9f34", "path": "../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9868ca625cac0a556c309a29a07ec27bad", "path": "../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9868ed32ae0ef65031c7a5b6668a29b123", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984aa2150d5a9ec6efad6ddb35bafe487c", "path": "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cdda63e3740a6610b028313c22fa9421", "path": "video_player_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e67f630e0865e125f6dffacd412e30ce", "path": "video_player_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98eda46ae74d97cf0fdd39093caa1601d9", "path": "video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0bf79a1c5cd3874aba71b08c39bd5f5", "path": "video_player_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983495af4103a26d0d17d64e65d482dbfd", "path": "video_player_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98311f0554a1458b707a0510d0feb62519", "path": "video_player_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986800e1249868e8fb0c36dd53ad04f54e", "path": "video_player_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9807a103a7c6ffe0fc0df7b0eaf18514ff", "name": "Support Files", "path": "../../../../Pods/Target Support Files/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98411fb47812102dc00c4503f03ad4bd1d", "name": "video_player_avfoundation", "path": "../.symlinks/plugins/video_player_avfoundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca3750648cfe20f56ed85e2099c227e4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fd0e7dc7d142f800c11eb2052e9403f0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c279e87069be3d9da10b868dcdaa002", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/UIApplication+idleTimerLock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cb75c9ee07570c53b81bbbdb92a9ca9e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/UIApplication+idleTimerLock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986df71cf46af070bfc839e7dd9bfc818d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/WakelockPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb57f090785fc037225c9a04b3b203b4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/WakelockPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986faefbc43c96b299d303a5c201034f8e", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9826a0c8bb1754fdea24338cd38425fadc", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ddec3b181235526be7520828741421d4", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df4b64becc8c9bbb5c18c7aae6820f9d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985679d3d51351921e8de1dc436edf20e9", "name": "wakelock_plus", "path": "wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1320125d954c4194de1defec1ac2359", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9b9dcfbad341e740c92f3fb0c5d2cfd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aab3a29efbca0afb0aee0171229a765c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca19a6c0c2af0c63f8090e6dd0c4c001", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e43e9d726a2431cc8350bdd32eeada87", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d71c5b84b61930f8b2d765178d42338f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cdcb627f66cd9c171fc11e7034c58c5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9ffa6b89e21abd8c6db2e3a0b2d6020", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d150b5e89c8fab3acfd22caf741408d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c54fc7136502c51b304851f63bd0973e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9971266ac889a64f8cb05126d70b47d", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98cfcc973dbf5e4d4d22b2bf445cdac73e", "path": "../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9804be24f9d28f4f9d21ee7ad578243d37", "path": "../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/wakelock_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a310ca96d8c79b183f478f3c6cf2a2ae", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d232d3ffd2d8f31f233899ba2041bf31", "path": "ResourceBundle-thermal-wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98393c4be7d3a8d63efc62f1b56fecaae4", "path": "wakelock_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a5435a9deea41c2c26db3880f341d69", "path": "wakelock_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98adc646b339c1183c844660fd5e2e378a", "path": "wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a4c0009b4c77693fc27a19cb2d8f02b", "path": "wakelock_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa18bfad4c90e5e2518df4c68ff083ba", "path": "wakelock_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98be3f3351933cf75899612bd320121b3b", "path": "wakelock_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98769015c7cafd67e787d26558be9af4c7", "path": "wakelock_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984f9961d7e9bad0c83da0e5524b48e55c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9b78559268f5bff6ab2d20bdca08243", "name": "wakelock_plus", "path": "../.symlinks/plugins/wakelock_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e34642bcee1f18f2672eb89a797dc9ed", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ffeea7861ef41227f2b7f8769ab2abbc", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98694857a4a372adf44b11f00719e035f6", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ade5b62ce5c4ec303ba45a1a4a44db45", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98238c5a55b5c132d7e7f7c26f4c40abd7", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888df216796e3b32c79286bea00a44b66", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f84563f1fdb7b17ce4709efc0c8e5b51", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e07b6b251d01792e9982e1a9a9b62346", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d22702a2f36cd1c1819cd8344d4d4fd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887d5373a79e016a25434b539b1d6663e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980051047f765359312d271ab9d74b2bc2", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ebf17a0e83703c46d78e11c6e86d4d7", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d27e352b9a1ea621f83284ef6134f723", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986718a5e0c19a4eccabb879fbf220a258", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f9861d6605c3792c7e219600f7a0af3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFDataConverters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985b19057e7e186ba7e65db5ec004de6c6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFGeneratedWebKitApis.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982e48810ec79f778c836f7f1c7ad901c7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891d5d820341694d65398300e1b8be7bd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFInstanceManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a23964269fa426e1d9901f1897a18fd2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cb90772c82c99b50abfac7836cd2f545", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFObjectHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825a62cdeb1d5513d980441338973a2b8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFPreferencesHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a8c38d6953117953006489fed3639aa6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d7bf18b40fcc42e4310eda8512ed5dfa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982ad2ba76d3920dc2571a42a7bb369b6e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9812eaca8bfeeb55cfe4ba189513299436", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ed11a91f5c56d94e0d80eee115ae3237", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b69d2ad6e94241f52db105601d65f847", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863f1c2cbc063edbfb82c07709beb5902", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLCredentialHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca7378b2ab48a01847d68bc9a10c60d9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f67df19dfbc832a22c1d728e4f482dc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98007200f57597db2ca06ed3468d74a78f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUserContentControllerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f593cd221df0e7e0c3829517d46484a0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a98762a928785ecd666ecbdfde78cd9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e36e0427a440906dfc8706120860a3f1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988eb89d850707711391079d03d5eaab73", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c30ca91174d7e168eaefa0733b03cae3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983180d4497ed1424649cc6d2725aad777", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1e8ec1ddd9de73ae18b9b121f97ce36", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFDataConverters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfa61e28738a5bf3cde0bedc11a8158d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFGeneratedWebKitApis.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845902a35fc3cf0ffe5d8d71d559d5326", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987830440cfea84880e4b69dd645f6bf84", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817c9c9967eb41ddcec133b4f51eefe32", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987cbfad25a3fdcf2589afc924d0c2eea9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869660172554d5ff8b788866b6fa9fccc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFObjectHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e192d4caf92cb00ecd0d627aa495de95", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFPreferencesHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98213999fc7d1a475a1392751b16eec17c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987cef3d227ba5d08521cf81355dce014e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e423b086afc9c11a3bc698374f75fe81", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e62948c586b5770976a313cacea7e13", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872f1c80bb11bd6b950ff986b5e05ad84", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff45070974f2a402fa5d8d0b1f74183f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984600ed172973dc37accf47b9a7ead73e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLCredentialHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c605c4331d545986fd2fdd000c28af8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987375a14450c9f5d171f32e619c39d62f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebf1d5692823a18c0d313b2cf09be656", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUserContentControllerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f1f99f82a43a6b1382208e99ce83bf3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981688b211278f4bdbf9f9d233021283f2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983cbf26ce40eadcd48310339ecbe962de", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98385986feb88f662aea51562f9b268746", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewHostApi.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f84e25f73755d76b3bf41dda72553ec6", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaea6e6096832c591d754f2c72f08aed", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ef5d07158339f78385ea8f3f2968ca8", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a038525269812710734b43e10aaffc41", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818f0ec6e78625fc61465751422d3e491", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988aaa61ec2827ff1078f6f466141c8deb", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837e00fed830f976a1fa70282c88a737e", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98680bc915210a5f930e30371fa4f5c54d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfe35af3175b17e3bde42024b726a968", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981dd3c14f6678cc2ca7f52ed05ab2ea16", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a12c1ff9c332a1759f321b4137d87def", "name": "mobile_app", "path": "mobile_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be3a5f1fd048fb489fe65ce0e3166bc8", "name": "<PERSON>", "path": "<PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98126f9449d9330561ed3aa6fe27eb80db", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f0b6dec954827d3cd126147861c3a44", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc6ed4725373241d097181d74c1b3177", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9890e53f31538225ded63e3ca6a40f7cdc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cda779e6228894d2ae29f8cdaaab44d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cbcb22979d6a517d9b6237e39c3bd15", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987453af96fcaa12fa4f4fe08081acf80c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d46c0e1308c3f28826a0b94b46b0a2e", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986ba026c959adf382168dae42fca36851", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/FlutterWebView.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982179c4fa5b0a1657c7013e399d95751a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98625d51987a6b9644e8074ceb21d84d80", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9817abf4ecbe357b4c90faa3366d490d32", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b2a5ba87106d6e7ffe39c91ed5e98145", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a4ebdeac10bd6a8dc81fc2d6ade1caf7", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9812676cb6ca590ef116ace5aeee97dd4b", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f894dec377fa366639431052abec8fb2", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb2efc0de5df0af90333e6dbf04d554c", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9864f150b8432f17d4cb38c3dfec6066e6", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fff1cb93f8a6477fbe45c40de547071d", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d4b1120477a456ac6f8697db11ca3213", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859d66b7bb6f7d7fe04eb022fb4830ae9", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a17312516e2dfe38dc5555a886933d65", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e980be491b844503953d3029603bc834554", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/CoreGraphics.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98a15b41da5b9cf00eaf9e8a12fb6fc89a", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98fb4060546442a0e4951e1b9a381ceefc", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/ImageIO.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9823116a8821514e966e1aa449d1060c27", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980b0ceeb3602d2c8399abdac8cd30c079", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d19418fdd45008b07b17f4326c01a60", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98264e9ab5a01359057a9f76b7adde5dd2", "path": "IOSSecuritySuite/DebuggerChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985054443d551553b873a364178e017c0e", "path": "IOSSecuritySuite/EmulatorChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98572e86abf0993a8a5c72519e5c840ecd", "path": "IOSSecuritySuite/FailedChecks.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b710407b25f9945a69b81858cc8627b", "path": "IOSSecuritySuite/FileChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b7e6003d005b269b0d4a7ffd51e1eb1d", "path": "IOSSecuritySuite/FishHookChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9894fef05cedca7c1ed59eccd5af6af482", "path": "IOSSecuritySuite/IntegrityChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985bc99543a986a6ddc6016153851bc477", "path": "IOSSecuritySuite/IOSSecuritySuite.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985b87a963af6b391d7f251a7a153ead41", "path": "IOSSecuritySuite/JailbreakChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9883339637d025693ad447224baef432a1", "path": "IOSSecuritySuite/MSHookFunctionChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989043dc39becb158d9575f54590e54ea6", "path": "IOSSecuritySuite/ProxyChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a1cee80ff0b9ae23105d4db705f906a", "path": "IOSSecuritySuite/ReverseEngineeringToolsChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e91f529f8ed0190b0c9c1a1ebdb64c09", "path": "IOSSecuritySuite/RuntimeHookChecker.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98296329026ab6b863f39060542eef32c9", "path": "IOSSecuritySuite.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9893063ec75a4f99737667e0663aec5caa", "path": "IOSSecuritySuite-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e133e9ba5554bd30256f6a7a7cbdc54f", "path": "IOSSecuritySuite-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984003f461abdc8147be2577e64b34ab75", "path": "IOSSecuritySuite-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4bb6f440cf9ec7b38d8ee547fab1cd4", "path": "IOSSecuritySuite-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ca4eb76d91bbd461663f4da29223096b", "path": "IOSSecuritySuite.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9855a7764517936fdcbd92e4ea7c63f2e1", "path": "IOSSecuritySuite.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cb42b0fe508b5be09990f7dde5954b0e", "name": "Support Files", "path": "../Target Support Files/IOSSecuritySuite", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981918221288111ab74abc2fd9de44ce50", "name": "IOSSecuritySuite", "path": "IOSSecuritySuite", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98cd4f853f43592d300472cca6240ea7d2", "path": "src/demux/anim_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98308be345ca2e94e431ded9dac476ca27", "path": "src/demux/demux.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845d00719ab7329dbf6994b1921b9b325", "path": "src/webp/demux.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989087d0510cdbaa0bdbf0994ce5d48ba7", "name": "demux", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98772773b56170c1b359b91e191c0f4a56", "path": "src/mux/anim_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985099f24af81382a4bf25179a3fb7abef", "path": "src/mux/animi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b992e8082d5dde666bc8c7c871eb7fa", "path": "src/webp/mux.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980d5ba071acb1174cae84e5c49a85a778", "path": "src/mux/muxedit.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98185c7dc6290cc364225418a48bcfa23c", "path": "src/mux/muxi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987c28fc2c8e3fdb5380ae8b3eb41123ab", "path": "src/mux/muxinternal.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a5b132fdbc82b3a2b808ad5c1b0edd57", "path": "src/mux/muxread.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98447cbdc3c9dba3b89c1280ce77e48fbf", "name": "mux", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9881d83b1849e1fa560c96ea99ecbc6773", "path": "sharpyuv/sharpyuv.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988106cdf040d51bfd7a3a849d7a4a3636", "path": "sharpyuv/sharpyuv.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989799fca27f933d80c8844ef62d98af5c", "path": "sharpyuv/sharpyuv_cpu.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860e880e72783dcd41b3f48559ad92e6f", "path": "sharpyuv/sharpyuv_cpu.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f2b758dd7314331554c15de17548b484", "path": "sharpyuv/sharpyuv_csp.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98935f1ac9c7f8d1ea87975268a8221448", "path": "sharpyuv/sharpyuv_csp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d46c012de639d13131b7c25dea418ab4", "path": "sharpyuv/sharpyuv_dsp.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1fe42e46270c96b6d08749c2f1a6315", "path": "sharpyuv/sharpyuv_dsp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e32c91b853b14b2bababcd50d61aaa69", "path": "sharpyuv/sharpyuv_gamma.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98011d6326bce6b3da65bdb0553c6411c5", "path": "sharpyuv/sharpyuv_gamma.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9873d910f9cae3361e665e0c7eef437db5", "path": "sharpyuv/sharpyuv_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98cd1d4bc2566d3bd113b550ab900780f1", "path": "sharpyuv/sharpyuv_sse2.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982efa17f00611cd6187fc4592a7d653e6", "name": "<PERSON><PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a2911606cbd135a54704c7c5a5e92680", "path": "libwebp.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff4c3db54933f59fbe28f5ed6ba857a0", "path": "libwebp-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9892d86a563bb3525390ed870ed2db19fc", "path": "libwebp-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5a374079041f8dbbdda88c3e7e67f07", "path": "libwebp-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989dc94ca28663c3b8151a4750a7c8c14d", "path": "libwebp-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98329dbe00d3810a6171a0c4b3523b2f4d", "path": "libwebp.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989664905deae191c9e897fd60729aaa40", "path": "libwebp.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ef96c2176ef7517a57360dd21a4e9690", "name": "Support Files", "path": "../Target Support Files/libwebp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f7a35f8b762185dc158b17f7c5d06875", "path": "src/dec/alpha_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a577a1a8439e547e5b4c0c0880f49359", "path": "src/enc/alpha_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988d5092b73b6537b05cad2ae545a2c71f", "path": "src/dsp/alpha_processing.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9858c22c9839adef572b05c16cd9d2ba99", "path": "src/dsp/alpha_processing_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9897050a75dd615118af24d8ee06baa372", "path": "src/dsp/alpha_processing_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9840504064762d8db05b4228583f279bc6", "path": "src/dsp/alpha_processing_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98adcf02fa7f8b294062722664ed1c4533", "path": "src/dsp/alpha_processing_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827e01fa98340152bcf9bffa8e2202882", "path": "src/dec/alphai_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983fec9d6896fd3d6db3f842951b456890", "path": "src/enc/analysis_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e764dc216fa51f10a4d660018df1c8fa", "path": "src/enc/backward_references_cost_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989afe0bc2ebd826b05ef68e9ce543a395", "path": "src/enc/backward_references_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a35796ddde31e3b21e5e2e614fafc80a", "path": "src/enc/backward_references_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5b80c7b0cf5aa5b4544744904fdf016", "path": "src/utils/bit_reader_inl_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c6136991f397ac57e779f59aa8a57dd1", "path": "src/utils/bit_reader_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a6e4c70364f2963ebf2b3f9a899e5a0", "path": "src/utils/bit_reader_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983e0bb67046af0ec4943cc1b5a7345706", "path": "src/utils/bit_writer_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e020f46e734395d371fc684d663e8f00", "path": "src/utils/bit_writer_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9866517332a28dc27954c3e673249c221e", "path": "src/dec/buffer_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98aded93cfbb3bcd03c80d4f4a3abbf2c5", "path": "src/utils/color_cache_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825607fc5427876e179a07e652bb1da48", "path": "src/utils/color_cache_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8878c110b4e9573c54fe5addf4b2b48", "path": "src/dec/common_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f48b21bd1233269f568cab5aa7a5301f", "path": "src/dsp/common_sse2.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98560998b39e4bbded25683fe394e6d31c", "path": "src/dsp/common_sse41.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f1b0b8ad177889b84349fc891f91847d", "path": "src/enc/config_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f69175fbd230b993227a1de7a9797d3d", "path": "src/dsp/cost.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ecad5500ad77752d7f499c3183046493", "path": "src/enc/cost_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4283e8b290f00c4bccb9fef5f312949", "path": "src/enc/cost_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982aa0c55e19bfa8dcbfa638c02e0edafe", "path": "src/dsp/cost_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989a7b273a06c9ba10657e1f5be4347a1a", "path": "src/dsp/cost_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980a240d51b5b94029df7e00fbfbf81473", "path": "src/dsp/cost_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985cf30d0ea932a33aa3698a9192c600cc", "path": "src/dsp/cost_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e1b18d8552b012806ffdae4c05e60354", "path": "src/dsp/cpu.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fbe1e0cad714571b5371196e53be645", "path": "src/dsp/cpu.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98706241c5b2591450ece7083ead401489", "path": "src/dsp/dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981138311a3f01c91a5e59f2e7ba7f2687", "path": "src/dsp/dec_clip_tables.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9896421633d3904a3482582ec6d1bcbd97", "path": "src/dsp/dec_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98759bf37cce949085f779d4b80d1fc9e5", "path": "src/dsp/dec_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9868e78b0f0cbb8286eeb26207c22cff62", "path": "src/dsp/dec_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a214c76d06529048deeeae9f41dd1ff2", "path": "src/dsp/dec_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987431e9d1496611d7c530b36214daff77", "path": "src/dsp/dec_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983f6d2715e1335a602735d15646a95e9c", "path": "src/dsp/dec_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98256a3f34f197ba1a4a05eb93f82c4699", "path": "src/webp/decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858023011b75895125f25eb930630a6ba", "path": "src/dsp/dsp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985a0fd9f2c073121566b22a9aac11e7d4", "path": "src/dsp/enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982aa24e83592c96ebb6798dbe9ed34162", "path": "src/dsp/enc_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d7cd80cc0636dde483d882fb16f51620", "path": "src/dsp/enc_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985991664a86e2d4696a2d9f5c5b0bbca1", "path": "src/dsp/enc_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989fe7cd17128af702a62c72ca1491bf48", "path": "src/dsp/enc_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988ac13f38e8978963cc924f4338e79e46", "path": "src/dsp/enc_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c920898950de2b4d789a957814365a62", "path": "src/dsp/enc_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fc36e5db59f8dcd9e52124195405a13", "path": "src/webp/encode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831547bcf951f0e9c079147275ea69172", "path": "src/utils/endian_inl_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986b9d59853ace6a47936b33ef59a8295a", "path": "src/enc/filter_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9856d38d7888645991bf3018c62269d9fc", "path": "src/dsp/filters.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9837377bd99d0d5bf2f7037b38caefc0f3", "path": "src/dsp/filters_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9845bead1b0440b258365cc044020f96c5", "path": "src/dsp/filters_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98392d67b14604e8864ce29183c1b365c6", "path": "src/dsp/filters_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981153a93eaada32d3aea2c4999658d650", "path": "src/dsp/filters_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989543e8eaf1a3f089897aee23d3e377e1", "path": "src/utils/filters_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982723cc785041e9f0de6a0ff88403e3f9", "path": "src/utils/filters_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee8ec575940ba3ddb36d338593cd0e26", "path": "src/webp/format_constants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988b98c2ce196d0ddffae82973734d9207", "path": "src/dec/frame_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980c5287d1c85c554e3f70cbe178e54029", "path": "src/enc/frame_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98eac21ce4e634fd07f8be8ca8f9040b1c", "path": "src/enc/histogram_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98796a2d8c7d25f1ac96401b8ad244ea3d", "path": "src/enc/histogram_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c6c3a2ece4f1d0b8629afba6e0a7f1b8", "path": "src/utils/huffman_encode_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e14c0b14691f2d25ccf28b5c2cd9be8", "path": "src/utils/huffman_encode_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bce412ee6f521f8ed83c589650196896", "path": "src/utils/huffman_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfd93d27064d60e8bde0e84c6b357e73", "path": "src/utils/huffman_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e62b8dc8fdbff330eb314c9966dd3c37", "path": "src/dec/idec_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988bd004474a4268c12a8602f54fa916ef", "path": "src/dec/io_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d6f3d0fdff84d8e90d8ff77a08e185fc", "path": "src/enc/iterator_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ab9df5c35bdec1373c23cb221e42c556", "path": "src/dsp/lossless.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98208b937128c170439e38f1d08af223ee", "path": "src/dsp/lossless.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1b144da70a83519a356218fb81d6829", "path": "src/dsp/lossless_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a72386a4a923ee30dd01034aeb8229d7", "path": "src/dsp/lossless_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9871dfa72abb9a196261b2838917661e4e", "path": "src/dsp/lossless_enc_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a1e36d9a12b77889beaac68b2b53ba3a", "path": "src/dsp/lossless_enc_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9869026b889d3e07b962e047c4b42b3d92", "path": "src/dsp/lossless_enc_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989c36c95146ad330fdb3bbb1be9fd0017", "path": "src/dsp/lossless_enc_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988331c5c317f3246a87f27e360b4ede98", "path": "src/dsp/lossless_enc_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98485df046ddb0a82b2fe3c3d8e9238135", "path": "src/dsp/lossless_enc_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986ce6ef6a626162daca530b1503f516ef", "path": "src/dsp/lossless_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985b5ac7b74b9b25d60d8c9e269b0d34e7", "path": "src/dsp/lossless_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9827c6b8966cfadb6fcaa50d66b0600b86", "path": "src/dsp/lossless_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98653fd5a17ab9505b0a0f407780bbd526", "path": "src/dsp/lossless_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bee1f2c01aab26e9be12bd283a4549cb", "path": "src/dsp/lossless_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3c2b9934b8e5be75d62cb3de955e9c2", "path": "src/dsp/mips_macro.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3529a008db9ab5f72dd7a8cf69d5ee4", "path": "src/dsp/msa_macro.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9863a5befe028e7dbc196651aefdafaacf", "path": "src/webp/mux_types.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98049c0f0fd145ead5557e7c1e5c5dae1d", "path": "src/enc/near_lossless_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d25a5e7a24fefbb3e40946c1610c9702", "path": "src/dsp/neon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e1f210bad1849641546b0d4f79a68787", "path": "src/enc/picture_csp_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d6c97192a114f09385c418eb565a67e5", "path": "src/enc/picture_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984689cf5ead429cddda5f33c5f17b6c96", "path": "src/enc/picture_psnr_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98669be346a6c51ed24884ca854f508990", "path": "src/enc/picture_rescale_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ae17cd215075e35168d139f5fc7ee567", "path": "src/enc/picture_tools_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986aa8c97973828cd89b46b1b438b21270", "path": "src/enc/predictor_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839bebdfc0deb74e8a8747dc7965795ed", "path": "src/dsp/quant.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985e56d882f13a299d8f57a44f41f7cc1b", "path": "src/dec/quant_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ab3409d13e82f9be23b2e394b1024ca1", "path": "src/enc/quant_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984246fdef99b0b08eef22091c75d896a8", "path": "src/utils/quant_levels_dec_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b4659ddaca72a441c6acb0e30974f13", "path": "src/utils/quant_levels_dec_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982672885ea37e0c5d3f20c1db6dd83092", "path": "src/utils/quant_levels_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f611456ef7c1465191581cd258345e0", "path": "src/utils/quant_levels_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982f6adbb7bed9367d116e39850eeefc3d", "path": "src/utils/random_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807f635f4c0f38b21b02f58bbdb5401fa", "path": "src/utils/random_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9821f9b8d662238dd53175f2236335a4f5", "path": "src/dsp/rescaler.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987f33d1c5e343ed40de652c6b191e3598", "path": "src/dsp/rescaler_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9846619ff0cd010eb1d9b354a3b47fe900", "path": "src/dsp/rescaler_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984cf9d0acb72fdc88996f466f7cdf4bdb", "path": "src/dsp/rescaler_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c2f387816f6672cf1d8d41059b537367", "path": "src/dsp/rescaler_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98528b969bbfb8836359a1c77470022005", "path": "src/dsp/rescaler_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985ef67a3856674cf5d84fb375d27f8075", "path": "src/utils/rescaler_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98180b99f59ddd07db8e9719698b5832fc", "path": "src/utils/rescaler_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98996a6d85223b1ac46a6272562d188e4e", "path": "src/dsp/ssim.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98100464f088f77abaef81f51cb8d86fbb", "path": "src/dsp/ssim_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98da1baa4f7ad3be472b5acd3b3734fb40", "path": "src/enc/syntax_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9869a27ca2b8872414f0360a6a3abc9ee5", "path": "src/utils/thread_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c80e12817b736b84a00d1aa9b0ba0755", "path": "src/utils/thread_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c99a402c3629585ad3c9405bb75a6228", "path": "src/enc/token_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9832454b249047d46dc8324114d002c11e", "path": "src/dec/tree_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ce62f782b85e4bcc2062f02004489436", "path": "src/enc/tree_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895a169ff4467c550f6a57324b43e8685", "path": "src/webp/types.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fc756d55c9ca91675bb3a155d7d74652", "path": "src/dsp/upsampling.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984398d058408830695ced442566c9a600", "path": "src/dsp/upsampling_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d893eb0e01b9cc3de108e0c5c8271045", "path": "src/dsp/upsampling_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985e82aa536201a289f80db6b2e392bf6b", "path": "src/dsp/upsampling_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982f1a91b211c8b9c1b076dbf85db8f5de", "path": "src/dsp/upsampling_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c72ab3fe3993d9e8a60393da4518fb8a", "path": "src/dsp/upsampling_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980eb02201d31128e06053b816e1218440", "path": "src/utils/utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98995e5a3319a61e7df9e95e30d664c834", "path": "src/utils/utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b8cdcf0df96b703aebcde12fa22eeeaa", "path": "src/dec/vp8_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1d25e8d6bfffd149af51caa9c31c3e0", "path": "src/dec/vp8_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab93962e51683c8487eaac5435d58af0", "path": "src/dec/vp8i_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4622e7b42e9cdd1b5c68a37e5af6e49", "path": "src/enc/vp8i_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986fc18d6b2cc4e264af7550365b5cd237", "path": "src/dec/vp8l_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a9a4dd0c89d1717cda51a4ca741c3f31", "path": "src/enc/vp8l_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a926c0c120b9e03b26fd36be167e73c5", "path": "src/dec/vp8li_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bebb144e7782b8d813b607c784a84cb1", "path": "src/enc/vp8li_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fc491c52da5135ae11ed779ade1b9f2f", "path": "src/dec/webp_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98403add5027b87bbf311e4498be2a2ef8", "path": "src/enc/webp_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806031a2ce58b68b72c56d0d6952d0154", "path": "src/dec/webpi_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987b1cebe2dd36b6daf547889968923446", "path": "src/dsp/yuv.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e87ee6fb8b232ed67e7f2b318b5e6914", "path": "src/dsp/yuv.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b8693bba586b71b42865617d0f0ac4fe", "path": "src/dsp/yuv_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984b597133b7c876dfcbd4e48deac5c4bc", "path": "src/dsp/yuv_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d43a12d5f59a7e53fd495b04dad0d1d7", "path": "src/dsp/yuv_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d04ac9d155ae7ec792e24772abc11ea6", "path": "src/dsp/yuv_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b04b4544f8e37badbaeebeb8f4470d78", "path": "src/dsp/yuv_sse41.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989c30af3774ab881d16a5d95930dad4bd", "name": "webp", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820217909b5a85b221be7512747c43872", "name": "libwebp", "path": "libwebp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98620631b2076aa22341713221a268695c", "path": "Mantle/include/Mantle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d22efe2f40df6cb9aad8122772616a6", "path": "Mantle/include/MTLJSONAdapter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be09802e8239f19dac1ed9d8fc0e7e0e", "path": "Mantle/MTLJSONAdapter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c5ef1b4141a6a212e8fedef833411aa", "path": "Mantle/include/MTLModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983640c42ad8de7093cb46d05794f58c28", "path": "Mantle/MTLModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c01d57cc196aa8d31897ee434d5836d", "path": "Mantle/include/MTLModel+NSCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9823acd47098ef5e3c072826f3be245634", "path": "Mantle/MTLModel+NSCoding.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd4dc84694c3b3828d22f842882a6fe3", "path": "Mantle/MTLReflection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98719808c46b0f3b674edbfafc6b4eab43", "path": "Mantle/MTLReflection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98970385880dac7a71d54aca360c2147e4", "path": "Mantle/include/MTLTransformerErrorHandling.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870a18fb235b816c08818898405ef8ace", "path": "Mantle/MTLTransformerErrorHandling.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb0409e838966fdad1a5432134f5392c", "path": "Mantle/include/MTLValueTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a86e87d056659730287ae8a4ea04a517", "path": "Mantle/MTLValueTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b0a8f8eaaaf92b3d3d96fa4380cb293", "path": "Mantle/include/NSArray+MTLManipulationAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca94d2a227a6cc3a2cd935c20fdb5c23", "path": "Mantle/NSArray+MTLManipulationAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828ea86a6907e54ba115e7e15c61e6c06", "path": "Mantle/NSDictionary+MTLJSONKeyPath.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cfc306cb66e767d6c4906073de03c80f", "path": "Mantle/NSDictionary+MTLJSONKeyPath.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988cea0ad0cc2c26a8cd7415a4a94c465d", "path": "Mantle/include/NSDictionary+MTLManipulationAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae3822f1522c96a8ba38fa666fe70149", "path": "Mantle/NSDictionary+MTLManipulationAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98018afd592f2a085140ca420d121c2f63", "path": "Mantle/include/NSDictionary+MTLMappingAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bcf3aaee749c6ba526a1e2cc15e9359a", "path": "Mantle/NSDictionary+MTLMappingAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2436c04de5afdd8dcd83561b1de3b2f", "path": "Mantle/NSError+MTLModelException.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea74cc8e3815b2d3f00326797095d6ea", "path": "Mantle/NSError+MTLModelException.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bbc7b8fd7509a40569a6d02b8637b4e", "path": "Mantle/include/NSObject+MTLComparisonAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869b44bfc9afb43e56c62e17d865d6d12", "path": "Mantle/NSObject+MTLComparisonAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa2beccf1e9e090f672c1f86019a8f27", "path": "Mantle/include/NSValueTransformer+MTLInversionAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d1fe5bb7b0c2af9217700fc817f05ff", "path": "Mantle/NSValueTransformer+MTLInversionAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6687e9d468b6213a72e0490b80d12b5", "path": "Mantle/include/NSValueTransformer+MTLPredefinedTransformerAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9803a0658f45eadc92faa7cdb27f9f041a", "path": "Mantle/NSValueTransformer+MTLPredefinedTransformerAdditions.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce9cb63003d70d6e767b456490400207", "path": "Mantle/extobjc/include/MTLEXTKeyPathCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985660747dd355070eb7f2c03236ab0a81", "path": "Mantle/extobjc/include/MTLEXTRuntimeExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e644249496aae8959ddf2caeafbb9d5", "path": "Mantle/extobjc/MTLEXTRuntimeExtensions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b762d350b195faf4a4b1ff6a602553de", "path": "Mantle/extobjc/include/MTLEXTScope.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f51f1a3ba7f7b9e7f10076c4304b17d0", "path": "Mantle/extobjc/MTLEXTScope.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0e98e70007a139d78ecd8195b753c8f", "path": "Mantle/extobjc/include/MTLMetamacros.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9882588d4aa0372a6dea4b2634978421cc", "name": "extobjc", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e1b9ddb328074a97a6f59d397233aa19", "path": "Mantle.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98738dead30c1cb1d3763ea6f9cf9705f1", "path": "Mantle-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98914416d2ab2fa725f3650d6da3d3152f", "path": "Mantle-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4a3f136d6d161486d9af9ef87c324cd", "path": "Mantle-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9861df3665ddcfa70e7cd0a70f6f3ae1e3", "path": "Mantle-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d5b25c047bbeef8ae591477985aeb883", "path": "Mantle.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c4f0da9cb2223bbf6cd76f8a357308f6", "path": "Mantle.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f4229dbc9216c044cf3ca345148cf4", "name": "Support Files", "path": "../Target Support Files/Mantle", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e953e435fa9f9ca1d6f2587eda4783bb", "name": "Mantle", "path": "Mantle", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980ffd21e9e0a3a831da3cca10b2de84ec", "path": "Sources/OrderedSet.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b7077320012bcd32f9ffa3a70f1e45eb", "path": "Framework/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f9edc9146823c223d131fa11f40d5d4", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ac35da7d6f328a6360b6181a5b212a93", "path": "OrderedSet.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826bcf5df1a72625bf524c62768d2530f", "path": "OrderedSet-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988d0b819aaa7c4e7779ed9ad6135cf105", "path": "OrderedSet-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1950020e5932856926e36995e814a3f", "path": "OrderedSet-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d04c4683273f5964017bd271bd9fe7aa", "path": "OrderedSet-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98410360fc6dca1eb97a97b87526131899", "path": "OrderedSet.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9817307907e46508e6d61184f04a9f24f7", "path": "OrderedSet.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9884aba25ccc073c936c3134cdc6303a72", "path": "ResourceBundle-OrderedSet_privacy-OrderedSet-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98da655650fa49c4c91b5057cd2d3dd33b", "name": "Support Files", "path": "../Target Support Files/OrderedSet", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981953a145686851c7eefc166938ec5830", "name": "OrderedSet", "path": "OrderedSet", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2ac85c3d41b84bebbbeb4c41924ef73", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827dc545df6814d2feef3f968db832bf8", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987faf8ebbf07cb6c029eeb23d1f35c019", "path": "SDWebImage/Core/NSButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e991eb9a2c808d2c9469d68cdccf49c4", "path": "SDWebImage/Core/NSButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98623a56f1307e371bd86bdb945f0d101c", "path": "SDWebImage/Core/NSData+ImageContentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9830eea60e771df1a05869a3de7d685554", "path": "SDWebImage/Core/NSData+ImageContentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841e7751f180f08cb5a1336b1ef8f6d60", "path": "SDWebImage/Core/NSImage+Compatibility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9897e7af2739d8fc744186225527cd59e7", "path": "SDWebImage/Core/NSImage+Compatibility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986da76a64cddd2ee49cefca970a667596", "path": "SDWebImage/Core/SDAnimatedImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836eaca02cd25e6fa7ad7484a36df8818", "path": "SDWebImage/Core/SDAnimatedImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d15fc14fe31e8b65c95b124f9b34f71", "path": "SDWebImage/Core/SDAnimatedImagePlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9823932c318f9633e804619f846a77158a", "path": "SDWebImage/Core/SDAnimatedImagePlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf19d701a25d11f1e34dfe239bfd5937", "path": "SDWebImage/Core/SDAnimatedImageRep.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98385130e1e3ef2bb0de65f720f07f479e", "path": "SDWebImage/Core/SDAnimatedImageRep.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811ef26ad5c79f15930f2377b1591cf44", "path": "SDWebImage/Core/SDAnimatedImageView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9851609c88f8a3e4e69559ee65d4ce4ed0", "path": "SDWebImage/Core/SDAnimatedImageView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b1ca3693bb65374fb060ef3fd9b562d", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b425c183c49da494307e001eaf5412cb", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f786a4bbb0f11aba883b22d49158f823", "path": "SDWebImage/Private/SDAssociatedObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984983dfa600da86e466872324e75d6d68", "path": "SDWebImage/Private/SDAssociatedObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981612c0ef8a9958997e5135d164ff5f39", "path": "SDWebImage/Private/SDAsyncBlockOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d88c094f36bc0d58705463a45a02ef54", "path": "SDWebImage/Private/SDAsyncBlockOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c69cbd842bb488095542251899dff38", "path": "SDWebImage/Core/SDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f1b362e3630f5163516994290ddc298a", "path": "SDWebImage/Core/SDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985292707a240cea0ab8c7b96a124e1af3", "path": "SDWebImage/Private/SDDeviceHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988557afd8dffd0eaa0cbb66a87c577613", "path": "SDWebImage/Private/SDDeviceHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b398147e2ce52ec71862851a330f40de", "path": "SDWebImage/Core/SDDiskCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f663d17b4a7c02ee9c54b6d58308a33", "path": "SDWebImage/Core/SDDiskCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897d13dcd62d7e57e2a39a6e2537380e3", "path": "SDWebImage/Private/SDDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9897331ded6b7f5f6dd77e13501f0375de", "path": "SDWebImage/Private/SDDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98404e90599efcbc69e53a37d6c39a3c29", "path": "SDWebImage/Private/SDFileAttributeHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0cbbcfcc380a388df39e6f08e240631", "path": "SDWebImage/Private/SDFileAttributeHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802726d22fca74d3c5ab0c0884ce77114", "path": "SDWebImage/Core/SDGraphicsImageRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a37ecff5359a62728f7c93f7a2d7ddca", "path": "SDWebImage/Core/SDGraphicsImageRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f3483f4f4ba92b763141f012c1c7c7b", "path": "SDWebImage/Core/SDImageAPNGCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820bc7aa082c51ff15a261818a6e68f71", "path": "SDWebImage/Core/SDImageAPNGCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d71a8069de00e5ed6027a2684ea431de", "path": "SDWebImage/Private/SDImageAssetManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9859a12ebf63c45f293b09f28a44368eee", "path": "SDWebImage/Private/SDImageAssetManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c63d68f9a248fb6a60b1d3d89ceb0542", "path": "SDWebImage/Core/SDImageAWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c2f6ec633dd4017f2d7456f23fc6f71", "path": "SDWebImage/Core/SDImageAWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6dba089f460cd0d72b1e688ef6dddd0", "path": "SDWebImage/Core/SDImageCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98319a26a02ed71dab33dd17da8d7e1441", "path": "SDWebImage/Core/SDImageCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4fb311206409cfdca1c63b86da2af47", "path": "SDWebImage/Core/SDImageCacheConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899ef9ddd8a038896900755ab26a0d5c9", "path": "SDWebImage/Core/SDImageCacheConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d07ab0cfa60b65aba5bb30f629db4a3", "path": "SDWebImage/Core/SDImageCacheDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a860b510e988283a6698fd24a7b48dbc", "path": "SDWebImage/Core/SDImageCacheDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b2bb110fbc10bf3ab920c5e0cf6c121", "path": "SDWebImage/Core/SDImageCachesManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98289e566338bb20dd02a734167557e65a", "path": "SDWebImage/Core/SDImageCachesManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0535644dccaa0d1670543d55c04c0e8", "path": "SDWebImage/Private/SDImageCachesManagerOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c573915368a693d1a65b123d8cb890e1", "path": "SDWebImage/Private/SDImageCachesManagerOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d0c075bcaa7f5de92ce42377bc223b7", "path": "SDWebImage/Core/SDImageCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a126a26cafa9c286206aa7bf96cf5dd", "path": "SDWebImage/Core/SDImageCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9816ed909ea0c64752d0eee394b746a908", "path": "SDWebImage/Core/SDImageCoderHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98132b52f17bd79ca0f123ca14f9f439c6", "path": "SDWebImage/Core/SDImageCoderHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853adbbabfb731af650970c0ccd65be4d", "path": "SDWebImage/Core/SDImageCodersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bfa6bc10b98b8443e88585029030f6c6", "path": "SDWebImage/Core/SDImageCodersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872e9542a68f758803c70c24d35ddf1a0", "path": "SDWebImage/Core/SDImageFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d7e74b71f64418bd20a2882706df3862", "path": "SDWebImage/Core/SDImageFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98682e49100c933e9b026d40a306cbbc66", "path": "SDWebImage/Private/SDImageFramePool.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98055bb3ba30e1f2813a299329315449b9", "path": "SDWebImage/Private/SDImageFramePool.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829226dfc31008189d97e6cbe97574396", "path": "SDWebImage/Core/SDImageGIFCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e601a07744cdad22fb6ad6972f9526c6", "path": "SDWebImage/Core/SDImageGIFCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872011f736872d0d96a5aac4ce54112ff", "path": "SDWebImage/Core/SDImageGraphics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866ccdbd1f8622a208017402a4ff906e3", "path": "SDWebImage/Core/SDImageGraphics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821bc1e591f27a66ea5222481f9a1f6eb", "path": "SDWebImage/Core/SDImageHEICCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9849c24271ef658d5171cd522b758a4a50", "path": "SDWebImage/Core/SDImageHEICCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6c51a1b6d459288717d96da262b1145", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c52ce527e31ec7a2ca8a33af67ddd40d", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e07cc49fc5c0e417f317624d716e707", "path": "SDWebImage/Private/SDImageIOAnimatedCoderInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807198eb8e65ac424d05c3a12884c09f8", "path": "SDWebImage/Core/SDImageIOCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876f2e9644b074678deeafb8469290ed7", "path": "SDWebImage/Core/SDImageIOCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f9f0294f84bbdf77782ce1358a371be", "path": "SDWebImage/Core/SDImageLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cea1cb8c70795366f164a54621619789", "path": "SDWebImage/Core/SDImageLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98359489954a2ec3edb2bd71dc0f95372b", "path": "SDWebImage/Core/SDImageLoadersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d5947d376694ec4907dc7948a4db245", "path": "SDWebImage/Core/SDImageLoadersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98630c5aa3323a2b78209a4c216f07779b", "path": "SDWebImage/Core/SDImageTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985bb4c75dcb8240b4db5646bb63d76954", "path": "SDWebImage/Core/SDImageTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf31e0a894389b904f392da631df30f8", "path": "SDWebImage/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c2465b9c56b835ffcf3a9db95f7b643", "path": "SDWebImage/Private/SDInternalMacros.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98079c0ccf8d622837f639f7347f2f18e0", "path": "SDWebImage/Core/SDMemoryCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800799e7a0e6a08b41e2bae9417c0e57b", "path": "SDWebImage/Core/SDMemoryCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e2889328ecbf53cb379385592f363ad", "path": "SDWebImage/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98615a2022084e691949170ab9066a4a7b", "path": "SDWebImage/Private/SDWeakProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a38babe5af5cb4d8021a2231aeabdc3", "path": "SDWebImage/Private/SDWeakProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985950eaccd718c40c0cbc4dcec6df2bea", "path": "WebImage/SDWebImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef798f89fa6430f7edc4498e4cc6851c", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea80b1c64da4ba41549948df9c833573", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a27370d54069d5e911ccb5276b4d38d", "path": "SDWebImage/Core/SDWebImageCacheSerializer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863c1ad10020bc6d069addf532bf021ce", "path": "SDWebImage/Core/SDWebImageCacheSerializer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cd5f5e1c7b1f73ff2f705d02592ee69", "path": "SDWebImage/Core/SDWebImageCompat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f69a03eef5c15c5e23f77487799bd199", "path": "SDWebImage/Core/SDWebImageCompat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9cd1fdebe2b5e0599f224754ef5a4cc", "path": "SDWebImage/Core/SDWebImageDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a84d9d4e430f2f6657e0cab1350f066c", "path": "SDWebImage/Core/SDWebImageDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f21c723106baa88f5fa5d423310d34c0", "path": "SDWebImage/Core/SDWebImageDownloader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7c328aa6009b295488a3de5dfea621b", "path": "SDWebImage/Core/SDWebImageDownloader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98340b51f9ade0d9cc661571151fe2e92b", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98581a6ada4dd3c9dfe784663487204b14", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98adea350c1e9ac4213ce4dd9253ba8e83", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f1afa191efb22e162d8040e302402fe7", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d6da1b9a94151babcc41a918656b5ea", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f566f49617efb08358460666ca8a3e05", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98467a872798715a0ce1a269f47ba43dbf", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b20bcbfdb231f88fda69734562dcc2d", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986fa423d38775d7765bf7da7b788d53c2", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98815472decfc6066ea4d973cf19f53cf2", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f30f1d722844d87387b02812b5b6863", "path": "SDWebImage/Core/SDWebImageError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a71d184e6bc5a6c0aac0e3fc26246271", "path": "SDWebImage/Core/SDWebImageError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e54dc5c5787387681ba7e6b9192efa4", "path": "SDWebImage/Core/SDWebImageIndicator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807023540305b4785b8397728539fe11a", "path": "SDWebImage/Core/SDWebImageIndicator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a700ef9121358e944fdc4213f21b07e", "path": "SDWebImage/Core/SDWebImageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988072ed61a2bc924e5b3f2980e568c18f", "path": "SDWebImage/Core/SDWebImageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a578d16e1cc140ac398518912c4dd4b", "path": "SDWebImage/Core/SDWebImageOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984be0746b1609ab644b016a2103e8443b", "path": "SDWebImage/Core/SDWebImageOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa9c1e4aea794d46cff4f84a1183ee8f", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98739292bf7f25198c3dc0bc39d1dc3ea8", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888c31e5a15c36fef106b04a8980c2f4e", "path": "SDWebImage/Core/SDWebImagePrefetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc2c2feb372e910d953909f0a3bd406c", "path": "SDWebImage/Core/SDWebImagePrefetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859568722677a5a930c25966783072864", "path": "SDWebImage/Core/SDWebImageTransition.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984bb92af0c90e3ff36759b38edba7d3f0", "path": "SDWebImage/Core/SDWebImageTransition.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f37c653641eb19c6cad01f1a4fa37bc", "path": "SDWebImage/Private/SDWebImageTransitionInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae6c83dfc0286954be6a891e3c740817", "path": "SDWebImage/Core/UIButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8f8a93a8951747941dd9b50b30b908d", "path": "SDWebImage/Core/UIButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1a6a9fb3cb2220ee4284565eef479be", "path": "SDWebImage/Private/UIColor+SDHexString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d61e39d102edae37411d411acf0ff955", "path": "SDWebImage/Private/UIColor+SDHexString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98934c7edf1a5c0e1b33caa447ca3dbbd2", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9841452a01ebb770ccd81fbc571672a0c6", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98201fa8a62e0a2ebe27ccfa99bb2f8f24", "path": "SDWebImage/Core/UIImage+ForceDecode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e979f190649705cc64c674ad81c49146", "path": "SDWebImage/Core/UIImage+ForceDecode.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c52733d3e60e33780839bc70d70e78d", "path": "SDWebImage/Core/UIImage+GIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986726e9d1d782b328ff99075a611675ca", "path": "SDWebImage/Core/UIImage+GIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98603937c33a2b3f5fea52594b0c7e4871", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986bf4e71dade60ffe353867ac1645c1ae", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a1d19b9086bd6af533f41f9620e8aad", "path": "SDWebImage/Core/UIImage+Metadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855c591d661fe222ead7406b3f57b2135", "path": "SDWebImage/Core/UIImage+Metadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980303682653924d03fc80393d405eb08a", "path": "SDWebImage/Core/UIImage+MultiFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981c1f1001f7a3f7c320ffb5fe07df0e53", "path": "SDWebImage/Core/UIImage+MultiFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f9bbce958233e805a8931ad1a57b250", "path": "SDWebImage/Core/UIImage+Transform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa21ee25972c715a84e8e9b9d2c0ada4", "path": "SDWebImage/Core/UIImage+Transform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98751b539de8b6a7f90b5636dfe3990989", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8a3f2c780464b77526202624aa49db4", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f749dc6a19213a51b92ee8977e198870", "path": "SDWebImage/Core/UIImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815d682f8bbe770ad83818b31fed9eee8", "path": "SDWebImage/Core/UIImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c71efaba7a2a984b1b2fecfa211d701", "path": "SDWebImage/Core/UIView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c4a696fdf5e0412b57187085f8cf031e", "path": "SDWebImage/Core/UIView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bdb83acd32fad82a7f709c85c8482b8", "path": "SDWebImage/Core/UIView+WebCacheOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a235bbc840537e01d12e7f6fe08310b", "path": "SDWebImage/Core/UIView+WebCacheOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9c6b0640161f660d7cf7cf78e3f4827", "path": "SDWebImage/Core/UIView+WebCacheState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98335a2c3888624558db06fb23c50f89a4", "path": "SDWebImage/Core/UIView+WebCacheState.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9869304d76b8e591873d177b1da16210f8", "path": "WebImage/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98be2476323774295c760528b21a08be3d", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cb207ac5585659d3f68db13c4b74244", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989198555b1a65830233b4ef2b3bfebfe3", "path": "ResourceBundle-SDWebImage-SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b3285d394159b04d6e18b6d2315e7f25", "path": "SDWebImage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986cf0c99ad30306f66f8535ab617717cc", "path": "SDWebImage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dfacc164c20a6f46acc4d73a94d0eaef", "path": "SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e087eb9f6ad6f919c8cd205b34b0782", "path": "SDWebImage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d2df8b2cc5f1fa59f11e28e565f490aa", "path": "SDWebImage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9845dbdf12dd1f4fe5144b28f8e14d23a2", "path": "SDWebImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980477061ce583dd06fafba0865e521e70", "path": "SDWebImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985bc9af9e2594c8de7251ac3e595e01a4", "name": "Support Files", "path": "../Target Support Files/SDWebImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dfb988b8545a84612133dd26c0a1ffc", "name": "SDWebImage", "path": "SDWebImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c4c62ee8b4685a50b0d6c79cc38cc45", "path": "SDWebImageWebPCoder/Classes/SDImageWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980187bc9a5cd612d517d5d0a895cf1da6", "path": "SDWebImageWebPCoder/Classes/SDImageWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986bc8b66056595639b2f39e05351d2321", "path": "SDWebImageWebPCoder/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985195af36cf4ddaedebf1d2dba4e812a4", "path": "SDWebImageWebPCoder/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98341de523a791ccc6384a3c64f3bad01b", "path": "SDWebImageWebPCoder/Module/SDWebImageWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e229c494c753597f4596d812b7ad7525", "path": "SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a09659043e414939de9e19339b826da2", "path": "SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981175711e6b783d3570f718451342dbbd", "path": "SDWebImageWebPCoder/Classes/UIImage+WebP.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984441fe43405911f046045cad9ae77728", "path": "SDWebImageWebPCoder/Classes/UIImage+WebP.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9815c8db4a1b22811363d97f185c8d58ec", "path": "SDWebImageWebPCoder.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981efc4ed53756fca8a42515cd950ddc18", "path": "SDWebImageWebPCoder-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a4e5995b0bfc323a6719b3fa7e022384", "path": "SDWebImageWebPCoder-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893b3b58f0365e465af347a93f7bb7d23", "path": "SDWebImageWebPCoder-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ec3b0ffc0c27494810f5c66710008126", "path": "SDWebImageWebPCoder.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985dfef20795f3d2c0b903ca85e717b8aa", "path": "SDWebImageWebPCoder.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988cfc1421cf234e9b4a0b065a43b40b17", "name": "Support Files", "path": "../Target Support Files/SDWebImageWebPCoder", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d64186025ed911e525a5806d692c60d6", "name": "SDWebImageWebPCoder", "path": "SDWebImageWebPCoder", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832b2b1a1fa94de2a88d65de6f5677ed4", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e988ee808217f805cdeab985b49a7cf83a8", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983e3485d5de14c283a1380298e18a4526", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98810f0c8e3d9cb80091b9909448cd6626", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fb913630fcec08bc27ac2263f59bdea4", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857bc931e356d7b7843fda47492893ed5", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98d15413e70e37fef51b4d07973f5f0bba", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987d99e57ff5a2fbc3c868ddd022b1a2a0", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a9b3ef9ba473b1f1f86ce82470c7d25", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986e1523d54ce0c59eb376d15073c00a62", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fa6d3f4a0bb22c1d653f7652a809829a", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ac3979f5c1325345624fa4eb4525c7db", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f0ddcbd73ecfe1dd17d3452c7fbe2d4", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d38343279834dca6061bda437330c834", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/V Equb/mobile_app/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/V Equb/mobile_app/ios/Pods", "targets": ["TARGET@v11_hash=effedc72e2480cc8355c6d234f7f61f2", "TARGET@v11_hash=85e370e086f738f8fc3c527dc493afe6", "TARGET@v11_hash=71d8e7ce10966a8f8f582b8cf0a872d1", "TARGET@v11_hash=c3afcbb930f9f079632ab2ea042c3f55", "TARGET@v11_hash=88728bb3978c52d58e3abb90b727753d", "TARGET@v11_hash=574fb00dfe43e53f8cdf3747a3da9a9b", "TARGET@v11_hash=8cdeeb856f6b440198cf571636f7c6fd", "TARGET@v11_hash=f44b482c80a55474b0b969f63f447eb9", "TARGET@v11_hash=d122ad04de57fb73cefc198c3832d14b", "TARGET@v11_hash=41ad366023ec27d41047aed0e92d6189", "TARGET@v11_hash=be2c556d4294dc783c45f4c84b64745b", "TARGET@v11_hash=58ac494ed5937e07a09bdfd3d5660358", "TARGET@v11_hash=2c836fcd82a4f3020f8157db53399892", "TARGET@v11_hash=ac0ef21ef39d790f242f727141ecb76f", "TARGET@v11_hash=d829c32ac45e3d8f08c94f2a75505ec8", "TARGET@v11_hash=145958e36c519067478c50ef3df4c254", "TARGET@v11_hash=aabf7bb9edf533e5bafe6543c7836e74", "TARGET@v11_hash=81e5190a5b07714395aa1f829b6c2635", "TARGET@v11_hash=a33b1eb3359b69896e4ca1da8a095624", "TARGET@v11_hash=50dff3195423d9d1539d0c1f51f88afe", "TARGET@v11_hash=f0e397fd31e629c210e46e9e061ca65e", "TARGET@v11_hash=84416ea081e26fc602e86cdbbfa046d0", "TARGET@v11_hash=ec35a351653a356fa5899f713f9ec1b3", "TARGET@v11_hash=a20b48946141cf8bd8e58e74b4da34f5", "TARGET@v11_hash=5ddb4d70e0e98ba2d87cfb6a6fb839d4", "TARGET@v11_hash=643b732c6c9a021ae8a02629bbb9b5e8", "TARGET@v11_hash=5dc5280b0958caf3fabd9ce425fb54d7", "TARGET@v11_hash=4c7aa76454db6c4e56c0f1d2627627f9", "TARGET@v11_hash=2fbcb812b4aeb4fdcb52370098183dfb", "TARGET@v11_hash=b944c43617de15d83c5c17a4bd1ccd71", "TARGET@v11_hash=e2625452144de969732c63aa47946668", "TARGET@v11_hash=e0e61d18516391f99aafad30349d9e43", "TARGET@v11_hash=f6363dbca6fd444e9229acb015d0164f", "TARGET@v11_hash=76bdad09f19ba74be0ed3b44011ce953", "TARGET@v11_hash=26816bbe159273fdcf4b8970197d5b05", "TARGET@v11_hash=e413019b961ea6b3373309462391e638", "TARGET@v11_hash=f9c1fc08d20cdc2b82c550faddb5b942", "TARGET@v11_hash=6f37aab8c91ca8ae874cd856bb58b39c", "TARGET@v11_hash=2a33e3597b4d52843beb3fdf1c8ce31b", "TARGET@v11_hash=f6258fb64c084476127fbd08b80748b3", "TARGET@v11_hash=ffcde4724d1e69a221354848a7daf2d0", "TARGET@v11_hash=7ecb73be4dd4ed669b87b703cea309a7"]}