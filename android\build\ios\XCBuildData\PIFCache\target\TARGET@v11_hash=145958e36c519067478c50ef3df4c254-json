{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ca4eb76d91bbd461663f4da29223096b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "IOSSecuritySuite", "PRODUCT_NAME": "IOSSecuritySuite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9830c9f192bf03bc2078d2088104ee84a7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855a7764517936fdcbd92e4ea7c63f2e1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite.modulemap", "PRODUCT_MODULE_NAME": "IOSSecuritySuite", "PRODUCT_NAME": "IOSSecuritySuite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a36aaeb29417a95893ef74ababf77964", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855a7764517936fdcbd92e4ea7c63f2e1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite.modulemap", "PRODUCT_MODULE_NAME": "IOSSecuritySuite", "PRODUCT_NAME": "IOSSecuritySuite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e63e60941cc93c46eea1f9844400141c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e4bb6f440cf9ec7b38d8ee547fab1cd4", "guid": "bfdfe7dc352907fc980b868725387e98dff93c70d8a9d573fcfed788270320b4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984a05081f82e3a04bca2a79182002ece4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98264e9ab5a01359057a9f76b7adde5dd2", "guid": "bfdfe7dc352907fc980b868725387e9887f46dc0e22e0747db7c20cbbe1c1843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985054443d551553b873a364178e017c0e", "guid": "bfdfe7dc352907fc980b868725387e98f5c044373dc97b8c0f261293a021622b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98572e86abf0993a8a5c72519e5c840ecd", "guid": "bfdfe7dc352907fc980b868725387e980056bfd5c531fb36e17962cf92921322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b710407b25f9945a69b81858cc8627b", "guid": "bfdfe7dc352907fc980b868725387e989f0408a4e1a6c583e978e8751ba44d40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7e6003d005b269b0d4a7ffd51e1eb1d", "guid": "bfdfe7dc352907fc980b868725387e98a2700dc66bc1bd7f3b076da8d1755759"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894fef05cedca7c1ed59eccd5af6af482", "guid": "bfdfe7dc352907fc980b868725387e98a3e2ae9f7779056e3e0b394328cdf66e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bc99543a986a6ddc6016153851bc477", "guid": "bfdfe7dc352907fc980b868725387e9879642f4b565b818ef03f7c302759427e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893063ec75a4f99737667e0663aec5caa", "guid": "bfdfe7dc352907fc980b868725387e98fa6b5222b8de1675c6269df2b99a70ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b87a963af6b391d7f251a7a153ead41", "guid": "bfdfe7dc352907fc980b868725387e98f48e13d7a08dcd88d1de3298cc46ca9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883339637d025693ad447224baef432a1", "guid": "bfdfe7dc352907fc980b868725387e985a0f2410b92797677caa3ad4e161abb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989043dc39becb158d9575f54590e54ea6", "guid": "bfdfe7dc352907fc980b868725387e989bf17976e28c6b70dd6fc30a870a790c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a1cee80ff0b9ae23105d4db705f906a", "guid": "bfdfe7dc352907fc980b868725387e98a443a5fcca53acab6afbaceac851cf71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91f529f8ed0190b0c9c1a1ebdb64c09", "guid": "bfdfe7dc352907fc980b868725387e9833428c9340eda4b24aefff7b8b872e7f"}], "guid": "bfdfe7dc352907fc980b868725387e988ae20ca69d809287aff3de51a604ec8a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a15b41da5b9cf00eaf9e8a12fb6fc89a", "guid": "bfdfe7dc352907fc980b868725387e98403e41810b5dc526e2507579bd0cd206"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823116a8821514e966e1aa449d1060c27", "guid": "bfdfe7dc352907fc980b868725387e98fd36e36d0f73fe6977de842d59047583"}], "guid": "bfdfe7dc352907fc980b868725387e98dfd89517258abeb676f900f558e48a71", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98630d690f731d4718219e5c65cd729aa2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e980c47d9cc97d9fb26be834bb86d7b9565", "name": "IOSSecuritySuite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e7249f0aa877cdaf149c69fba7e0f19c", "name": "IOSSecuritySuite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}