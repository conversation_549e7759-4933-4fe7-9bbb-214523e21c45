// lib/firebase/my_firebase_messaging_service.dart

import 'package:firebase_messaging/firebase_messaging.dart';

class MyFirebaseMessagingService {
  @override
  Future<void> onMessageReceived(RemoteMessage message) async {
    // Handle incoming messages when the app is in the foreground
    if (message.notification != null) {
      print("Message Title: ${message.notification?.title}");
      print("Message Body: ${message.notification?.body}");
    }
  }

  @override
  Future<void> onNewToken(String token) async {
    // Handle new FCM token generation
    // print("New FCM token: $token");
  }
}
