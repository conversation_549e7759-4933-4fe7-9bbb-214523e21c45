import 'dart:convert'; // For jsonEncode, base64Encode, utf8.encode
import 'dart:math'; // For min function
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:js' as js;

// App imports
import 'package:ekub/models/members.dart';
import 'package:ekub/screens/payment/payment_success.dart';
import '../service/headers.dart';
import 'constants.dart';
import 'js_interop.dart';
import 'session.dart';

/// JSPaymentExecutor handles the payment process by:
/// 1. Making a backend request to initialize the payment
/// 2. Processing the response and extracting necessary data (rawRequest)
/// 3. Executing JavaScript code with the response data on web platforms
/// 4. Simulating the process on non-web platforms
/// 5. Handling errors and showing appropriate dialogs
class JSPaymentExecutor {
  // Static flag to track if we're already handling a 405 error
  static bool isHandling405Error = false;

  // Global key for navigation
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  // Store the current member for use in JavaScript callbacks
  static Member? _currentMember;

  /// Executes the payment process
  ///
  /// This method handles the entire payment flow:
  /// - Shows a loading dialog
  /// - Makes a backend request to initialize the payment
  /// - Executes JavaScript with the response data on web platforms
  /// - Simulates the process on non-web platforms
  /// - Handles errors and shows appropriate dialogs
  /// - Navigates to success page on completion
  ///
  /// @param context The BuildContext for showing dialogs and navigation
  /// @param title The payment title
  /// @param amount The payment amount
  /// @param member The member making the payment
  /// @param equbId The ID of the equb
  static Future<void> executePayment({
    required BuildContext context,
    required String title,
    required String amount,
    required Member member,
    required String equbId,
  }) async {
    // Store initial mounted state for debugging
    debugPrint("⚠️ INITIAL CONTEXT MOUNTED: ${context.mounted}");

    if (kIsWeb) {
      setupJavaScriptCallback();
    }

    // Show loading dialog in a microtask to avoid build cycle issues
    // This ensures we're not in the middle of a build when showing the dialog
    await Future.microtask(() {
      if (context.mounted) {
        _showLoadingDialog(context);
        debugPrint("⚠️ LOADING DIALOG SHOWN");
      } else {
        debugPrint("⚠️ CONTEXT NOT MOUNTED, CANNOT SHOW LOADING DIALOG");
      }
    });

    try {
      // Log payment details for debugging
      _logPaymentDetails(title, amount, member.id);

      // Initialize payment object with default values
      // This will be updated with actual values from the backend response
      final paymentObject = _createInitialPaymentObject(title, amount);

      // Store the current member for use in JavaScript callbacks
      _currentMember = member;

      // Register JavaScript callback handlers if on web platform
      if (kIsWeb) {
        try {
          // These handlers will be called by the JavaScript code
          js.context.callMethod('eval', [
            """
            if (typeof window.navigateToSuccess === 'undefined') {
              window.navigateToSuccess = function() {
                console.log('navigateToSuccess called');
                // This will be intercepted by Dart
              };
            }

            if (typeof window.showErrorDialog === 'undefined') {
              window.showErrorDialog = function(message) {
                console.log('showErrorDialog called with: ' + message);
                // This will be intercepted by Dart
              };
            }
          """
          ]);

          // Register the Dart callbacks
          js.context['navigateToSuccess'] = () {
            debugPrint("⚠️ JS CALLED navigateToSuccess");
            navigateToSuccessPage(member);
          };

          js.context['showErrorDialog'] = (dynamic message) {
            String errorMessage =
                message is String ? message : message.toString();
            debugPrint("⚠️ JS CALLED showErrorDialog: $errorMessage");
            showJavaScriptErrorDialog(errorMessage);
          };

          debugPrint("⚠️ Successfully registered JavaScript callbacks");
        } catch (e) {
          debugPrint("⚠️ Error registering JavaScript callbacks: $e");
        }
      }

      // Variable to store raw request data from backend response
      String rawRequest = "";

      try {
        // Log backend request details
        _logBackendRequestDetails(member.id, equbId, amount);

        // Make actual backend request
        debugPrint("⚠️ MAKING ACTUAL BACKEND REQUEST");
        final response = await _makeBackendRequest(equbId, amount, member.id);

        // Log the actual response for debugging
        debugPrint("⚠️ RECEIVED ACTUAL BACKEND RESPONSE");
        debugPrint("⚠️ STATUS CODE: ${response.statusCode}");
        debugPrint(
            "⚠️ RESPONSE BODY: ${response.body.substring(0, min(100, response.body.length))}${response.body.length > 100 ? "..." : ""}");

        // Log the actual response for debugging
        Session().logSession("payment_actual_response", response.body);

        debugPrint("⚠️ RESPONSE STATUS CODE: ${response.statusCode}");
        debugPrint("⚠️ CONTEXT AFTER RESPONSE: ${context.mounted}");
        // Handle HTTP 405 error (Method Not Allowed)
        if (response.statusCode == 405) {
          // Log the error for debugging
          Session().logError("payment_http_error",
              "Status Code: ${response.statusCode}, Body: ${response.body}");

          debugPrint(
              "⚠️ HANDLING 405 ERROR - Context mounted: ${context.mounted}");

          // Check if we're already handling a 405 error
          // This prevents multiple error dialogs from being shown
          if (!isHandling405Error) {
            isHandling405Error = true;

            // Use a delayed execution to ensure we're not in the middle of a build cycle
            Future.microtask(() {
              try {
                if (context.mounted) {
                  // Try to close the loading dialog if it's showing
                  try {
                    if (Navigator.canPop(context)) {
                      Navigator.pop(context);
                      debugPrint("⚠️ Successfully closed loading dialog");
                    }
                  } catch (e) {
                    debugPrint("⚠️ Error closing dialog: $e");
                  }

                  // Show a generic error message for 405
                  _showErrorDialog(context, "Error",
                      "Something went wrong. Please try again later.");
                  debugPrint("⚠️ Showed 405 error dialog");
                } else {
                  debugPrint(
                      "⚠️ Context is no longer mounted, using global navigator key");

                  // Use the global navigator key to close the loading dialog first
                  if (navigatorKey.currentContext != null) {
                    // Try to close any open dialogs first
                    try {
                      Navigator.of(navigatorKey.currentContext!,
                              rootNavigator: true)
                          .pop();
                      debugPrint(
                          "⚠️ Closed loading dialog using global navigator key");
                    } catch (e) {
                      debugPrint("⚠️ Error closing dialog with global key: $e");
                    }

                    // Wait a moment before showing the error dialog
                    Future.delayed(const Duration(milliseconds: 300), () {
                      if (navigatorKey.currentContext != null) {
                        _showErrorDialog(navigatorKey.currentContext!, "Error",
                            "Something went wrong. Please try again later.");
                        debugPrint(
                            "⚠️ Showed 405 error dialog using global navigator key");
                      } else {
                        debugPrint(
                            "⚠️ Global navigator key context is null after delay");
                      }
                    });
                  } else {
                    debugPrint("⚠️ Global navigator key context is null");
                  }
                }
              } finally {
                // Reset the flag when we're done
                isHandling405Error = false;
              }
            });
          }

          return; // Exit the method
        }

        // Parse response data
        Map<String, dynamic> responseData;
        try {
          debugPrint(
              "⚠️ ATTEMPTING TO PARSE RESPONSE BODY: ${response.body.substring(0, min(100, response.body.length))}${response.body.length > 100 ? "..." : ""}");
          responseData = jsonDecode(response.body) as Map<String, dynamic>;
          debugPrint("⚠️ SUCCESSFULLY PARSED RESPONSE BODY AS JSON");
        } catch (e) {
          debugPrint("⚠️ ERROR PARSING RESPONSE BODY AS JSON: $e");
          // Create a default response data object
          responseData = {
            "code": response.statusCode,
            "message": "Response body is not valid JSON",
            "rawRequest": "SIMULATED_rawRequest_FOR_TESTING_12345"
          };
          debugPrint("⚠️ USING DEFAULT RESPONSE DATA: $responseData");
        }

        // Log the parsed response for debugging
        _logBackendResponse(responseData);

        // Handle HTTP 400 error (Bad Request) or 405 error (Method Not Allowed)
        if (response.statusCode == 400 || response.statusCode == 405) {
          // Log the error for debugging
          Session().logError("payment_http_error",
              "Status Code: ${response.statusCode}, Body: ${response.body}");

          // Debug log for context mounted state
          debugPrint("⚠️ CONTEXT MOUNTED CHECK (400/405): ${context.mounted}");

          // Prepare error message
          String title = "Error";
          String message = responseData['message'] ?? "An error occurred";

          // Set generic message for 405 Method Not Allowed
          if (response.statusCode == 405) {
            title = "Error";
            message = "Something went wrong. Please try again later.";
          }

          // Use a delayed execution to ensure we're not in the middle of a build cycle
          Future.microtask(() {
            if (context.mounted) {
              // Try to close the loading dialog if it's showing
              try {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }
              } catch (e) {
                debugPrint("⚠️ Error closing dialog: $e");
              }

              // Show the error dialog
              _showErrorDialog(context, title, message);
            } else {
              debugPrint(
                  "⚠️ Context is no longer mounted, cannot show error dialog");
            }
          });

          return; // Exit the method
        }

        // Handle successful HTTP response (200 OK)
        if (response.statusCode == 200) {
          // Log successful response for debugging
          Session()
              .logSession("payment_success_response", responseData.toString());

          // Handle server error code within a successful HTTP response
          if (responseData['code'] == 500) {
            // Log server error for debugging
            Session().logError("payment_server_error", responseData['message']);
            throw Exception(responseData['message']);
          }

          // Extract raw request data from the response
          debugPrint("⚠️ RESPONSE DATA TYPE: ${responseData.runtimeType}");
          debugPrint("⚠️ RESPONSE DATA: $responseData");

          if (responseData.containsKey("rawRequest")) {
            debugPrint("⚠️ RAW REQUEST EXISTS");
            debugPrint(
                "⚠️ RAW REQUEST TYPE: ${responseData["rawRequest"].runtimeType}");

            // Check if rawRequest is a string or an object
            if (responseData["rawRequest"] is String) {
              debugPrint("⚠️ RAW REQUEST IS STRING");
              rawRequest = responseData["rawRequest"];
            } else {
              debugPrint("⚠️ RAW REQUEST IS NOT STRING, CONVERTING TO JSON");
              // If it's an object, convert it to JSON string
              try {
                rawRequest = jsonEncode(responseData["rawRequest"]);
                debugPrint(
                    "⚠️ SUCCESSFULLY CONVERTED RAW REQUEST TO JSON STRING");
              } catch (e) {
                debugPrint("⚠️ ERROR CONVERTING RAW REQUEST TO JSON: $e");
                // Fallback to default if conversion fails
                rawRequest = "SIMULATED_rawRequest_FOR_TESTING_12345";
              }
            }
            debugPrint("⚠️ FINAL RAW REQUEST: $rawRequest");
          } else {
            debugPrint("⚠️ RAW REQUEST DOES NOT EXIST IN RESPONSE");
            // If rawRequest is null, use a default value
            rawRequest = "SIMULATED_rawRequest_FOR_TESTING_12345";
            debugPrint("⚠️ Using default raw request: $rawRequest");
          }

          // Update payment object with the raw request from backend
          final params = paymentObject['params'] as Map<String, dynamic>;
          params['rawRequest'] = rawRequest;

          // Log the final processed response for debugging
          Session().logSession(
              "payment_processed_response", responseData.toString());

          // Execute JavaScript on web platforms or simulate on non-web platforms
          if (kIsWeb) {
            try {
              // Log JavaScript execution for debugging
              debugPrint("⚠️ EXECUTING JAVASCRIPT WITH PAYMENT OBJECT:");
              debugPrint("⚠️ ${jsonEncode(paymentObject)}");
              Session().logSession("js_execution",
                  "Executing JavaScript on web with: ${jsonEncode(paymentObject)}");

              // Execute JavaScript code with the payment object containing rawRequest
              debugPrint("⚠️ CALLING _executeJavaScript...");
              final jsResult = await _executeJavaScript(paymentObject);
              debugPrint("⚠️ _executeJavaScript COMPLETED");

              // Log JavaScript execution result for debugging
              debugPrint("⚠️ JAVASCRIPT EXECUTION RESULT: $jsResult");
              Session().logSession("js_execution_result", jsResult);
            } catch (e) {
              // Handle JavaScript execution errors
              if (context.mounted) {
                // Close loading dialog
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }

                // Show JavaScript error dialog
                _showErrorDialog(context, 'JavaScript Error',
                    'Failed to execute JavaScript: $e');
              }
              return; // Exit the method if JavaScript execution failed
            }
          } else {
            // Simulate JavaScript execution on non-web platforms (mobile, desktop)
            await _simulateJavaScriptExecution(paymentObject);
          }

          // The payment result will be handled by the JavaScript callback
          // We'll just close the loading dialog here
          if (context.mounted && Navigator.canPop(context)) {
            Navigator.pop(context);
          } else if (navigatorKey.currentContext != null) {
            try {
              Navigator.of(navigatorKey.currentContext!, rootNavigator: true)
                  .pop();
              debugPrint("⚠️ Closed loading dialog using global navigator key");
            } catch (e) {
              debugPrint("⚠️ Error closing dialog with global key: $e");
            }
          }

          // Note: The actual payment result handling is now done in the JavaScript callback
          // The callback will call handlePaymentResult which will show success/error UI

          return; // Exit the method after successful payment
        } else {
          // Handle other HTTP status codes
          // Debug log for context mounted state
          debugPrint(
              "⚠️ CONTEXT MOUNTED CHECK (other status): ${context.mounted}");

          // Prepare error message based on status code
          String title;
          String message;

          switch (response.statusCode) {
            case 401:
              title = 'Unauthorized';
              message =
                  'You are not authorized to make this payment. Please log in again.';
              break;
            case 403:
              title = 'Forbidden';
              message = 'You do not have permission to make this payment.';
              break;
            case 404:
              title = 'Not Found';
              message =
                  'The payment service could not be found. Please try again later.';
              break;
            case 500:
            case 502:
            case 503:
            case 504:
              title = 'Server Error';
              message =
                  'The payment server is currently unavailable. Please try again later.';
              break;
            default:
              title = 'Error (${response.statusCode})';
              message = responseData['message'] ??
                  'An error occurred during payment processing';
              break;
          }

          // Use a delayed execution to ensure we're not in the middle of a build cycle
          Future.microtask(() {
            if (context.mounted) {
              // Try to close the loading dialog if it's showing
              try {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }
              } catch (e) {
                debugPrint("⚠️ Error closing dialog: $e");
              }

              // Show the error dialog
              _showErrorDialog(context, title, message);
            } else {
              debugPrint(
                  "⚠️ Context is no longer mounted, using global navigator key");

              // Use the global navigator key to close any open dialogs first
              if (navigatorKey.currentContext != null) {
                // Try to close any open dialogs first
                try {
                  Navigator.of(navigatorKey.currentContext!,
                          rootNavigator: true)
                      .pop();
                  debugPrint(
                      "⚠️ Closed loading dialog using global navigator key");
                } catch (e) {
                  debugPrint("⚠️ Error closing dialog with global key: $e");
                }

                // Wait a moment before showing the error dialog
                Future.delayed(const Duration(milliseconds: 300), () {
                  if (navigatorKey.currentContext != null) {
                    _showErrorDialog(
                        navigatorKey.currentContext!, title, message);
                    debugPrint(
                        "⚠️ Showed error dialog using global navigator key");
                  } else {
                    debugPrint(
                        "⚠️ Global navigator key context is null after delay");
                  }
                });
              } else {
                debugPrint("⚠️ Global navigator key context is null");
              }
            }
          });

          return; // Exit the method
        }

        // Commented code removed for clarity
      } catch (e, stackTrace) {
        // Handle backend request errors
        debugPrint(
            "⚠️ CONTEXT MOUNTED CHECK (backend error): ${context.mounted}");
        debugPrint("⚠️ Backend request error: $e");
        debugPrint("⚠️ Error type: ${e.runtimeType}");
        debugPrint("⚠️ Stack trace: $stackTrace");

        // Use a delayed execution to ensure we're not in the middle of a build cycle
        Future.microtask(() {
          if (context.mounted) {
            // Try to close the loading dialog if it's showing
            try {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }
            } catch (dialogError) {
              debugPrint("⚠️ Error closing dialog: $dialogError");
            }

            // Show error dialog
            _showErrorDialog(
                context, 'Payment Error', 'Failed to initialize payment: $e');
          } else {
            debugPrint(
                "⚠️ Context is no longer mounted, using global navigator key");

            // Use the global navigator key to close any open dialogs first
            if (navigatorKey.currentContext != null) {
              // Try to close any open dialogs first
              try {
                Navigator.of(navigatorKey.currentContext!, rootNavigator: true)
                    .pop();
                debugPrint(
                    "⚠️ Closed loading dialog using global navigator key");
              } catch (e) {
                debugPrint("⚠️ Error closing dialog with global key: $e");
              }

              // Wait a moment before showing the error dialog
              Future.delayed(const Duration(milliseconds: 300), () {
                if (navigatorKey.currentContext != null) {
                  _showErrorDialog(navigatorKey.currentContext!,
                      'Payment Error', 'Failed to initialize payment: $e');
                  debugPrint(
                      "⚠️ Showed error dialog using global navigator key");
                } else {
                  debugPrint(
                      "⚠️ Global navigator key context is null after delay");
                }
              });
            } else {
              debugPrint("⚠️ Global navigator key context is null");
            }
          }
        });
        return; // Exit the method if backend request failed
      }
    } catch (e) {
      // Handle any unexpected errors in the main try-catch block
      debugPrint("⚠️ CONTEXT MOUNTED CHECK (main error): ${context.mounted}");
      debugPrint("⚠️ Main error: $e");

      // Use a delayed execution to ensure we're not in the middle of a build cycle
      Future.microtask(() {
        if (context.mounted) {
          // Try to close the loading dialog if it's still showing
          try {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          } catch (dialogError) {
            debugPrint("⚠️ Error closing dialog: $dialogError");
          }

          // Show general error dialog
          _showErrorDialog(context, 'Error', 'Failed to process payment: $e');
        } else {
          debugPrint(
              "⚠️ Context is no longer mounted, using global navigator key");

          // Use the global navigator key to close any open dialogs first
          if (navigatorKey.currentContext != null) {
            // Try to close any open dialogs first
            try {
              Navigator.of(navigatorKey.currentContext!, rootNavigator: true)
                  .pop();
              debugPrint("⚠️ Closed loading dialog using global navigator key");
            } catch (e) {
              debugPrint("⚠️ Error closing dialog with global key: $e");
            }

            // Wait a moment before showing the error dialog
            Future.delayed(const Duration(milliseconds: 300), () {
              if (navigatorKey.currentContext != null) {
                _showErrorDialog(navigatorKey.currentContext!, 'Error',
                    'Failed to process payment: $e');
                debugPrint("⚠️ Showed error dialog using global navigator key");
              } else {
                debugPrint(
                    "⚠️ Global navigator key context is null after delay");
              }
            });
          } else {
            debugPrint("⚠️ Global navigator key context is null");
          }
        }
      });
    }
  }

  // ==================== HELPER METHODS ====================

  /// Shows a loading dialog to indicate payment processing
  static void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Processing payment...'),
            ],
          ),
        );
      },
    );
  }

  /// Shows an error dialog with the specified title and message
  static void _showErrorDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(message),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  // _showSuccessDialog method removed as we now navigate directly to success page

  // The _handlePaymentResult method has been replaced by navigateToSuccessPage and showJavaScriptErrorDialog

  /// Navigates to the success page
  /// This method is called from the JavaScript callback
  static void navigateToSuccessPage(Member member) {
    debugPrint("⚠️ JAVASCRIPT CALLED navigateToSuccessPage");

    // Use the global navigator key to navigate to the success page
    if (navigatorKey.currentContext != null) {
      Navigator.push(
        navigatorKey.currentContext!,
        MaterialPageRoute(
          builder: (context) => PaymentSuccessPage(member: member),
        ),
      );
      debugPrint("⚠️ NAVIGATED TO SUCCESS PAGE");
    } else {
      debugPrint("⚠️ FAILED TO NAVIGATE TO SUCCESS PAGE: NO VALID CONTEXT");
    }
  }

  /// Shows an error dialog
  /// This method is called from the JavaScript callback
  static void showJavaScriptErrorDialog(String message) {
    debugPrint("⚠️ JAVASCRIPT CALLED showJavaScriptErrorDialog: $message");

    // Use the global navigator key to show the error dialog
    if (navigatorKey.currentContext != null) {
      _showErrorDialog(navigatorKey.currentContext!, 'Payment Failed', message);
      debugPrint("⚠️ SHOWED ERROR DIALOG");
    } else {
      debugPrint("⚠️ FAILED TO SHOW ERROR DIALOG: NO VALID CONTEXT");
    }
  }

  /// Creates the initial payment object with default values
  static Map<String, dynamic> _createInitialPaymentObject(
      String title, String amount) {
    return {
      'functionName': 'js_fun_start_pay',
      'params': {
        'rawRequest':
            'SIMULATED_rawRequest', // Will be updated with actual value from backend
        'functionCallBackName': 'handleinitDataCallback',
      },
    };
  }

  /// Logs payment details for debugging
  static void _logPaymentDetails(String title, String amount, int? memberId) {
    final logMessage = """
==== JAVASCRIPT PAYMENT PROCESSING ====
Title: $title
Amount: $amount
Member ID: $memberId
======================================""";

    // Log to console and session
    Session().logSession("payment_details", logMessage);
  }

  /// Logs backend request details for debugging
  static void _logBackendRequestDetails(
      int? memberId, String equbId, String amount) {
    final logMessage = """
==== MAKING BACKEND REQUEST ====
Member ID: $memberId
Equb ID: $equbId
Amount: $amount
================================""";

    // Log to session
    Session().logSession("backend_request", logMessage);

    // For immediate visibility in debug console
    debugPrint(logMessage);
  }

  /// Logs backend response for debugging
  static void _logBackendResponse(dynamic responseData) {
    final logMessage = """
==== BACKEND RESPONSE ====
Response: $responseData
============================""";

    // Log to session
    Session().logSession("backend_response", responseData.toString());

    // For immediate visibility in debug console
    debugPrint(logMessage);
  }

  /// Makes the backend request to initialize payment
  static Future<http.Response> _makeBackendRequest(
      String equbId, String amount, int? memberId) async {
    try {
      // Log request details before making the request
      final requestUrl = '${RequestHeader.baseApp}/telebirr-miniapp/initialize';
      final requestBody = {
        "equb_id": equbId,
        "amount": amount,
        "member_id": memberId
      };

      debugPrint('⬆️ SENDING HTTP REQUEST:');
      debugPrint('URL: $requestUrl');
      debugPrint('Body: ${json.encode(requestBody)}');

      Session().logSession(
          "http_request_start",
          {"url": requestUrl, "method": "POST", "body": requestBody}
              .toString());

      // Make the actual HTTP request
      final response = await http
          .post(
            Uri.parse(requestUrl),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode(requestBody),
          )
          .timeout(timeout);

      // Log response details after receiving the response
      debugPrint('⬇️ RECEIVED HTTP RESPONSE:');
      debugPrint('Status code: ${response.statusCode}');
      debugPrint(
          'Response body: ${response.body.substring(0, min(100, response.body.length))}${response.body.length > 100 ? "..." : ""}');

      Session().logSession(
          "http_request_complete",
          {
            "status_code": response.statusCode,
            "body_length": response.body.length,
            "body_preview":
                response.body.substring(0, min(100, response.body.length))
          }.toString());

      return response;
    } catch (e) {
      // Log any errors that occur during the request
      debugPrint('❌ HTTP REQUEST ERROR: $e');
      Session().logError("http_request_error", e.toString());
      rethrow; // Rethrow the error to be handled by the caller
    }
  }

  /// Executes JavaScript code for payment processing on web platform
  ///
  /// This method handles the JavaScript execution for payment processing on web platforms.
  /// It first sets the payment object as a global JavaScript variable, then executes the main
  /// payment processing code that uses this variable.
  ///
  /// @param paymentObject The payment object containing all necessary payment details
  /// @return A Future that resolves to a String containing the result of the JavaScript execution
  static Future<String> _executeJavaScript(
      Map<String, dynamic> paymentObject) async {
    debugPrint("Payment processing: Preparing JavaScript execution");

    // Log payment details for debugging (without sensitive information)
    final params = paymentObject['params'] as Map<String, dynamic>?;
    if (params != null) {
      final amount = params['amount'];
      final title = params['title'];
      debugPrint("Payment details - Title: $title, Amount: $amount");

      // Log that we're using rawRequest but don't log its contents (could be sensitive)
      if (params.containsKey('rawRequest')) {
        debugPrint("Payment includes rawRequest data from backend");
      }
    }

    // First, store the payment object in a global JavaScript variable
    // This avoids having to inject it directly into the JavaScript code string
    try {
      final paymentObjJson = jsonEncode(paymentObject);
      debugPrint("Payment processing: Setting payment data in JavaScript");

      // Set the payment object as a global JavaScript variable
      js.context.callMethod('eval', [
        """
        // Store payment data in a global variable for the payment processor to use
        window._paymentObject = $paymentObjJson;
        console.log('Payment data ready for processing');
      """
      ]);

      debugPrint("Payment processing: Data successfully prepared");
    } catch (e) {
      // Log the error and return an error message
      debugPrint(
          "Payment processing error: Failed to prepare payment data: $e");
      return "Error: Failed to prepare payment data: $e";
    }

    // Now execute the main JavaScript code that uses the global payment object
    return await executeJavaScript("""
      (function() {
        // Initialize payment processing
        console.log('Initializing payment processing');

        // Check if payment object is available
        if (!window._paymentObject) {
          console.error('Payment object not found');
          return 'Error: Payment object not found';
        }

        /**
         * Handles the payment result and communicates with the Flutter application
         * @param {boolean} success - Whether the payment was successful
         * @param {string} message - The message to display to the user
         * @param {Object} [data] - Additional data about the transaction
         * @returns {string} The result message
         */
        function handlePaymentResult(success, message, data) {
          console.log('Payment result:', { success, message, data });

          try {
            if (success) {
              // Handle successful payment
              console.log('Payment successful');

              // Call the Flutter success handler
              if (typeof window.navigateToSuccess === 'function') {
                window.navigateToSuccess();
                console.log('Navigation to success page initiated');
              } else {
                console.warn('Flutter success handler not available');
                // Fallback for testing environments
                alert('Payment successful: ' + message);
              }
            } else {
              // Handle failed payment
              console.log('Payment failed: ' + message);

              // Call the Flutter error handler
              if (typeof window.showErrorDialog === 'function') {
                window.showErrorDialog(message);
                console.log('Error dialog displayed');
              } else {
                console.warn('Flutter error handler not available');
                // Fallback for testing environments
                alert('Payment failed: ' + message);
              }
            }
          } catch (error) {
            // Handle any errors in the result processing
            console.error('Error processing payment result:', error);

            // Try to show error using Flutter handler
            try {
              if (typeof window.showErrorDialog === 'function') {
                window.showErrorDialog('Error processing payment result: ' + error.message);
              } else {
                alert('Error processing payment result: ' + error.message);
              }
            } catch (e) {
              // Last resort fallback
              alert('Payment error: ' + error.message);
            }
          }

          return message;
        }

        let callBackCount = 0;
        // Define the callback function that will be called by the third-party payment system
        window.handleinitDataCallback = function(response) {
          // Pass the response to Dart's handlePaymentResult function
          if (typeof window.handlePaymentResult === 'function') {
            window.handlePaymentResult(response, "Payment message");
          } else {
            console.error('Dart handlePaymentResult function is not defined');
          }
          return;
          handlePaymentResult(response, "");
          console.log('Payment callback received:', response);
          if (callBackCount > 0) {
            return;
          }
          callBackCount++;
          alert(`Payment callback: `);
          alert(response);
          alert(`res type check: `+(typeof response));

          try {
            let responseData;
            if (typeof response === 'string') {
                try {
                    // Debug logging to inspect the problematic area
                    console.log('Response length:', response.length);
                    console.log('Characters around position 188:', response.substring(178, 198));
                    alert(response.substring(178, Math.min(response.length, 198))); // shows 20 characters
                    
                    // First attempt direct JSON parse
                    try {
                        alert(`going to parse res`);
                        responseData = JSON.parse(response);
                    } catch (e) {
                        // If direct parse fails, clean and try again
                        let cleanResponse = response
                          .trim()
                          .replace(/\\n/g, '') // Remove newlines
                          .replace(/\\r/g, '') // Remove carriage returns
                          .replace(/\\t/g, '') // Remove tabs
                          ; // Fix URLs

                        // let cleanResponse = response
                        //     .trim()
                        //     .replace(/\\n/g, '')
                        //     .replace(/\\r/g, '')
                        //     .replace(/\\t/g, '')
                        //     .replace(/\\s+/g, ' ');
                        
                        // Debug the cleaned response
                        console.log('Cleaned response:', cleanResponse);
                        console.log('Characters around position 188 after cleaning:', 
                            cleanResponse.substring(178, 198));
                        alert(`retrying to parse res`);
                        alert(cleanResponse.substring(178, 198));
                        
                        responseData = JSON.parse(cleanResponse);
                    }
                    
                } catch (parseError) {
                    console.error('JSON Parse Error:', parseError);
                    console.log('Original response:', response);
                    console.log('First 200 characters:', response.substring(0, 200));
                    console.log('Last 200 characters:', response.substring(Math.max(0, response.length - 200)));
                    return handlePaymentResult(false, 'Invalid response format: ' + parseError.message);
                }
            } else if (typeof response === 'object' && response !== null) {
                responseData = response;
            } else {
                return handlePaymentResult(false, 'Invalid response type: ' + typeof response);
            }

            alert('responseData: ');
            alert(responseData);

            // Validate response structure
            if (!responseData || typeof responseData !== 'object') {
                return handlePaymentResult(false, 'Invalid response structure');
            }

            // BannerResponse special handling
            if (responseData.referenceData?.bannerResponse) {
                try {
                    let content = responseData.referenceData.bannerResponse;
                    console.log('Original:', content);
                    
                    content = content.trim();
                    console.log('After trim:', content);
                    
                    content = content.replace(/\\s+/g, ' ');
                    console.log('After space normalize:', content);
                    
                    content = content.replace(/([{,]\\s*)(\\w+)\\s*:/g, function(match, p1, p2) {
                        return p1 + '\\"' + p2 + '\\":';
                    });
                    console.log('After quoting keys:', content);
                    
                    content = content.replace(/:\\s*([^,}\\"\\[\\]]+)([,}\\]])/g, function(match, p1, p2) {
                        return ':\\"' + p1 + '\\"' + p2;
                    });
                    console.log('After quoting values:', content);
                    
                    content = content.replace(/:\\s*,/g, ':\\"\\",' );
                    console.log('After empty values:', content);
                    
                    content = content.replace(/https?:\\s*\\/\\//g, 'https://');
                    console.log('After URL fix:', content);

                    // Fix missing object closure
                    content = content.replace(/\\]/, '}]');
                    console.log('After closing fix:', content);
                    
                    console.log('Final content:', content);
                    
                    // Don't wrap in additional brackets since input already has them
                    responseData.referenceData.bannerResponse = JSON.parse(content);
                } catch (error) {
                    console.error('Parse error:', error);
                    responseData.referenceData.bannerResponse = [];
                }
            }

            // Final validation
            if (!responseData.referenceData?.prepayId) {
                responseData.referenceData = responseData.referenceData || {};
                responseData.referenceData.prepayId = 
                    (responseData.referenceData.prepayId || '')
                        .replace(/[^a-zA-Z0-9-]/g, '')
                        .slice(0, 32);
            }

            // Check if the response indicates success
            let isSuccess = false;
            let responseMessage = '';
            let transactionId = '';

            isSuccess = responseData.result === 'success';
            responseMessage = isSuccess ? 'Payment processed successfully' : 'Payment processing failed';

            // Extract transaction ID using optional chaining
            transactionId = responseData?.transactionId || 
                           responseData?.transaction_id || 
                           responseData?.id || '';

            if (transactionId) {
                responseMessage += ' (Transaction ID: ' + transactionId + ')';
            }

            return handlePaymentResult(isSuccess, responseMessage);
          } catch (error) {
            console.error('Processing error:', error);
            return handlePaymentResult(false, 'Error processing payment: ' + error.message);
          }
        };

        // Check if window.consumerapp exists
        if (window.consumerapp) {
          console.log('window.consumerapp exists');

          if (typeof window.consumerapp.evaluate === 'function') {
            console.log('window.consumerapp.evaluate is a function');

            try {
              // Safely trim rawRequest if it exists
              if (window._paymentObject &&
                  window._paymentObject.params &&
                  typeof window._paymentObject.params.rawRequest === 'string') {
                console.log('Trimming rawRequest to remove whitespace');
                window._paymentObject.params.rawRequest = window._paymentObject.params.rawRequest.trim();
              } else {
                console.log('rawRequest not available or not a string - skipping trim');
              }
              alert(`res rawRequest: `+JSON.stringify(window._paymentObject));

              // Use the payment object from the global variable
              const result = window.consumerapp.evaluate(JSON.stringify(window._paymentObject));
              console.log('window.consumerapp.evaluate result:', result);
            } catch (error) {
              console.error('Error calling window.consumerapp.evaluate:', error);
              return handlePaymentResult(false, 'Error calling payment service: ' + error.message);
            }
          } else {
            console.log('window.consumerapp.evaluate is NOT a function');
            return handlePaymentResult(false, 'Payment service is not properly configured');
          }
        }
        else {
          console.log('window.consumerapp does NOT exist, creating simulation');
          alert("[Testing] consumer");

          // Create a payment service simulation for development/testing environments
          console.log('Creating payment service simulation');

          // Define the payment service interface
          window.consumerapp = {
            evaluate: function(paymentDataStr) {
              console.log('Payment service simulation activated');

              try {
                // Parse the payment data
                const paymentData = JSON.parse(paymentDataStr);

                // Log payment details for debugging
                console.log('Processing payment with amount:',
                  paymentData.params && paymentData.params.amount ? paymentData.params.amount : 'unknown');

                // Check if we should simulate a failure (for testing error handling)
                const simulateFailure = false; // Set to true to test error handling

                // Simulate the payment processing with a delay
                // In production, this would be replaced by the actual payment gateway
                setTimeout(function() {
                  if (simulateFailure) {
                    // Simulate a failed payment
                    console.log('Simulating payment failure');
                    window.handleinitDataCallback({
                      success: false,
                      result: 'failed',
                      message: 'Payment declined (simulation)',
                      error_code: 'SIMULATION_DECLINED',
                      timestamp: new Date().toISOString()
                    });
                  } else {
                    const testResponse = `
                      {
                        "result": "success",
                        "referenceData": {
                          "transactionCredentialCode": "ODAxODAwMDIwNjAxMDI5MTAyMDEzMTgxMjQwMDBBNDMONDRmMzEzMDUwNDEzMzQ5MzU2MzA00UI1Mw==",
                          "showBanner": "true",
                          "bannerResponse": "[{endTime:1764190800000,execute: https://deje.shop/,imgUrl:https://developerportal.ethiotelebirr.et:38443/customer/img/Money_Transfer.png,order:1,reportTag:,showSeconds:4,startTime:1732693066000},{endTime:1746011109000,execute: merchant://100000000036,imgUrl:https://developerportal.ethiotelebirr.et:38443/customer/img/eid/banner/en.png,order:1,reportTag:,showSeconds:4,startTime:1742814347000},{endTime:1765659600000,execute: https://deje.shop/,imgUrl:https://developerportal.ethiotelebirr.et:38443/customer/img/Money_Transfer.png,order:5,reportTag:,showSeconds:5,startTime:1734173353000}]",
                          "billShareUrl": "merchant://10000000009?orderId=016011074023151700001008&mmOrderId=CDO70PA43Z",
                          "tipUrl": "merchant://10000000006?orderId=016011074023151700001008",
                          "billShareDisplay": "Bill Share",
                          "tipDisplay": "Give Tip",
                          "resultCode": "1",
                          "prepayId": "01674b39121214bc82f95d386269e38f319008"
                        }
                      }
                    `;
                    // Simulate a successful payment
                    console.log('Simulating successful payment');
                    window.handleinitDataCallback(testResponse);
                  }
                }, 2000); // 2 second delay to simulate processing time

                return true; // Indicate that the payment process has started
              } catch (error) {
                console.error('Payment simulation error:', error);

                // Report the error through the callback
                window.handleinitDataCallback({
                  success: false,
                  status: 'error',
                  message: 'Payment processing error: ' + error.message,
                  error_code: 'SIMULATION_ERROR',
                  timestamp: new Date().toISOString()
                });

                return false; // Indicate that the payment process failed to start
              }
            }
          };

          try {
            // Start the payment process using the simulation
            console.log('Initiating simulated payment process');

            // Safely trim rawRequest if it exists (same as in the real payment flow)
            if (window._paymentObject &&
                window._paymentObject.params &&
                typeof window._paymentObject.params.rawRequest === 'string') {
              console.log('Simulation: Trimming rawRequest to remove whitespace');
              window._paymentObject.params.rawRequest = window._paymentObject.params.rawRequest.trim();
            }

            const result = window.consumerapp.evaluate(JSON.stringify(window._paymentObject));

            if (result) {
              console.log('Payment process initiated successfully');
            } else {
              console.error('Failed to initiate payment process');
              return handlePaymentResult(false, 'Failed to initiate payment process');
            }
          } catch (error) {
            console.error('Error starting payment process:', error);
            return handlePaymentResult(false, 'Error starting payment: ' + error.message);
          }
        }

        return 'JavaScript execution completed';
      })();
    """);
  }

  static void setupJavaScriptCallback() {
    js.context['handlePaymentResult'] =
        (dynamic res, String message, [dynamic data]) {
      try {
        // Parse the response if it's a JSON string
        if (res is String) {
          final parsedResponse = jsonDecode(res);
          debugPrint("Parsed Response: $parsedResponse");
          // Handle the parsed response
          handleParsedResponse(parsedResponse.result, message, data);
        } else if (res is Map<String, dynamic>) {
          // If the response is already a Map, use it directly
          debugPrint("Response is already a Map: $res");
          handleParsedResponse(res, message, data);
        } else {
          debugPrint("Invalid response type: $res");
        }
      } catch (e) {
        debugPrint("Error parsing response: $e");
      }
    };
  }

  static void handleParsedResponse(
      Map<String, dynamic> response, String message, dynamic data) {
    // Process the parsed response here
    debugPrint("Handling parsed response: $response");
    debugPrint("Message: $message");
    debugPrint("Data: $data");
  }

  /// Simulates JavaScript execution on non-web platforms
  ///
  /// This method provides a simulation of the payment process for non-web platforms
  /// (mobile, desktop) where JavaScript execution is not available. It simulates
  /// the payment flow with appropriate delays and logging.
  ///
  /// @param paymentObject The payment object containing payment details
  static Future<void> _simulateJavaScriptExecution(
      Map<String, dynamic> paymentObject) async {
    // Extract payment details for logging (without sensitive information)
    final params = paymentObject['params'] as Map<String, dynamic>?;
    final amount = params?['amount'] ?? 'unknown';
    final title = params?['title'] ?? 'Payment';

    // Log the start of the payment simulation
    debugPrint("Payment simulation started for: $title ($amount)");
    Session().logSession(
        "payment_simulation",
        {
          "event": "simulation_started",
          "title": title,
          "amount": amount,
          "timestamp": DateTime.now().toIso8601String()
        }.toString());

    // Simulate the payment processing delay
    debugPrint("Processing payment...");
    await Future.delayed(const Duration(seconds: 2));

    // Log successful completion
    debugPrint("Payment simulation completed successfully");
    Session().logSession(
        "payment_simulation",
        {
          "event": "simulation_completed",
          "status": "success",
          "transaction_id": "SIM_${DateTime.now().millisecondsSinceEpoch}",
          "timestamp": DateTime.now().toIso8601String()
        }.toString());

    // Note: The actual UI updates (success dialog, navigation) are handled in the main executePayment method
  }
}
