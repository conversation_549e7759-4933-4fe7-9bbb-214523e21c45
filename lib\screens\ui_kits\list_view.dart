// ignore_for_file: must_be_immutable

import 'package:ekub/models/members.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/admin/actions/edit_member.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

// Format date with respect to the current locale
String formatDate(String date, [BuildContext? context]) {
  if (context != null) {
    // Use the current app locale if context is provided
    final locale = Localizations.localeOf(context).toString();
    return DateFormat('MMMM-d-yyyy', locale).format(DateTime.parse(date));
  }
  // Fallback to default format
  return DateFormat('MMMM-d-yyyy').format(DateTime.parse(date));
}

class ListCard extends StatelessWidget {
  const ListCard({
    super.key,
    required this.title,
    required this.svgSrc,
    required this.amountOfFiles,
    required this.numOfFiles,
    required this.icon,
    required this.theme,
  });

  final String title, svgSrc, amountOfFiles;
  final int numOfFiles;
  final IconData icon;
  final Color theme;

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      margin: const EdgeInsets.only(top: 5),
      child: Container(
        padding: const EdgeInsets.only(top: 10, bottom: 15, right: 5, left: 5),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(defaultPadding),
          ),
        ),
        child: Row(
          children: [
            SizedBox(
              height: 30,
              width: 30,
              child: Icon(
                icon,
                size: 38,
                color: Theme.of(context).iconTheme.color,
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: fontMedium,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        "$numOfFiles Birr",
                        style: const TextStyle(
                          fontSize: fontSmall,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Text(
              amountOfFiles,
              style: const TextStyle(
                fontSize: fontMedium,
                fontWeight: FontWeight.bold,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class MemberCard extends StatelessWidget {
  const MemberCard(
      {super.key,
      required this.isAdmin,
      required this.title,
      required this.phone,
      required this.status,
      required this.member,
      required this.icon,
      required this.theme});

  final String title, phone, status;
  final IconData icon;
  final Color theme;
  final Member member;
  final bool? isAdmin;

  @override
  Widget build(BuildContext context) {
    ThemeProvider themeProvider =
        Provider.of<ThemeProvider>(context, listen: false);

    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.only(top: 15, bottom: 15, right: 10, left: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5), //color of shadow
            spreadRadius: .1, //spread radius
            blurRadius: 2, // blur radius
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                    border: Border.all(color: themeProvider.getColor, width: 3),
                    color: themeProvider.getColor,
                    shape: BoxShape.circle),
                height: 50,
                width: 50,
                child: CircleAvatar(
                  backgroundColor: themeProvider.getColor,
                  // backgroundImage: AssetImage("assets/icons/user.jpg"),
                  child: Text(
                    title.substring(0, 1).toUpperCase(),
                    style: const TextStyle(
                        fontSize: 25,
                        color: Colors.white,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 18),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        toCamelCase(title),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: fontMedium,
                          fontWeight: boldFont,
                        ),
                      ),
                      InkWell(
                        child: Container(
                          width: 130,
                          margin: const EdgeInsets.only(top: 5),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 5.0, vertical: 0),
                          child: Row(
                            children: [
                              Text(
                                phone,
                                style: TextStyle(
                                    fontSize: fontSmall,
                                    fontWeight: normalFontWeight,
                                    color: Colors.grey.shade400),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              isAdmin == true
                  ? Column(children: [
                      IconButton(
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => EditMember(
                                      args: EditMemberArgs(
                                          member: member, forEdit: true))));
                        },
                        icon: Container(
                          height: 35,
                          width: 40,
                          decoration: BoxDecoration(
                              color: Colors.deepPurple[50],
                              shape: BoxShape.circle),
                          child: const Icon(
                            Icons.edit_outlined,
                            size: 20,
                            color: Colors.deepPurple,
                          ),
                        ),
                      )
                    ])
                  : const Text(""),
            ],
          ),
        ],
      ),
    );
  }
}

class EqubTypeCard extends StatelessWidget {
  EqubTypeCard({
    super.key,
    required this.title,
    required this.round,
    required this.rote,
    required this.status,
    required this.icon,
    required this.theme,
    required this.type,
  });

  final String title, round, status, rote, type;
  final IconData icon;
  final ThemeProvider theme;

  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 120,
      padding: const EdgeInsets.only(top: 10, bottom: 15, right: 5, left: 0),
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5), //color of shadow
            spreadRadius: .1, //spread radius
            blurRadius: 2, // blur radius
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: const BoxDecoration(),
                            child: Text(
                              title,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                fontSize: fontSmall,
                                color: Colors.black,
                                fontWeight: boldFont,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 3),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: theme.getLightColor.withOpacity(.4)),
                        child: Text(
                          status,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: fontSmall,
                            color: Colors.black,
                            fontWeight: normalFontWeight,
                          ),
                        ),
                      ),
                    ],
                  ),
                  // const Divider(),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Row(
                          children: [
                            Text(
                              "${EkubLocalization.of(context)!.translate("round")}: ",
                              style: TextStyle(
                                fontSize: fontSmall,
                                color: Colors.grey.shade400,
                                fontWeight: boldFont,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 5),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.deepPurple[50]),
                              child: Text(
                                round,
                                style: const TextStyle(
                                  overflow: TextOverflow.ellipsis,
                                  fontSize: fontSmall,
                                  color: Colors.deepPurple,
                                  fontWeight: normalFontWeight,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 0.0,
                        ),
                        child: Row(
                          children: [
                            Text(
                              "${EkubLocalization.of(context)!.translate("rote")}: ",
                              style: TextStyle(
                                fontSize: fontSmall,
                                color: Colors.grey.shade400,
                                fontWeight: boldFont,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 5, vertical: 5),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.deepPurple[50]),
                              child: Text(
                                rote,
                                style: const TextStyle(
                                  fontSize: fontSmall,
                                  color: Colors.deepPurple,
                                  fontWeight: normalFontWeight,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 0, left: 0),
                        child: Row(
                          children: [
                            Text(
                              "${EkubLocalization.of(context)!.translate("type")}: ",
                              style: TextStyle(
                                fontSize: fontSmall,
                                color: Colors.grey.shade400,
                                fontWeight: boldFont,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 5, vertical: 5),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.deepPurple[50]),
                              child: Text(
                                type,
                                style: const TextStyle(
                                  overflow: TextOverflow.ellipsis,
                                  fontSize: fontSmall,
                                  color: Colors.deepPurple,
                                  fontWeight: normalFontWeight,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  var myMenuItems = <String>['Delete', 'Deactivate'];
}

class EqubCard extends StatelessWidget {
  EqubCard({
    super.key,
    required this.status,
    required this.round,
    required this.startDate,
    required this.endDate,
    required this.lotteryDate,
    required this.amount,
    required this.totalAmount,
    required this.equbType,
    required this.icon,
    required this.theme,
    required this.totalPayment,
    required this.remainingPayment,
    required this.remainingLotteryDate,
  });

  final String status,
      startDate,
      round,
      endDate,
      lotteryDate,
      amount,
      totalAmount,
      equbType,
      totalPayment,
      remainingPayment,
      remainingLotteryDate;
  final IconData icon;
  final ThemeProvider theme;

  @override
  Widget build(BuildContext context) {
    var language = EkubLocalization.of(context)!;

    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 0.5,
              blurRadius: 4,
              offset: const Offset(0, 3),
            ),
          ]),
      padding: const EdgeInsets.all(10.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  Text(
                    equbType,
                    style: const TextStyle(
                        overflow: TextOverflow.ellipsis,
                        color: Colors.black,
                        fontSize: fontMedium,
                        fontWeight: boldFont),
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    EkubLocalization.of(context)!.translate("round"),
                    style: TextStyle(
                        overflow: TextOverflow.ellipsis,
                        color: bodyTextColor,
                        fontSize: fontSmall,
                        fontWeight: normalFontWeight),
                  ),
                  Container(
                    alignment: Alignment.center,
                    margin:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 1),
                    height: 20,
                    width: 25,
                    decoration: BoxDecoration(
                        color: ColorProvider.backgroundColor,
                        shape: BoxShape.circle),
                    child: Text(
                      round,
                      style: TextStyle(
                          overflow: TextOverflow.ellipsis,
                          color: theme.getColor,
                          fontSize: fontSmall,
                          fontWeight: normalFontWeight),
                    ),
                  ),
                  const SizedBox(width: 10),
                  // Padding(
                  //   padding: const EdgeInsets.all(8.0),
                  //   child: Container(
                  //       decoration: BoxDecoration(
                  //           color: theme.getColor.withOpacity(.4),
                  //           border: Border.all(
                  //             color: theme.getColor.withOpacity(.4),
                  //           ),
                  //           borderRadius:
                  //               const BorderRadius.all(Radius.circular(10))),
                  //       child: const SizedBox(
                  //         width: 5,
                  //         height: 5,
                  //       )),
                  // ),
                  Container(
                    alignment: Alignment.center,
                    // margin: const EdgeInsets.symmetric(horizontal: 5),
                    height: 20,
                    width: 60,
                    decoration: BoxDecoration(
                        color: theme.getLightColor.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(20)),
                    child: Text(
                      status,
                      style: TextStyle(
                          color: theme.getColor,
                          fontSize: fontSmall,
                          fontWeight: boldFont),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Divider(
            color: Colors.grey[300],
            height: 3,
          ),
          // Padding(
          //   padding: const EdgeInsets.only(top: 8.0),
          //   child: Row(
          //     children: [
          //       Row(
          //         children: [
          //           Text("${language.translate("remaining_payment")}:",
          //               style: TextStyle(
          //                 color: bodyTextColor,
          //                 fontSize: fontSmall,
          //                 fontWeight: boldFont,
          //               )),
          //           const SizedBox(
          //             width: 5,
          //           ),
          //           Container(
          //             padding: const EdgeInsets.symmetric(
          //                 horizontal: 3, vertical: 3),
          //             decoration: BoxDecoration(
          //                 borderRadius: BorderRadius.circular(10),
          //                 color: theme.getColor.withOpacity(.4)),
          //             child: Text(
          //               '${formatCurrency.format(double.parse(remainingPayment))} ${language.translate("etb")} ',
          //               style: TextStyle(
          //                 overflow: TextOverflow.ellipsis,
          //                 color: whiteText,
          //                 fontSize: fontSmall,
          //                 fontWeight: normalFontWeight,
          //               ),
          //             ),
          //           ),
          //         ],
          //       ),
          //     ],
          //   ),
          // ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(language.translate("remaining_lottery_date"),
                        style: lableStyle),
                    const SizedBox(
                      height: 5,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 3),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: ColorProvider.backgroundColor),
                      child: Text(
                        remainingLotteryDate != "passed"
                            ? remainingLotteryDate == "0"
                                ? "Unassigned"
                                : "$remainingLotteryDate ${language.translate("days")}"
                            : "$remainingLotteryDate ",
                        style: TextStyle(
                          color: theme.getColor,
                          fontSize: fontSmall,
                          fontWeight: boldFont,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(" ${language.translate("remaining_days")}",
                            style: lableStyle),
                        const SizedBox(
                          width: 0,
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 3, vertical: 3),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: ColorProvider.backgroundColor),
                          child: Text(
                            overflow: TextOverflow.ellipsis,
                            (DateTime.parse(endDate)
                                            .difference(DateTime.now())
                                            .inDays +
                                        1) <
                                    0
                                ? language.translate("passed")
                                : " ${(DateTime.parse(endDate).difference(DateTime.now()).inDays + 1).toString()} ${language.translate("days")}",
                            style: TextStyle(
                              overflow: TextOverflow.ellipsis,
                              color: theme.getColor,
                              fontSize: fontSmall,
                              fontWeight: boldFont,
                            ),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(" ${language.translate("started")}",
                              style: lableStyle),
                          const SizedBox(
                            height: 5,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 5, vertical: 3),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: ColorProvider.backgroundColor),
                            child: Text(
                              formatDate(startDate, context),
                              style: TextStyle(
                                color: theme.getColor,
                                fontWeight: boldFont,
                                fontSize: fontSmall,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Row(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: .0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(" ${language.translate("ends")}",
                                        style: lableStyle),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 5, vertical: 3),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          color: ColorProvider.backgroundColor),
                                      child: Text(
                                        formatDate(endDate, context),
                                        style: TextStyle(
                                          overflow: TextOverflow.ellipsis,
                                          color: theme.getColor,
                                          fontWeight: boldFont,
                                          fontSize: fontSmall,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Divider(
                  color: Colors.grey[300],
                  height: 3,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5.0),
                      child: Row(
                        children: [
                          Text("${language.translate("paid")} : ",
                              style: const TextStyle(
                                fontSize: fontSmall,
                                fontWeight: boldFont,
                                color: Colors.deepPurple,
                              )),
                          Text(
                            '${formatCurrency.format(double.parse(totalPayment))}  ${language.translate("etb")}',
                            //_formatedDate(award.startDate ?? sampleUtcU),
                            style: const TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontSize: fontSmall,
                                color: Colors.deepPurple,
                                fontWeight: boldFont),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text("${language.translate("of")} ",
                                style: const TextStyle(
                                  fontSize: fontSmall,
                                  color: Colors.deepPurple,
                                  fontWeight: boldFont,
                                )),
                          ),
                          Text(
                            '${formatCurrency.format(double.parse(totalAmount))}  ${language.translate("etb")}',
                            style: const TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontSize: fontSmall,
                                color: Colors.deepPurple,
                                fontWeight: boldFont),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 5.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: LinearPercentIndicator(
                          alignment: MainAxisAlignment.start,
                          animation: true,
                          lineHeight: 5.0,
                          animationDuration: 2500,
                          percent: _inDecimal(),
                          backgroundColor: _inDecimal() > .5
                              ? Colors.grey.shade400
                              : _inDecimal() > .75
                                  ? theme.getColor.withOpacity(0.4)
                                  : Colors.grey.withOpacity(0.6),
                          barRadius: const Radius.circular(3),
                          progressColor: theme.getLightColor,
                        ),
                      ),
                      Text(
                        _inPercent(),
                        style: const TextStyle(
                            color: Colors.deepPurple,
                            fontSize: fontSmall,
                            fontWeight: boldFont),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _inDecimal() {
    var dec = double.parse(totalPayment) / double.parse(totalAmount);
    if (dec > 1) {
      dec = 1;
    }
    if (dec.isNaN) {
      return 0.toDouble();
    }
    return dec;
  }

  String _inPercent() {
    double val = _inDecimal() * 100;
    String p = "%";
    String res = val.toStringAsFixed(2);
    // String result = res.split(".")[0];
    return res + p;
  }

  final formatCurrency = NumberFormat("#,##0.00", "en_US");
}

class PaymentCard extends StatelessWidget {
  PaymentCard(
      {super.key,
      required this.amount,
      required this.paymentType,
      required this.credit,
      required this.balance,
      required this.collector,
      required this.status,
      required this.icon,
      required this.theme,
      required this.createdAt,
      required this.updatedAt,
      required Null Function() onTap});
  final String amount;
  final String paymentType;
  final String credit;
  final String balance;
  final String collector;

  final String status;
  final String createdAt;
  final String updatedAt;
  final IconData icon;
  final ThemeProvider theme;

  @override
  Widget build(BuildContext context) {
    var language = EkubLocalization.of(context)!;

    return Container(
      margin: const EdgeInsets.all(5),
      padding: const EdgeInsets.only(top: 10, bottom: 15, right: 5, left: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5), //color of shadow
            spreadRadius: .1, //spread radius
            blurRadius: 2, // blur radius
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              '${formatCurrency.format(double.parse(amount))} ${language.translate("etb")}',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                fontSize: fontMedium,
                                fontWeight: boldFont,
                              ),
                            ),
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.date_range,
                                  color: Colors.grey.shade400),
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                height: 22,
                                // width: 40,
                                decoration: BoxDecoration(
                                    color: Colors.deepPurple.withOpacity(.1),
                                    borderRadius: BorderRadius.circular(15)),
                                child: Text(
                                  formatDate(createdAt, context),
                                  style: const TextStyle(
                                    fontSize: fontSmall,
                                    color: Colors.deepPurple,
                                    fontWeight: boldFont,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.all(.0),
                        child: Row(
                          children: [
                            Text(
                              paymentType.toUpperCase(),
                              style: const TextStyle(
                                fontSize: fontSmall,
                                color: Colors.deepPurple,
                                fontWeight: boldFont,
                              ),
                            ),
                            const Padding(
                              padding: EdgeInsets.all(5.0),
                              child: Text(
                                "-",
                                style: TextStyle(
                                  fontSize: fontSmall,
                                  color: Colors.deepPurple,
                                  fontWeight: boldFont,
                                ),
                              ),
                            ),
                            Text(
                              collector,
                              style: const TextStyle(
                                fontSize: fontSmall,
                                color: Colors.deepPurple,
                                fontWeight: boldFont,
                              ),
                            ),
                            status == "pending"
                                ? const Text(
                                    ' -> pending',
                                    style: TextStyle(
                                      fontSize: fontSmall,
                                      color: Colors.amber,
                                      fontWeight: boldFont,
                                    ),
                                  )
                                : Container(),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5.0, left: 10.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(EkubLocalization.of(context)!.translate("balance"),
                        style: lableStyle),
                    const SizedBox(height: 5),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      height: 22,
                      // width: 40,
                      decoration: BoxDecoration(
                          color: theme.getLightColor.withOpacity(.1),
                          borderRadius: BorderRadius.circular(15)),
                      child: Text(
                        formatCurrency.format(double.parse(balance)),
                        style: TextStyle(
                          fontSize: fontSmall,
                          color: theme.getLightColor,
                          fontWeight: boldFont,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 5.0, right: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(language.translate("credit"), style: lableStyle),
                    const SizedBox(height: 5),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      height: 25,
                      // width: 40,
                      decoration: BoxDecoration(
                          color: Colors.red.withOpacity(.1),
                          borderRadius: BorderRadius.circular(15)),
                      child: Text(
                        formatCurrency.format(double.parse(credit)),
                        style: const TextStyle(
                          fontSize: fontSmall,
                          color: Colors.red,
                          fontWeight: boldFont,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  final formatCurrency = NumberFormat("#,##0.00", "en_US");
}

class TakerCard extends StatelessWidget {
  TakerCard(
      {super.key,
      required this.lotteryAmount,
      required this.paymentType,
      required this.takerName,
      required this.remainingAmount,
      required this.payedBy,
      required this.chequeAmount,
      required this.chequeBankName,
      required this.chequeDescription,
      required this.status,
      required this.icon,
      required this.theme,
      required this.createdAt,
      required this.updatedAt});
  final String takerName;
  final String lotteryAmount;
  final String paymentType;
  final String remainingAmount;
  final String payedBy;

  final String chequeAmount;
  String? chequeBankName;
  final String chequeDescription;

  final String status;
  final String createdAt;
  final String updatedAt;
  final IconData icon;
  final ThemeProvider theme;

  @override
  Widget build(BuildContext context) {
    var language = EkubLocalization.of(context)!;

    return Container(
      margin: const EdgeInsets.all(5),
      padding: const EdgeInsets.only(top: 10, bottom: 15, right: 5, left: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5), //color of shadow
            spreadRadius: .1, //spread radius
            blurRadius: 2, // blur radius
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              '${formatCurrency.format(double.parse(lotteryAmount))} ${language.translate("etb")}',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                fontSize: fontMedium,
                                fontWeight: boldFont,
                              ),
                            ),
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.date_range,
                                  color: Colors.grey.shade400),
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                height: 22,
                                // width: 40,
                                decoration: BoxDecoration(
                                    color: Colors.deepPurple.withOpacity(.1),
                                    borderRadius: BorderRadius.circular(15)),
                                child: Text(
                                  formatDate(createdAt, context),
                                  style: const TextStyle(
                                    fontSize: fontSmall,
                                    color: Colors.deepPurple,
                                    fontWeight: boldFont,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // Container(
              //   padding: const EdgeInsets.all(5.0),
              //   width: chequeBankName != null
              //       ? MediaQuery.of(context).size.width * .45
              //       : MediaQuery.of(context).size.width * .35,
              //   child: Column(
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       Text(
              //         "${EkubLocalization.of(context)!.translate("paid_with")}: $paymentType",
              //         style: TextStyle(
              //           fontSize: fontSmall,
              //           color: bodyTextColor,
              //           fontWeight: normalFontWeight,
              //         ),
              //       ),
              //       Text(
              //         '${EkubLocalization.of(context)!.translate("paid_by")}: $payedBy',
              //         style: TextStyle(
              //           fontSize: fontSmall,
              //           color: bodyTextColor,
              //           fontWeight: normalFontWeight,
              //         ),
              //       ),
              //       if (chequeBankName != null)
              //         SizedBox(
              //           width: MediaQuery.of(context).size.width * .45,
              //           child: Text(
              //             "${EkubLocalization.of(context)!.translate("check_from")}: $chequeBankName",
              //             style: TextStyle(
              //               fontSize: fontSmall,
              //               color: bodyTextColor,
              //               fontWeight: normalFontWeight,
              //             ),
              //           ),
              //         ),
              //       if (chequeBankName != null)
              //         SizedBox(
              //           width: MediaQuery.of(context).size.width * .45,
              //           child: Text(
              //             "${EkubLocalization.of(context)!.translate("check_detail")}: $chequeDescription",
              //             style: TextStyle(
              //               fontSize: fontSmall,
              //               color: bodyTextColor,
              //               fontWeight: normalFontWeight,
              //             ),
              //           ),
              //         ),
              //     ],
              //   ),
              // ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: Row(
              children: [
                Text(
                  paymentType.toUpperCase(),
                  style: const TextStyle(
                    fontSize: fontSmall,
                    color: Colors.deepPurple,
                    fontWeight: boldFont,
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(5.0),
                  child: Text(
                    "-",
                    style: TextStyle(
                      fontSize: fontSmall,
                      color: Colors.deepPurple,
                      fontWeight: boldFont,
                    ),
                  ),
                ),
                Text(
                  payedBy,
                  style: const TextStyle(
                    fontSize: fontSmall,
                    color: Colors.deepPurple,
                    fontWeight: boldFont,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Row(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 0),
                child: Row(
                  children: [
                    Text(
                      '${language.translate("remaining_amount")} ',
                      style: const TextStyle(
                        fontSize: fontSmall,
                        color: Colors.black,
                        fontWeight: boldFont,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      height: 22,
                      // width: 40,
                      decoration: BoxDecoration(
                          color: Colors.deepPurple.withOpacity(.1),
                          borderRadius: BorderRadius.circular(15)),
                      child: Text(
                        "${formatCurrency.format(double.parse(remainingAmount))} ${language.translate("etb")}",
                        style: const TextStyle(
                          fontSize: fontSmall,
                          color: Colors.deepPurple,
                          fontWeight: boldFont,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10.0),
                child: Row(
                  children: [
                    Text("${language.translate("paid")} : ",
                        style: TextStyle(
                          fontSize: fontSmall,
                          fontWeight: boldFont,
                          color: Colors.grey.shade400,
                        )),
                    status == "unpaid"
                        ? Text(
                            'ETB 0.0',
                            style: TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontSize: fontSmall,
                                color: Colors.grey.shade400,
                                fontWeight: boldFont),
                          )
                        : Text(
                            '${formatCurrency.format(double.parse(lotteryAmount))} ${language.translate("etb")}',
                            style: TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontSize: fontSmall,
                                color: Colors.grey.shade400,
                                fontWeight: boldFont),
                          ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text("${language.translate("of")} ",
                          style: TextStyle(
                            fontSize: fontSmall,
                            color: Colors.grey.shade400,
                            fontWeight: boldFont,
                          )),
                    ),
                    status == "unpaid"
                        ? Text(
                            '${formatCurrency.format(double.parse(lotteryAmount))} ${language.translate("etb")}',
                            style: TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontSize: fontSmall,
                                color: Colors.grey.shade400,
                                fontWeight: boldFont),
                          )
                        : Text(
                            '${formatCurrency.format(total())} ${language.translate("etb")}',
                            style: TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontSize: fontSmall,
                                color: Colors.grey.shade400,
                                fontWeight: boldFont),
                          )
                  ],
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 0.0),
            child: Row(
              children: [
                Expanded(
                  child: LinearPercentIndicator(
                    alignment: MainAxisAlignment.start,
                    animation: true,
                    lineHeight: 5.0,
                    animationDuration: 2500,
                    percent: _inDecimal(),
                    backgroundColor: _inDecimal() > .5
                        ? Colors.grey.shade400
                        : _inDecimal() > .75
                            ? theme.getColor.withOpacity(0.4)
                            : Colors.grey.withOpacity(0.6),
                    barRadius: const Radius.circular(3),
                    progressColor: theme.getLightColor,
                  ),
                ),
                Text(
                  _inPercent(),
                  style: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: fontSmall,
                      fontWeight: boldFont),
                ),
              ],
            ),
          ),
          // Padding(
          //   padding: const EdgeInsets.all(1.0),
          //   child: LinearPercentIndicator(
          //     backgroundColor: theme.getColor.withOpacity(0.5),
          //     animation: true,
          //     lineHeight: 20.0,
          //     animationDuration: 2500,
          //     percent: _inDecimal(),
          //     center: Text(
          //       _inPercent(),
          //       style: const TextStyle(color: Colors.white, fontSize: fontMedium),
          //     ),
          //     barRadius: const Radius.circular(10),
          //     progressColor: _inDecimal() != 1
          //         ? Theme.of(context).primaryColor
          //         : theme.getColor,
          //   ),
          // ),
        ],
      ),
    );
  }

  var myMenuItems = <String>['Edit', 'Delete'];

  double total() {
    return double.parse(lotteryAmount) + double.parse(remainingAmount);
  }

  _inDecimal() {
    var dec = status == "unpaid" ? 0.0 : double.parse(lotteryAmount) / total();
    if (dec > 1) {
      dec = 1;
    }
    if (dec.isNaN) {
      return 0.toDouble();
    }
    return dec;
  }

  String _inPercent() {
    double val = _inDecimal() * 100;
    String p = "%";
    String res = val.toString();
    String result = res.split(".")[0];
    return result + p;
  }

  final formatCurrency = NumberFormat("#,##0.00", "en_US");
}
