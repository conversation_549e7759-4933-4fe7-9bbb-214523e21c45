// ignore_for_file: unused_field

import 'dart:async';

import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../repository/user_repos.dart';
import '../../utils/network.dart';
import '../../utils/validator.dart';
import '../settings/constant.dart';
import '../themes/ThemeProvider.dart';
import '../ui_kits/app_bar.dart';

class ChangePassword extends StatefulWidget {
  static const routeName = "/change_password";
  final ChangePasswordArgs arg;
  const ChangePassword({super.key, required this.arg});

  @override
  State<ChangePassword> createState() => _ChangePasswordState();
}

class _ChangePasswordState extends State<ChangePassword> {
  final _appBar = GlobalKey<FormState>();
  var invisible = false;
  bool isSubmitted = false;
  bool _onProcess = false;
  final _changeFormKey = GlobalKey<FormState>();
  bool isOffline = false;
  late StreamSubscription _connectionChangeStream;
  final password0Control = TextEditingController();
  final password1Control = TextEditingController();
  final password2Control = TextEditingController();
  int _currentThemeIndex = 2;
  late ThemeProvider themeProvider;

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  _loadPreTheme() {
    _currentThemeIndex = themeProvider.getThemeIndex();
  }

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  @override
  void initState() {
    _onProcess = false;
    NetworkManager connectionStatus = NetworkManager.getInstance();
    _connectionChangeStream =
        connectionStatus.connectionChange.listen(connectionChanged);
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _loadPreTheme();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var language = EkubLocalization.of(context)!;
    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: EkubAppBar(key: _appBar, title: '', widgets: const []),
      body: Form(
          key: _changeFormKey,
          autovalidateMode: isSubmitted
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.disabled,
          child: Stack(
            children: <Widget>[
              Align(
                alignment: Alignment.bottomCenter,
                child: ListView(
                  children: <Widget>[
                    Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          //border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(10)),
                      margin: const EdgeInsets.fromLTRB(25, 30, 25, 10),
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
                      child: Column(
                        children: <Widget>[
                          const SizedBox(
                            height: 15,
                          ),
                          Text(
                            language.translate("change_password"),
                            style: const TextStyle(
                              fontSize: fontBig,
                              fontWeight: boldFont,
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 80, vertical: 0),
                            child: const Image(
                              image: AssetImage(
                                "assets/icons/key.png",
                              ),
                              // height: 240,
                              fit: BoxFit.cover,
                              // height: 200,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Text(
                                    language.translate("old_password"),
                                    style: TextStyle(
                                        color: themeProvider.getColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: fontMedium),
                                  ),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                TextFormField(
                                  style: lableStyle,
                                  controller: password0Control,
                                  obscureText: invisible,
                                  decoration: InputDecoration(
                                    prefixIcon: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 3, vertical: 3),
                                      height: 10,
                                      width: 10,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.backgroundColor,
                                          shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.lock_outline,
                                        size: 20,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: BorderSide(
                                          color: Colors.grey.shade400),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: BorderSide(
                                          color: themeProvider.getColor),
                                    ),
                                    fillColor: Colors.white,
                                    errorBorder: OutlineInputBorder(
                                      borderSide: const BorderSide(
                                        color: Colors.red,
                                      ),
                                      borderRadius: BorderRadius.circular(18),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: const BorderSide(
                                          color: Colors.red, width: 2),
                                    ),
                                    filled: true,
                                    hintText: '******',
                                    hintStyle: hintStyle,
                                    suffix: GestureDetector(
                                      onTap: changeSate,
                                      child: Icon(
                                        invisible
                                            ? Icons.visibility
                                            : Icons.visibility_off,
                                        color: themeProvider.getColor,
                                        size: 15,
                                      ),
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return EkubLocalization.of(context)!
                                          .translate("password_empty");
                                    } else if (value.length < 6) {
                                      return EkubLocalization.of(context)!
                                          .translate("password_length");
                                    } else if (value.length > 25) {
                                      return EkubLocalization.of(context)!
                                          .translate("password_limit");
                                    } else {
                                      return null;
                                    }
                                  },
                                  onSaved: (value) {},
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Text(
                                    language.translate("new_password"),
                                    style: TextStyle(
                                        color: themeProvider.getColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: fontMedium),
                                  ),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                TextFormField(
                                  style: lableStyle,
                                  controller: password1Control,
                                  obscureText: invisible,
                                  decoration: InputDecoration(
                                    prefixIcon: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 3, vertical: 3),
                                      height: 10,
                                      width: 10,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.backgroundColor,
                                          shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.lock_outline,
                                        size: 20,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: BorderSide(
                                          color: Colors.grey.shade400),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: BorderSide(
                                          color: themeProvider.getColor),
                                    ),
                                    fillColor: Colors.white,
                                    errorBorder: OutlineInputBorder(
                                      borderSide: const BorderSide(
                                        color: Colors.red,
                                      ),
                                      borderRadius: BorderRadius.circular(18),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: const BorderSide(
                                          color: Colors.red, width: 2),
                                    ),
                                    filled: true,
                                    hintText: '******',
                                    hintStyle: hintStyle,
                                    suffix: GestureDetector(
                                      onTap: changeSate,
                                      //call this method when contact with screen is removed
                                      child: Icon(
                                        invisible
                                            ? Icons.visibility
                                            : Icons.visibility_off,
                                        color: themeProvider.getColor,
                                        size: 15,
                                      ),
                                    ),
                                  ),
                                  validator: (value) => Sanitizer()
                                      .isPasswordValid(value!, context),
                                  onSaved: (value) {},
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Text(
                                    language.translate("confirm_password"),
                                    style: TextStyle(
                                        color: themeProvider.getColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: fontMedium),
                                  ),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                TextFormField(
                                  style: lableStyle,
                                  controller: password2Control,
                                  obscureText: invisible,
                                  decoration: InputDecoration(
                                    prefixIcon: Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 3, vertical: 3),
                                      height: 10,
                                      width: 10,
                                      decoration: BoxDecoration(
                                          color: ColorProvider.backgroundColor,
                                          shape: BoxShape.circle),
                                      child: const Icon(
                                        Icons.lock_outline,
                                        size: 20,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: BorderSide(
                                          color: Colors.grey.shade400),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: BorderSide(
                                          color: themeProvider.getColor),
                                    ),
                                    fillColor: Colors.white,
                                    filled: true,
                                    errorBorder: OutlineInputBorder(
                                      borderSide: const BorderSide(
                                        color: Colors.red,
                                      ),
                                      borderRadius: BorderRadius.circular(18),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(18),
                                      borderSide: const BorderSide(
                                          color: Colors.red, width: 2),
                                    ),
                                    hintText: '******',
                                    hintStyle: hintStyle,
                                    suffix: GestureDetector(
                                      onTap: changeSate,
                                      //call this method when contact with screen is removed
                                      child: Icon(
                                        invisible
                                            ? Icons.visibility
                                            : Icons.visibility_off,
                                        color: themeProvider.getColor,
                                        size: 15,
                                      ),
                                    ),
                                  ),
                                  validator: (value) => Sanitizer()
                                      .isPasswordMatch(password1Control.text,
                                          value!, context),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                top: 30, left: 8, right: 8),
                            child: GestureDetector(
                              onTap: _onProcess
                                  ? null
                                  : () async {
                                      final form = _changeFormKey.currentState;
                                      setState(() {
                                        isSubmitted = true;
                                      });
                                      if (form!.validate()) {
                                        setState(() {
                                          _onProcess = true;
                                        });
                                        form.save();
                                        if (!await InternetConnectivity()
                                            .checkInternetConnectivty(
                                                context, true)) {
                                          setState(() {
                                            _onProcess = false;
                                          });
                                          return;
                                        } else {
                                          prepareRequest(password0Control.text,
                                              password1Control.text);
                                        }
                                      }
                                    },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: themeProvider.getColor,
                                ),
                                height: 50,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Spacer(),
                                    Text(language.translate("change"),
                                        style: buttonText),
                                    const Spacer(),
                                    Align(
                                      widthFactor: 2,
                                      alignment: Alignment.centerRight,
                                      child: _onProcess
                                          ? const Padding(
                                              padding: EdgeInsets.all(8.0),
                                              child: SizedBox(
                                                height: 20,
                                                width: 20,
                                                child:
                                                    CircularProgressIndicator(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            )
                                          : Container(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          )),
    );
  }

  void prepareRequest(String oldPassword, String newPassword) {
    var sender = AuthDataProvider(httpClient: http.Client());
    setState(() {
      _onProcess = true;
    });
    var res = sender.changePassword(context, oldPassword, newPassword);
    res
        .then((value) => {
              setState(() {
                _onProcess = false;
              }),
              if (value.success)
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value.message,
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    imagePath: successDialogIcon,
                    onTapDismiss: () {
                      Navigator.pop(context);

                      widget.arg.fromDrawer
                          ? _openHomeScreen(
                              widget.arg.role == "admin", widget.arg.role!)
                          : Navigator.pop(context);
                    },
                    panaraDialogType: PanaraDialogType.success,
                  )
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value.message,
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                  }),
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          prepareRequest(oldPassword, newPassword);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        _onProcess = false;
      });
      return {};
    });
  }

  _openHomeScreen(bool isAdmin, String role) async {
    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);

    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }
}
