import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebViewScreen extends StatefulWidget {
  final String url;

  WebViewScreen({required this.url});

  @override
  _WebViewScreenState createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late InAppWebViewController _webViewController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    print('WebViewScreen initialized with URL: ${widget.url}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Payment Gateway'),
      ),
      body: Stack(
        children: [
          InAppWebView(
            initialUrlRequest: URLRequest(url: WebUri(widget.url)),
            initialOptions: InAppWebViewGroupOptions(
              crossPlatform: InAppWebViewOptions(
                javaScriptEnabled: true,
                cacheEnabled: false,
              ),
            ),
            onWebViewCreated: (controller) {
              _webViewController = controller;
              print('WebView created');
            },
            onLoadStart: (controller, url) {
              print('Page started loading: $url');
              setState(() {
                _isLoading = true;
              });
            },
            onLoadStop: (controller, url) async {
              print('Page finished loading: $url');
              setState(() {
                _isLoading = false;
              });
              final currentUrl = await _webViewController.getUrl();
              print('Current URL in WebView: $currentUrl');
            },
            onLoadError: (controller, url, code, message) {
              print('Failed to load page: $url, Error: $message');
              setState(() {
                _isLoading = false;
              });
            },
          ),
          if (_isLoading)
            Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
