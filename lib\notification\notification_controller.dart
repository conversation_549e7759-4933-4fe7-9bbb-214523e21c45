// // ignore_for_file: prefer_final_fields, empty_catches

// import 'package:awesome_notifications/awesome_notifications.dart';
// import 'package:awesome_notifications_fcm/awesome_notifications_fcm.dart';
// import 'package:flutter/material.dart';

// class NotificationController extends ChangeNotifier {
//   /// *********************************************
//   ///   SINGLETON PATTERN
//   /// *********************************************
//   static final NotificationController _instance =
//       NotificationController._internal();
//   factory NotificationController() {
//     return _instance;
//   }
//   NotificationController._internal();

//   /// *********************************************
//   ///  OBSERVER PATTERN
//   /// *********************************************
//   String _firebaseToken = '';
//   String get firebaseToken => _firebaseToken;
//   String _nativeToken = '';
//   String get nativeToken => _nativeToken;
//   ReceivedAction? initialAction;

//   /// *********************************************
//   ///   INITIALIZATION METHODS
//   /// *********************************************
//   static Future<void> initializeLocalNotifications(
//       {required bool debug}) async {
//     await AwesomeNotifications().initialize(
//       // 'resource://drawable/logo',
//       null,
//       [
//         NotificationChannel(
//           channelKey: 'kabba',
//           channelName: 'Kabba',
//           channelDescription: 'Kabba notification',
//           playSound: true,
//           importance: NotificationImportance.High,
//           defaultPrivacy: NotificationPrivacy.Private,
//           defaultColor: Colors.deepPurple,
//           ledColor: Colors.deepPurple,
//         )
//       ],
//       debug: debug,
//     );
//     // Get initial notification action is optional
//     _instance.initialAction =
//         await AwesomeNotifications().getInitialNotificationAction(
//       removeFromActionEvents: false,
//     );
//   }

 
//   static Future<void> startListeningNotificationEvents() async {
//     AwesomeNotifications().setListeners(
//         onActionReceivedMethod: onActionReceivedMethod,
//         onNotificationDisplayedMethod: (ReceivedNotification not) async {});
//   }

//   ///  *********************************************
//   ///     LOCAL NOTIFICATION EVENTS
//   ///  *********************************************
//   static Future<void> getInitialNotificationAction() async {
//     ReceivedAction? receivedAction = await AwesomeNotifications()
//         .getInitialNotificationAction(removeFromActionEvents: true);
//     if (receivedAction == null) return;
//   }

//   @pragma('vm:entry-point')
//   static Future<void> onActionReceivedMethod(
//       ReceivedAction receivedAction) async {
    
//   }

//   ///  *********************************************
//   ///     REMOTE NOTIFICATION EVENTS
//   ///  *********************************************
//   /// Use this method to execute on background when a silent data arrives
//   /// (even while terminated)
//   @pragma("vm:entry-point")
//   static Future<void> mySilentDataHandle(FcmSilentData silentData) async {}

//   /// Use this method to detect when a new fcm token is received
//   @pragma("vm:entry-point")
//   static Future<void> myFcmTokenHandle(String token) async {}

//   /// Use this method to detect when a new native token is received
//   @pragma("vm:entry-point")
//   static Future<void> myNativeTokenHandle(String token) async {}
//   static Future<bool> displayNotificationRationale() async {
//     return await AwesomeNotifications().requestPermissionToSendNotifications();
//   }

//   static Future<void> resetBadge() async {
//     await AwesomeNotifications().resetGlobalBadge();
//   }

//   ///  *********************************************
//   ///     REMOTE TOKEN REQUESTS
//   ///  *********************************************
//   static Future<String> requestFirebaseToken() async {
//     if (await AwesomeNotificationsFcm().isFirebaseAvailable) {
//       try {
//         return await AwesomeNotificationsFcm().requestFirebaseAppToken();
//       } catch (exception) {}
//     } else {}
//     return '';
//   }
// }
