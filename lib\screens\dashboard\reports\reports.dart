import 'package:flutter/material.dart';

import '../../ui_kits/app_bar.dart';

class ReportScreen extends StatefulWidget {
  static const routeName = "/reports";
  const ReportScreen({super.key});

  @override
  State<ReportScreen> createState() => _EditCollectorState();
}

class _EditCollectorState extends State<ReportScreen> {
  final _appBar = GlobalKey<FormState>();

  _toastPassed() {
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: EkubAppBar(key: _appBar, title: "Reports", widgets: [
       
        IconButton(onPressed: _toastPassed, icon: const Icon(Icons.add))
      ]),
      //appBar: _buildBar(context),
      body: Container(),
    );
  }
}
