import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/language.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class LanguageTestScreen extends StatelessWidget {
  const LanguageTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final appLanguage = Provider.of<AppLanguage>(context);
    final ekubLocalization = EkubLocalization.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Language Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.language),
            onPressed: () {
              Navigator.pushNamed(context, '/language_selection');
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Locale Information',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('AppLanguage Locale: ${appLanguage.appLocale}'),
                    Text('Device Locale: ${Localizations.localeOf(context)}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Translations from EkubLocalization',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    _buildTranslationRow('title', ekubLocalization),
                    _buildTranslationRow('home', ekubLocalization),
                    _buildTranslationRow('login', ekubLocalization),
                    _buildTranslationRow('register', ekubLocalization),
                    _buildTranslationRow('payment', ekubLocalization),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            const SizedBox(height: 16),
            // Language selection buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () =>
                      appLanguage.changeLanguage(const Locale('en', 'US')),
                  child: const Text('English'),
                ),
                ElevatedButton(
                  onPressed: () =>
                      appLanguage.changeLanguage(const Locale('am', 'ET')),
                  child: const Text('አማርኛ'),
                ),
                ElevatedButton(
                  onPressed: () =>
                      appLanguage.changeLanguage(const Locale('es')),
                  child: const Text('ትግሪኛ'),
                ),
                ElevatedButton(
                  onPressed: () =>
                      appLanguage.changeLanguage(const Locale('fr')),
                  child: const Text('Oromiffa'),
                ),
                ElevatedButton(
                  onPressed: () =>
                      appLanguage.changeLanguage(const Locale('tl')),
                  child: const Text('Somali'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTranslationRow(String key, EkubLocalization? localization) {
    if (localization == null) {
      return Text('$key: Localization not available');
    }
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text('$key:',
                style: const TextStyle(fontWeight: FontWeight.bold)),
          ),
          Expanded(
            flex: 3,
            child: Text(localization.translate(key)),
          ),
        ],
      ),
    );
  }
}
