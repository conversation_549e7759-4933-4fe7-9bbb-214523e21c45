// ignore_for_file: empty_catches, must_be_immutable, use_build_context_synchronously

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:ekub/exports/models.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/screens/account/change_password.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/screens/ui_kits/list_view.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/tools.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../../repository/member_repos.dart';
import '../../../routs/shared.dart';
import '../../../utils/colors.dart';
import '../../../utils/device.dart';
import '../../settings/profile.dart';
import '../../themes/ThemeProvider.dart';
import '../../ui_kits/loading_indicator.dart';
import '../../ui_kits/no_internet_connection_found.dart';
import 'actions/add_member.dart';
import 'member_equbs.dart';

class MembersScreen extends StatefulWidget {
  static const routeName = '/member_screen';
  MemberEqubsArgs? args;
  MembersScreen({super.key, this.args});

  @override
  State<MembersScreen> createState() => _MembersScreenState();
}

class _MembersScreenState extends State<MembersScreen> {
  int page = 1;
  int offset = 0;
  var proLoaded = false;
  bool isConnected = true;
  bool isLoading = true;
  bool _isMessageLoading = false;
  bool hasNextPage = true;
  bool _isLoadMoreRunning = false;
  bool _isSearching = false;
  List<Member> serchList = [];
  List<Member> members = [];
  List<Member> item = [];
  int totalMembers = 0;
  String searchMessage = "";
  var myMenuItems = <String>["Edit", "Add Equb", "Delete"];
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      gender: "loading",
      email: "loading",
      role: "loading",
      memberId: "loading");
  late ThemeProvider themeProvider;
  late ScrollController _controller;
  final _appBar = GlobalKey<FormState>();
  TextEditingController searchControl = TextEditingController();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _controller = ScrollController()..addListener(_loadMore);
    _loadProfile();
    loadMembers();
    super.initState();
  }

  void _loadMore() async {
    if (hasNextPage == true &&
        _controller.offset >= _controller.position.maxScrollExtent &&
        !_controller.position.outOfRange &&
        _isMessageLoading == false &&
        _isLoadMoreRunning == false) {
      try {
        setState(() {
          _isLoadMoreRunning = true;
        });
        page += 1;
        offset += 10;

        await Provider.of<MemberProvider>(context, listen: false)
            .loadMoreMembers(context, page, offset, true);

        var loadedMembers = Provider.of<MemberProvider>(context, listen: false)
            .member["members"];
        print("Loaded Members: $loadedMembers");

        setState(() {
          _isMessageLoading = false;
          _isLoadMoreRunning = false;
        });
      } catch (e) {
        if (e is TimeoutException) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("time_out"),
              message: EkubLocalization.of(context)!.translate("timeout_msg"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            Navigator.pop(context);
            _loadMore();
          }, onTapCancel: () {
            setState(() {
              isLoading = false;
            });
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        } else {
          // PanaraConfirmDialog.show(context,
          //     title: "Error",
          //     message: "U",
          //     confirmButtonText: "Try Again",
          //     cancelButtonText: "Cancel", onTapConfirm: () {
          //   _loadMore();
          //   Navigator.pop(context);
          // }, onTapCancel: () {
          //   Navigator.pop(context);
          // }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  _openProfile() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: ((context) => const ProfilePage(member: null))));
  }

  _addMember() {
    Navigator.pushNamed(context, AddMember.routeName,
        arguments: AddMemberArgs(forEdit: false));
  }

  _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());

    try {
      await auth.getUserData().then((value) => {
            setState(() {
              user = value;
              proLoaded = true;
            })
          });
    } catch (e) {}
  }

  void loadMembers() async {
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        setState(() {
          isConnected = false;
          isLoading = false;
        });
        return;
      }
      setState(() {
        isLoading = true;
        page = 1;
        offset = 0;
      });

      await Provider.of<MemberProvider>(context, listen: false)
          .loadMoreMembers(context, page, offset, false);
      setState(() {
        isLoading = false;
        isConnected = true;
      });
    } catch (e) {
      if (e is TimeoutException) {
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("time_out"),
            message: EkubLocalization.of(context)!.translate("timeout_message"),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          loadMembers();
        }, onTapCancel: () {
          setState(() {
            isLoading = false;
          });
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("error"),
              message: e.toString(),
              // EkubLocalization.of(context)!.translate("unable_load_member"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            loadMembers();
            Navigator.pop(context);
          }, onTapCancel: () {
            setState(() {
              isLoading = false;
            });
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var mprovider = Provider.of<MemberProvider>(context, listen: true);
    members =
        Provider.of<MemberProvider>(context, listen: true).member["members"];
    item = Provider.of<MemberProvider>(context, listen: true).searchResult;

    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: AppBar(
          iconTheme: IconThemeData(color: themeProvider.getColor),
          backgroundColor: Colors.white,
          elevation: 0,
          key: _appBar,
          title: Text(
            toCamelCase(user.fullName ?? ""),
            style: TextStyle(
                color: themeProvider.getColor,
                fontSize: fontMedium,
                fontWeight: FontWeight.bold),
          ),
          actions: user.role == "admin"
              ? [
                  IconButton(
                      onPressed: () {
                        loadMembers();
                      },
                      icon: Icon(
                        Icons.refresh,
                        color: themeProvider.getColor,
                      )),
                ]
              : [
                  IconButton(
                      onPressed: _openProfile,
                      icon: const Icon(Icons.person_outline)),
                  IconButton(
                      onPressed: () {
                        loadMembers();
                      },
                      icon: const Icon(Icons.refresh)),
                ]),
      body: isConnected
          ? !isLoading
              ? members.isNotEmpty
                  ? RefreshIndicator(
                      onRefresh: () async {
                        await Future.delayed(const Duration(seconds: 2));
                        loadMembers();
                      },
                      key: _refreshIndicatorKey,
                      child: SizedBox(
                        height: Device.body(context),
                        child: listHolder(_isSearching ? serchList : members,
                            themeProvider.getColor, mprovider),
                      ),
                    )
                  : Container(
                      margin: const EdgeInsets.symmetric(vertical: 0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            alignment: Alignment.center,
                            child: const Image(
                              height: 230,
                              image: AssetImage(
                                "assets/icons/members.png",
                              ),
                              fit: BoxFit.cover,
                            ),
                          ),
                          Center(
                              child: Text(
                            EkubLocalization.of(context)!
                                .translate("no_member_found"),
                            style: TextStyle(
                                color: bodyTextColor,
                                fontWeight: normalFontWeight),
                          )),
                          const SizedBox(
                            height: 10,
                          ),
                          ElevatedButton(
                              onPressed: _addMember,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20.0, vertical: 10),
                                child: Text(
                                    EkubLocalization.of(context)!
                                        .translate("add_member"),
                                    style: buttonText),
                              )),
                        ],
                      ),
                    )
              : searchLoading()
          : NoConnectionWidget(
              fun: loadMembers,
              isLoading: isLoading,
            ),
      drawer: user.role == "admin" ? _drawer(context) : null,
      floatingActionButton: members.isNotEmpty && !_isSearching
          ? InkWell(
              onTap: _addMember,
              // ignore: sort_child_properties_last
              child: Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: themeProvider.getColor,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.add,
                  size: 40,
                  color: themeProvider.getLightColor,
                ),
              ))
          : Container(),
    );
  }

  Drawer _drawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: const EdgeInsets.only(top: 30),
        children: [
          Padding(
            padding: const EdgeInsets.only(
                top: 30.0, left: 20, bottom: 25, right: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CircleAvatar(
                  backgroundColor: themeProvider.getLightColor,
                  radius: 30,
                  child: Text(
                    user.fullName!.substring(0, 1).toUpperCase(),
                    style: const TextStyle(fontSize: 30.0, color: Colors.white),
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        toCamelCase(proLoaded ? user.fullName! : ""),
                        style: TextStyle(
                            color: themeProvider.getColor,
                            fontWeight: FontWeight.bold,
                            fontSize: fontMedium),
                      ),
                      Text(
                        proLoaded ? user.email ?? "" : "",
                        style: TextStyle(
                            color: Colors.grey.shade500,
                            fontWeight: FontWeight.bold,
                            fontSize: fontMedium),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Divider(
            color: Colors.grey.shade400,
          ),
          const SizedBox(
            height: 15,
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.home_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("home"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pushReplacementNamed(context, HomeScreen.routeName,
                  arguments: HomeScreenArgs(
                      isAdmin: user.role == "admin",
                      isOnline: true,
                      role: user.role!));
              // Navigator.pop(context);
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.group_add_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("add_member"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) =>
                        AddMember(args: AddMemberArgs(forEdit: false))),
              );
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.person_outline,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("my_profile"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pop(context);
              _openProfile();
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.lock_outline,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("change_password"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pushNamed(context, ChangePassword.routeName,
                  arguments: ChangePasswordArgs(
                      isOnline: true, role: user.role, fromDrawer: true));
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.logout,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("logout"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              PanaraConfirmDialog.show(
                context,
                title: EkubLocalization.of(context)!.translate("warning"),
                message:
                    EkubLocalization.of(context)!.translate("confirm_logout"),
                confirmButtonText:
                    EkubLocalization.of(context)!.translate("confirm"),
                cancelButtonText:
                    EkubLocalization.of(context)!.translate("cancel"),
                onTapCancel: () {
                  Navigator.pop(context);
                },
                onTapConfirm: () {
                  gotoSignIn(context);
                },
                imagePath: warningDialogIcon,

                panaraDialogType: PanaraDialogType.warning,
                barrierDismissible:
                    false, // optional parameter (default is true)
              );
            },
          ),
        ],
      ),
    );
  }

  Widget listHolder(items, theme, mprovider) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        height: 56,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 0.5,
              blurRadius: 4,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: TextFormField(
          controller: searchControl,
          onTap: () {
            setState(() {
              _isSearching = true;
            });
          },

          decoration: InputDecoration(
              prefixIcon: const Icon(Icons.search, color: Colors.black),
              suffixIcon: IconButton(
                  onPressed: () {
                    setState(() {
                      searchControl.clear();
                      _isSearching = !_isSearching;
                    });
                  },
                  icon: Icon(
                    _isSearching ? Icons.cancel : null,
                    color: themeProvider.getColor,
                  )),
              focusedBorder: InputBorder.none,
              border: InputBorder.none,
              hintText: EkubLocalization.of(context)!.translate("enter_name"),
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: fontSmall,
              )),
          autofocus: false,
          style: TextStyle(
              fontSize: fontMedium,
              letterSpacing: 0.5,
              color: themeProvider.getColor),
          //when search text changes then updated search list
          onEditingComplete: () {
            _isSearching = true;
            setState(() {});

            serchList.clear();
            mprovider.searchMember(context, 1, 0, searchControl.text);
            for (var member in item) {
              setState(() {
                serchList.add(member);
              });
            }
          },
          onChanged: (val) {
            _isSearching = true;
            setState(() {});
            serchList.clear();
            mprovider.searchMember(context, 1, 0, val);
            if (item.isNotEmpty) {
              for (var member in item) {
                setState(() {
                  serchList.add(member);
                });
              }
            } else {
              searchMessage =
                  EkubLocalization.of(context)!.translate("no_record");
            }
          },
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(top: 5.0, left: 15, bottom: 5),
        child: Text(EkubLocalization.of(context)!.translate("members"),
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20)),
      ),
      _isSearching && serchList.isEmpty
          ? Expanded(
              // margin: const EdgeInsets.symmetric(vertical: 0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Center(
                      child: Text(
                    searchMessage,
                    style: TextStyle(
                        color: bodyTextColor, fontWeight: normalFontWeight),
                  )),
                ],
              ),
            )
          : Expanded(
              child: ListView.builder(
                  controller: _controller,
                  itemCount: items.length,
                  padding: const EdgeInsets.all(0.0),
                  itemBuilder: (context, item) {
                    return _buildListItems(context, items[item], item, theme);
                  }),
            ),
      if (_isLoadMoreRunning == true)
        Padding(
          padding: const EdgeInsets.only(top: 10, bottom: 10),
          child: Center(
            child: CircularProgressIndicator(
              color: themeProvider.getColor,
              strokeWidth: 1,
            ),
          ),
        ),
    ]);
  }

  Widget _buildListItems(BuildContext context, Member member, int item, theme) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(context, MemberEqubs.routeName,
            arguments: MemberEqubsArgs(
              member: member,
              isOnline: true,
              isAdmin: user.role == "admin",
            ));
      },
      child: _listUi(theme, member),
    );
  }

  _listUi(Color theme, Member member) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, right: 8.0),
      child: MemberCard(
        isAdmin: user.role == "admin",
        member: member,
        title: member.fullName ?? "loading...",
        phone: member.phone ?? "loading...",
        status: member.status ?? "loading...",
        icon: Icons.person,
        theme: theme,
      ),
    );
  }
}
