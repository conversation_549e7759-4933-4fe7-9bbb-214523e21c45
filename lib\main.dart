// ignore_for_file: no_logic_in_create_state, deprecated_member_use, must_be_immutable

//import 'package:ekub/models/equb_type_model.dart';
import 'package:ekub/repository/dashboard_repos.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/equb_repos.dart';
import 'package:ekub/repository/language.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/screens/dashboard/ekubs/equb_provider.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_update/in_app_update.dart';
import 'package:provider/provider.dart';
import 'package:upgrader/upgrader.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_in_app_messaging/firebase_in_app_messaging.dart';

import 'routs/router.dart';
import 'screens/themes/ThemeProvider.dart';
import 'utils/network.dart';
import 'firebase_options.dart';
import 'utils/js_payment_executor.dart';

Future<void> main() async {
  // This ensures that plugin messages are buffered until a listener is registered
  // Helps prevent the "message discarded" warnings
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase Core
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Clear upgrader settings
  await Upgrader.clearSavedSettings();
  checkForUpdate();

  // Initialize network manager
  NetworkManager connectionStatus = NetworkManager.getInstance();
  connectionStatus.initialize();

  // Initialize Firebase services with platform checks
  await _initializeFirebaseServices();

  // Small delay to ensure all initialization is complete
  await Future.delayed(const Duration(milliseconds: 100));
}

/// Initialize Firebase services with platform-specific checks
Future<void> _initializeFirebaseServices() async {
  try {
    // Initialize Firebase Messaging
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    // Request permission for iOS
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    print('User granted permission: ${settings.authorizationStatus}');

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Initialize Firebase In-App Messaging (only for mobile platforms)
    if (!kIsWeb) {
      // Skip for web platform to avoid MissingPluginException
      try {
        FirebaseInAppMessaging.instance.setMessagesSuppressed(false);
      } catch (e) {
        print('Error initializing Firebase In-App Messaging: $e');
      }
    }
  } catch (e) {
    print('Error initializing Firebase services: $e');
  }
}

// Background message handler
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print('Handling a background message: ${message.messageId}');
}

String updateTrace = 'home';

Future<void> checkForUpdate() async {
  updateTrace = 'init';
  InAppUpdate.checkForUpdate().then((updateInfo) {
    updateTrace = 'updateInfo returned';
    if (updateInfo.immediateUpdateAllowed) {
      updateTrace = 'immediateUpdateAllowed';
      // Perform immediate update
      InAppUpdate.performImmediateUpdate().then((appUpdateResult) {
        if (appUpdateResult == AppUpdateResult.success) {
          updateTrace = 'immediateUpdate AppUpdateResult success';
          //App Update successful
          prepareEnv();
        } else {
          updateTrace = 'immediateUpdate AppUpdateResult failed';
          prepareEnv();
        }
      });
    } else if (updateInfo.flexibleUpdateAllowed) {
      updateTrace = 'flexibleUpdateAllowed';
      //Perform flexible update
      InAppUpdate.startFlexibleUpdate().then((appUpdateResult) {
        if (appUpdateResult == AppUpdateResult.success) {
          updateTrace = 'flexibleUpdate AppUpdateResult success';
          //App Update successful
          InAppUpdate.completeFlexibleUpdate();
          prepareEnv();
        } else {
          updateTrace = 'flexibleUpdate AppUpdateResult failed';
          prepareEnv();
        }
      });
    } else {
      updateTrace = 'update info not found';
      prepareEnv();
    }
  }).onError((error, stackTrace) {
    updateTrace = 'error: $error';
    prepareEnv();
  });
  updateTrace = 'in app update at edge';
}

Future<void> prepareEnv() async {
  const secureStorage = FlutterSecureStorage();
  AppLanguage appLanguage = AppLanguage();

  // Initialize the app language
  await appLanguage.fetchLocale();

  String? theme = await secureStorage.read(key: "theme");

  runApp(MultiProvider(
    providers: [
      ChangeNotifierProvider(create: (context) => EqubProvider()),
      //  ChangeNotifierProvider(create: (context) => EqubTypeModel()),
      ChangeNotifierProvider(
          create: (context) => ThemeProvider(theme: int.parse(theme ?? "0"))),
      ChangeNotifierProvider.value(value: appLanguage),
    ],
    child: const MyApp(),
  ));
}

class MyApp extends StatefulWidget {
  // RequestDao dao;
  // final AppLanguage appLanguage;
  // const MyApp({super.key, required this.appLanguage});
  const MyApp({super.key}); // Modified constructor
  @override
  // State<MyApp> createState() => _MyAppState(appLanguage);
  State<MyApp> createState() => _MyAppState(); // Modified createState
}

class _MyAppState extends State<MyApp> {
  // RequestDao dao;
  // final AppLanguage appLanguage;
  // _MyAppState(this.appLanguage);
  _MyAppState(); // Modified constructor

  @override
  void initState() {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.white,
      statusBarIconBrightness: Brightness.light,
    ));
    super.initState();

    // Add debug dialog with a small delay
    // Future.delayed(const Duration(milliseconds: 500), () {
    //   if (mounted) {
    //     showDialog(
    //       context: context,
    //       barrierDismissible: false, // User must tap button to dismiss
    //       builder: (context) => AlertDialog(
    //         title: const Text('Debug Info'),
    //         content: Column(
    //           mainAxisSize: MainAxisSize.min,
    //           crossAxisAlignment: CrossAxisAlignment.start,
    //           children: [
    //             Text('EasyLocalization Locale: ${EasyLocalization.of(context)?.locale}'),
    //             Text('Current Context Locale: ${Localizations.localeOf(context)}'),
    //           ],
    //         ),
    //         actions: [
    //           TextButton(
    //             onPressed: () => Navigator.of(context).pop(),
    //             child: const Text('OK'),
    //           ),
    //         ],
    //       ),
    //     );
    //   }
    // });

    // Listen for foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Received a message while in the foreground: ${message.messageId}');
      if (message.notification != null && mounted) {
        // Display a dialog or snackbar
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(message.notification!.title ?? 'Notification'),
            content:
                Text(message.notification!.body ?? 'You have a new message.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    });
  }
  // This widget is the root of your application.

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

    // Use the locale from AppLanguage provider
    final appLanguage = Provider.of<AppLanguage>(context);
    final locale = appLanguage.appLocale;

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => DashboardProvider(httpClient: http.Client()),
        ),
        ChangeNotifierProvider(
          create: (_) => MemberProvider(httpClient: http.Client()),
        ),
        ChangeNotifierProvider(
          create: (_) => EqubDataProvider(httpClient: http.Client()),
        ),
      ],
      child: Consumer<ThemeProvider>(builder: (context, themeProvider, child) {
        return UpgradeAlert(
          upgrader: Upgrader(),
          child: MaterialApp(
            navigatorKey: JSPaymentExecutor.navigatorKey,
            locale: locale, // Use selected language
            debugShowCheckedModeBanner: false,
            localizationsDelegates: const [
              EkubLocalization.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', 'US'),
              Locale('am', 'ET'), // Amharic
              Locale('es'), // Tigrinya
              Locale('fr'), // Oromo
              Locale('tl'), // Somali
            ],
            title: "Virtual Equb",
            theme: ThemeData(
                appBarTheme: const AppBarTheme(
                  backgroundColor:
                      Colors.white, // Set the app bar background color
                ),
                textSelectionTheme: const TextSelectionThemeData(
                  cursorColor:
                      Colors.green, // Change this to your desired color
                ),
                bottomNavigationBarTheme: const BottomNavigationBarThemeData(
                    backgroundColor: Colors.white
                    // Set the navigation bar background color
                    ),
                scaffoldBackgroundColor: Colors.white,
                inputDecorationTheme: InputDecorationTheme(
                    suffixIconColor: themeProvider.getColor,
                    prefixIconColor: themeProvider.getColor,
                    focusedBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: themeProvider.getColor, width: 2.0),
                    ),
                    focusColor: themeProvider.getColor),
                floatingActionButtonTheme: FloatingActionButtonThemeData(
                    backgroundColor: Colors.white,
                    sizeConstraints:
                        const BoxConstraints(minWidth: 80, minHeight: 80),
                    extendedPadding: const EdgeInsets.all(50),
                    foregroundColor: themeProvider.getColor,
                    extendedTextStyle: const TextStyle(
                        color: Colors.white,
                        fontSize: 26,
                        fontWeight: FontWeight.w300)),

                //F48221

                // backgroundColor: ColorProvider.backgroundColor,
                primaryColor: themeProvider.getColor,
                textTheme:
                    GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme),
                iconTheme: const IconThemeData(
                  color: Colors.white,
                ),
                textButtonTheme: TextButtonThemeData(
                    style: ButtonStyle(
                  foregroundColor: MaterialStateProperty.all<Color>(Colors.red),
                  textStyle: MaterialStateProperty.all<TextStyle>(
                      const TextStyle(color: Colors.black)),
                )),
                elevatedButtonTheme: ElevatedButtonThemeData(
                  style: ButtonStyle(
                      textStyle: MaterialStateProperty.all<TextStyle>(
                          const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.normal,
                              fontSize: 20)),
                      backgroundColor: MaterialStateProperty.all<Color>(
                          themeProvider.getColor),
                      foregroundColor:
                          MaterialStateProperty.all<Color>(Colors.white),
                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ))),
                ),
                colorScheme: ColorScheme.fromSwatch(
                  primarySwatch: const MaterialColor(
                    0xFFFFFFFF, // The primary color value in hexadecimal (white in this case)
                    <int, Color>{
                      50: Color(
                          0xFFFFFFFF), // Shades of white for lighter variations
                      100: Color(0xFFFFFFFF),
                      200: Color(0xFFFFFFFF),
                      300: Color(0xFFFFFFFF),
                      400: Color(0xFFFFFFFF),
                      500: Color(0xFFFFFFFF), // The default primary color
                      600: Color(0xFFFFFFFF),
                      700: Color(0xFFFFFFFF),
                      800: Color(0xFFFFFFFF),
                      900: Color(
                          0xFFFFFFFF), // Shades of white for darker variations
                    },
                  ),
                ).copyWith(secondary: Colors.grey.shade600)),
            onGenerateRoute: AppRoute
                .generateRoute, // This trailing comma makes auto-formatting nicer for build methods.
          ),
        );
      }),
    );
  }
}
