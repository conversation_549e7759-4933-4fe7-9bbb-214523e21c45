import 'package:ekub/repository/ekub_localization.dart';

class DataValidator {
  String validateEmail(String email) {
    // 1
    RegExp regex = RegExp(r'\w+@\w+\.\w+'); // <NAME_EMAIL>
    // 2
    if (email.isEmpty) {
      return 'Please Enter Your Email';
    } else if (!regex.hasMatch(email)) {
      return "Please Enter Valid Email Address";
    } else {
      return "0";
    }
  }
}

class Sanitizer {
  bool isNameValid(String name) {
    // Regex pattern to match only alphabetic characters
    final RegExp regex = RegExp(r'^[a-zA-Z\s]+$');

    // Check if the name matches the pattern
    return regex.hasMatch(name);
  }

  bool isFullName(String name) {
    // Split the name into parts by whitespace
    List<String> parts = name.trim().split(' ');

    // Check if there are at least two parts
    if (parts.length < 2) {
      return false;
    }

    // Check if each part contains alphabetic characters
    for (String part in parts) {
      if (!RegExp(r'^[a-zA-Z]+$').hasMatch(part)) {
        return false;
      }
    }

    return true;
  }

  String? isFullNameValid(String name, bool isAdmin, context) {
    if (name.isEmpty) {
      return isAdmin
          ? EkubLocalization.of(context)!.translate("name_empty")
          : EkubLocalization.of(context)!.translate("name_empty_member");
    } else if (!isFullName(name)) {
      return isAdmin
          ? EkubLocalization.of(context)!.translate("name_empty")
          : EkubLocalization.of(context)!.translate("name_empty");
    } else if (name.length < 5) {
      return EkubLocalization.of(context)!.translate("name_invalid");
    } else if (name.length > 50) {
      return EkubLocalization.of(context)!.translate("name_length");
    } else if (!isNameValid(name)) {
      return EkubLocalization.of(context)!.translate("name_invalid");
    }
    return null;
  }

  String? isPhoneValid(String phone, bool isAdmin, context) {
    if (phone.isEmpty) {
      return isAdmin
          ? EkubLocalization.of(context)!.translate("phone_empty")
          : "Please Enter Your Phone Number";
    } else if (phone.length < 9) {
      return EkubLocalization.of(context)!.translate("phone_length");
    }
    return null;
  }

  String? isValidField(String data, String tar, context) {
    if (data.isEmpty) {
      return "${EkubLocalization.of(context)!.translate("valid_field")} $tar";
    }
    if (data.length > 50) {
      return "$tar ${EkubLocalization.of(context)!.translate("field_length")}";
    }

    return null;
  }

  String? checkLength(String data, String tar, context) {
    if (data.length > 50) {
      return "$tar ${EkubLocalization.of(context)!.translate("field_length")}";
    }
    return null;
  }

  bool _validEmail(String email) {
    return RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(email);
  }

  String? isEmailValid(String email, bool isAdmin, context) {
    if (email.isEmpty) {
      return isAdmin
          ? EkubLocalization.of(context)!.translate("email_empty")
          : EkubLocalization.of(context)!.translate("email_empty_member");
    } else if (!_validEmail(email)) {
      return EkubLocalization.of(context)!.translate("email_invalid");
    }
    return null;
  }

  String? isValidEmail(String email, context) {
    if (email.isNotEmpty) {
      if (!_validEmail(email)) {
        return EkubLocalization.of(context)!.translate("email_invalid");
      }
    }
    return null;
  }

  String? isPasswordValid(String password, context) {
    if (password.isEmpty) {
      return EkubLocalization.of(context)!.translate("password_empty");
    } else if (password.length < 6) {
      return EkubLocalization.of(context)!.translate("password_length");
    } else if (password.length > 25) {
      return EkubLocalization.of(context)!.translate("password_limit");
    }
    return null;
  }

  String? isPasswordMatch(String password1, String password2, context) {
    if (isPasswordValid(password1, context) == null &&
        isPasswordValid(password2, context) == null &&
        password1 == password2) {
      return null;
    } else if (password2.isEmpty) {
      return EkubLocalization.of(context)!.translate("confirm_password_empty");
    } else if (password2.length < 6) {
      return EkubLocalization.of(context)!.translate("password_length");
    }
    return EkubLocalization.of(context)!.translate("mismatch_password");
  }

  String? isVerificationCodeValid(String code, context) {
    if (code.isEmpty) {
      return EkubLocalization.of(context)!.translate("code_empty");
    } else if (code.length < 6) {
      return EkubLocalization.of(context)!.translate("code_length");
    }
    return null;
  }
}
