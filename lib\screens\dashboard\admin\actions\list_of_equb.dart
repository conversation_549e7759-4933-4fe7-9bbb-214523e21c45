// import 'package:ekub/models/members.dart';
// import 'package:ekub/repository/ekub_localization.dart';
// import 'package:ekub/repository/member_repos.dart';
// import 'package:ekub/routs/shared.dart';
// import 'package:ekub/screens/dashboard/admin/actions/add_ekub.dart';
// import 'package:ekub/screens/dashboard/admin/members.dart';
// import 'package:ekub/screens/dashboard/root/root_screen.dart';
// import 'package:ekub/screens/themes/ThemeProvider.dart';
// import 'package:ekub/screens/ui_kits/loading_indicator.dart';
// import 'package:ekub/utils/constants.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
// import 'package:provider/provider.dart';
// import '../../../../models/equb_type.dart';
// import '../../../../repository/equb_repos.dart';
// import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
// import 'package:panara_dialogs/panara_dialogs.dart';
// import 'package:http/http.dart' as http;
// import 'package:intl/intl.dart';

// class ListOfEqub extends StatefulWidget {
//   final String title;
//   final String description;
//   final EqubType equbType;
//   final String frequency;
//   final AddEqubArgs args;

//   ListOfEqub(
//       {required this.title,
//       required this.description,
//       required this.equbType,
//       required this.frequency,
//       required this.args});

//   @override
//   _ListOfEqubState createState() => _ListOfEqubState();
// }

// class _ListOfEqubState extends State<ListOfEqub> {
//   bool _onProcess = false;
//   Member? member;
//   late ThemeProvider themeProvider;
//   late Future<List<EqubType>> _equbFuture;
//   bool _agreedToTerms = false;

//   @override
//   void initState() {
//     super.initState();
//     member = widget.args.member;
//     themeProvider = Provider.of<ThemeProvider>(context, listen: false);
//     loadProfile();
//     _equbFuture = _loadEqubs();
//   }

//   Future<List<EqubType>> _loadEqubs() {
//     if (member == null || member?.id == null) {
//       throw Exception('Member ID is required');
//     }
//     return Provider.of<EqubDataProvider>(context, listen: false)
//         .loadEqubsByFrequency(context, widget.frequency, member!.id!);
//   }

//   loadProfile() async {
//     member = await Provider.of<MemberProvider>(context, listen: false)
//         .getMember(context, member!.id.toString());
//   }

//   void refreshEqubList() {
//     setState(() {
//       print("Refreshing equb list...");
//       _equbFuture = _loadEqubs(); // Reload the list
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder<List<EqubType>>(
//       future: _equbFuture,
//       builder: (context, snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return searchLoading();
//         } else if (snapshot.hasError) {
//           print('Error: ${snapshot.error}');
//           return Center(child: Text('Error: ${snapshot.error}'));
//         } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
//           print('No Equbs found.');

//           return Center(child: Text('No Equbs found!'));
//         } else {
//           print('Equbs loaded: ${snapshot.data!.length}');
//           return SingleChildScrollView(
//             child: Column(
//               children: snapshot.data!.map((equb) {
//                 return _buildEqubCard(equb);
//               }).toList(),
//             ),
//           );
//         }
//       },
//     );
//   }

//   // Widget buildTestWidget(EqubType equb) {
//   //   //return Text(equb.name ?? 'No Name');
//   //   return Card(
//   //     margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
//   //     elevation: 4.0,
//   //     child: Padding(
//   //       padding: EdgeInsets.all(8.0),
//   //       child: Column(
//   //         crossAxisAlignment: CrossAxisAlignment.start,
//   //         children: [
//   //           Text(
//   //             equb.name ?? 'No Name',
//   //             style: TextStyle(
//   //               fontWeight: FontWeight.bold,
//   //               fontSize: 16.0,
//   //             ),
//   //           ),
//   //           SizedBox(height: 10),
//   //           Text('Type: ${equb.type ?? 'Unknown'}'),
//   //           Text('Amount: ${equb.amount ?? 'N/A'}'),
//   //         ],
//   //       ),
//   //     ),
//   //   );
//   // }

//   Widget _buildEqubCard(EqubType equb) {
//     // // Check if equb is null or any critical property is null
//     // if (equb == null || equb.name == null || equb.type == null) {
//     //   return Text('Invalid Equb Data');
//     // }

//     return equb.type == "Automatic" || equb.type == "Seasonal"
//         ? Card(
//             margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 1.0),
//             elevation: 4.0,
//             child: ClipRRect(
//               borderRadius: BorderRadius.circular(4.0),
//               child: Padding(
//                 padding: EdgeInsets.all(8.0),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Row(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               Image.asset(
//                                 'assets/icons/Money_hand.png',
//                                 color: themeProvider.getLightColor,
//                                 width: 20,
//                                 height: 20,
//                               ),
//                               SizedBox(width: 8),
//                               Expanded(
//                                 child: Column(
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     Text(
//                                       equb.name!,
//                                       style: TextStyle(
//                                           color: themeProvider.getLightColor,
//                                           fontWeight: boldFont),
//                                     ),
//                                     SizedBox(height: 10),
//                                     _buildInfoRow(
//                                       'Start Date',
//                                       _formatDate(equb.startDate),
//                                     ),
//                                     _buildInfoRow(
//                                         'End Date', _formatDate(equb.endDate)),
//                                     _buildInfoRow('Amount', equb.amount,
//                                         fontSizes: {'Amount': 14.0}),
//                                   ],
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ],
//                       ),
//                     ),
//                     ElevatedButton(
//                       onPressed: _onProcess
//                           ? null
//                           : () async {
//                               setState(() {
//                                 _onProcess = true;
//                               });

//                               // Show loading indicator immediately
//                               showDialog(
//                                 context: context,
//                                 barrierDismissible: false,
//                                 builder: (BuildContext context) {
//                                   return Center(
//                                     child: CircularProgressIndicator(),
//                                   );
//                                 },
//                               );

//                               // Check internet connectivity asynchronously
//                               bool isConnected = await InternetConnectivity()
//                                   .checkInternetConnectivty(context, true);
//                               if (!isConnected) {
//                                 Navigator.pop(context);
//                                 setState(() {
//                                   _onProcess = false;
//                                 });
//                                 return;
//                               }

//                               // Fetch member data asynchronously
//                               var member = await Provider.of<MemberProvider>(
//                                       context,
//                                       listen: false)
//                                   .getMember(context,
//                                       widget.args.member?.id.toString() ?? '0');

//                               if (member.status != "Active" &&
//                                   widget.args.user?.role == "member") {
//                                 Navigator.pop(context);
//                                 showDialog(
//                                   context: context,
//                                   builder: (BuildContext context) {
//                                     return AlertDialog(
//                                       title: const Row(
//                                         children: [
//                                           Icon(Icons.warning,
//                                               color: Colors.red),
//                                           SizedBox(width: 8),
//                                           Text('Account Inactive'),
//                                         ],
//                                       ),
//                                       content: const Text(
//                                         "Your account is inactive",
//                                         style: TextStyle(fontSize: 16),
//                                       ),
//                                       actions: [
//                                         ElevatedButton(
//                                           onPressed: () {
//                                             Navigator.pop(context);
//                                           },
//                                           style: ElevatedButton.styleFrom(
//                                             backgroundColor:
//                                                 themeProvider.getLightColor,
//                                           ),
//                                           child: Text("Okay"),
//                                         ),
//                                       ],
//                                     );
//                                   },
//                                 );
//                                 setState(() {
//                                   _onProcess = false;
//                                 });
//                                 return;
//                               }

//                               Navigator.pop(context);

//                               // Print the data that will be sent
//                               print("Data to be sent:");
//                               print("Member ID: ${member.id}");
//                               print("Equb Type ID: ${equb.id}");
//                               print("Amount: ${equb.amount}");
//                               print(
//                                   "Total Amount: ${calculateExpectedAmount(equb.amount?.toDouble() ?? 0.0, equb.startDate ?? '', equb.endDate ?? '')}");
//                               print("Start Date: ${equb.startDate}");
//                               print("End Date: ${equb.endDate}");
//                               print(
//                                   "Lottery Date: ''"); // Assuming lottery date is not set here
//                               print(
//                                   "Timeline t: '0'"); // Assuming timeline is not set here
//                               print("Type: ${equb.type}");
//                               // Show modal bottom sheet
//                               showModalBottomSheet(
//                                 context: context,
//                                 isDismissible: true,
//                                 isScrollControlled: true,
//                                 builder: (builder) {
//                                   return StatefulBuilder(
//                                     builder: (BuildContext context,
//                                         StateSetter setModalState) {
//                                       return Container(
//                                         height:
//                                             MediaQuery.of(context).size.height *
//                                                 0.9,
//                                         decoration: const BoxDecoration(
//                                           color: Colors.white,
//                                         ),
//                                         child: Column(
//                                           children: [
//                                             Flexible(
//                                               child: SingleChildScrollView(
//                                                 child: Column(
//                                                   crossAxisAlignment:
//                                                       CrossAxisAlignment.start,
//                                                   children: [
//                                                     Padding(
//                                                       padding: const EdgeInsets
//                                                           .symmetric(
//                                                           vertical: 10,
//                                                           horizontal: 20),
//                                                       child: Text(
//                                                         "${EkubLocalization.of(context)!.translate("terms")} ${equb.name}",
//                                                         style: TextStyle(
//                                                           fontWeight:
//                                                               FontWeight.bold,
//                                                           fontSize: fontBig,
//                                                         ),
//                                                       ),
//                                                     ),
//                                                     Divider(
//                                                       color:
//                                                           Colors.grey.shade400,
//                                                     ),
//                                                     equb.term == null
//                                                         ? Padding(
//                                                             padding:
//                                                                 const EdgeInsets
//                                                                     .symmetric(
//                                                                     horizontal:
//                                                                         15),
//                                                             child: Text(
//                                                               "${EkubLocalization.of(context)!.translate("no_terms")}.",
//                                                               style:
//                                                                   const TextStyle(
//                                                                 color: Colors
//                                                                     .black38,
//                                                                 fontWeight:
//                                                                     normalFontWeight,
//                                                                 fontSize:
//                                                                     fontMedium,
//                                                               ),
//                                                             ),
//                                                           )
//                                                         : Padding(
//                                                             padding:
//                                                                 const EdgeInsets
//                                                                     .symmetric(
//                                                                     horizontal:
//                                                                         15),
//                                                             child: HtmlWidget(
//                                                               "${equb.term}",
//                                                             ),
//                                                           ),
//                                                     SizedBox(height: 20),
//                                                   ],
//                                                 ),
//                                               ),
//                                             ),
//                                             Row(
//                                               mainAxisAlignment:
//                                                   MainAxisAlignment.center,
//                                               children: [
//                                                 Checkbox(
//                                                   value: _agreedToTerms,
//                                                   onChanged: (bool? value) {
//                                                     setModalState(() {
//                                                       _agreedToTerms =
//                                                           value ?? false;
//                                                     });
//                                                   },
//                                                 ),
//                                                 TextButton(
//                                                   onPressed: () {
//                                                     setModalState(() {
//                                                       _agreedToTerms =
//                                                           !_agreedToTerms;
//                                                     });
//                                                   },
//                                                   child: Text(
//                                                     'I agree to the terms and conditions',
//                                                     style: TextStyle(
//                                                       color: themeProvider
//                                                           .getLightColor,
//                                                     ),
//                                                   ),
//                                                 ),
//                                               ],
//                                             ),
//                                             Container(
//                                               width: double.infinity,
//                                               padding:
//                                                   const EdgeInsets.all(16.0),
//                                               child: ElevatedButton(
//                                                 onPressed: _agreedToTerms
//                                                     ? () {
//                                                         final equbId =
//                                                             equb.id ?? 0;
//                                                         final typeId =
//                                                             equb.id ?? 0;
//                                                         final amount = equb
//                                                                 .amount
//                                                                 ?.toString() ??
//                                                             '0';
//                                                         final totalAmount =
//                                                             calculateExpectedAmount(
//                                                                 equb.amount
//                                                                         ?.toDouble() ??
//                                                                     0.0,
//                                                                 equb.startDate ??
//                                                                     '',
//                                                                 equb.endDate ??
//                                                                     '');
//                                                         final startDate = equb
//                                                                 .startDate
//                                                                 ?.split(
//                                                                     ' ')[0] ??
//                                                             '';
//                                                         final endDate =
//                                                             equb.endDate?.split(
//                                                                     ' ')[0] ??
//                                                                 '';
//                                                         final lotteryDate = '';
//                                                         final timeline = '0';

//                                                         _addEqub(
//                                                           member!.id ?? 0,
//                                                           typeId,
//                                                           amount,
//                                                           totalAmount,
//                                                           startDate,
//                                                           endDate,
//                                                           lotteryDate,
//                                                           timeline,
//                                                           equb.type ??
//                                                               'Automatic',
//                                                         );

//                                                         Navigator.pop(context);
//                                                       }
//                                                     : null,
//                                                 style: ButtonStyle(
//                                                   foregroundColor:
//                                                       MaterialStateProperty
//                                                           .resolveWith<
//                                                               Color>((Set<
//                                                                   MaterialState>
//                                                               states) {
//                                                     if (states.contains(
//                                                         MaterialState
//                                                             .disabled)) {
//                                                       return Colors
//                                                           .white; // Text color when disabled
//                                                     }
//                                                     return Colors
//                                                         .white; // Text color when enabled
//                                                   }),
//                                                   backgroundColor:
//                                                       MaterialStateProperty
//                                                           .resolveWith<
//                                                               Color>((Set<
//                                                                   MaterialState>
//                                                               states) {
//                                                     if (states.contains(
//                                                         MaterialState
//                                                             .disabled)) {
//                                                       return Colors
//                                                           .grey; // Background color when disabled
//                                                     }
//                                                     return themeProvider
//                                                         .getLightColor; // Background color when enabled
//                                                   }),
//                                                   elevation:
//                                                       MaterialStateProperty.all(
//                                                           0),
//                                                   shadowColor:
//                                                       MaterialStateProperty.all(
//                                                           Colors.transparent),
//                                                   surfaceTintColor:
//                                                       MaterialStateProperty.all(
//                                                           Colors.transparent),
//                                                 ),
//                                                 child: Text('Add Equb'),
//                                               ),
//                                             ),
//                                           ],
//                                         ),
//                                       );
//                                     },
//                                   );
//                                 },
//                               ).whenComplete(() {
//                                 setState(() {
//                                   _onProcess = false;
//                                 });
//                               });
//                             },
//                       style: TextButton.styleFrom(
//                         foregroundColor:
//                             const Color.fromARGB(255, 255, 255, 255),
//                         backgroundColor: themeProvider.getLightColor,
//                         padding:
//                             EdgeInsets.symmetric(vertical: 0, horizontal: 24.0),
//                         shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(38.0),
//                         ),
//                         //elevation: 5.0,
//                       ),
//                       child: Text(
//                         'Join',
//                         style: TextStyle(fontSize: 14),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ))
//         : Card(
//             margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
//             elevation: 4.0,
//             child: ListTile(
//               onTap: () {
//                 Navigator.push(
//                   context,
//                   MaterialPageRoute(
//                     builder: (context) => AddEqub(
//                       slectedEqubType: equb,
//                       args: AddEqubArgs(
//                         isOnline: true,
//                         member: widget.args.member,
//                         isAdmin: false,
//                         user: widget.args.user,
//                       ),
//                     ),
//                   ),
//                 );
//               },
//             ),
//           );
//   }

//   String calculateExpectedAmount(
//       double amount, String startDate, String endDate) {
//     int days =
//         DateTime.parse(endDate).difference(DateTime.parse(startDate)).inDays;
//     return (amount * days).toString();
//   }

//   _addEqub(
//       int memberId,
//       int typeId,
//       String amount,
//       String totalAmount,
//       String startDate,
//       String endDate,
//       String lotteryDate,
//       String timeline,
//       String type) async {
//     var sender = EqubDataProvider(httpClient: http.Client());
//     var response = await sender.addEqubToMember(context, memberId, typeId,
//         amount, totalAmount, startDate, endDate, lotteryDate, timeline, type);

//     if (response["code"] == 200) {
//       // Show loading indicator
//       showDialog(
//         context: context,
//         barrierDismissible: false,
//         builder: (BuildContext context) {
//           return Center(
//             child: CircularProgressIndicator(),
//           );
//         },
//       );

//       // Show a snackbar after a short delay
//       Future.delayed(Duration(seconds: 1), () {
//         Navigator.pop(context); // Close the loading indicator
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text("You have successfully joined the equb."),
//             duration: Duration(seconds: 2),
//             backgroundColor: Colors.green,
//           ),
//         );

//         // Perform navigation automatically after showing the snackbar
//         Future.delayed(Duration(seconds: 2), () {
//           if (widget.args.user!.role == "equb_collector") {
//             Navigator.pushAndRemoveUntil(
//               context,
//               MaterialPageRoute(
//                 builder: (context) => MembersScreen(
//                   args: MemberEqubsArgs(
//                     member: Member(
//                       id: int.parse(widget.args.user!.id ?? "0"),
//                       fullName: widget.args.user!.fullName,
//                       phone: widget.args.user!.phoneNumber,
//                       gender: widget.args.user!.gender,
//                     ),
//                     isOnline: true,
//                   ),
//                 ),
//               ),
//               (route) => false,
//             );
//           } else {
//             _openHomeScreen(
//               widget.args.user!.role!.toLowerCase() == "admin",
//               widget.args.user!.role!,
//             );
//           }
//         });
//       });
//     } else {
//       showDialog(
//         context: context,
//         builder: (BuildContext context) {
//           return AlertDialog(
//             title: Row(
//               children: [
//                 Icon(Icons.error, color: Colors.red),
//                 SizedBox(width: 8),
//                 Text("Error"),
//               ],
//             ),
//             content: Text(
//               response["message"],
//               style: TextStyle(fontSize: 16),
//             ),
//             actions: [
//               TextButton(
//                 onPressed: () {
//                   Navigator.pop(context);
//                 },
//                 child: Text("Retry",
//                     style: TextStyle(color: themeProvider.getLightColor)),
//               ),
//             ],
//           );
//         },
//       );
//     }
//     setState(() {
//       _onProcess = false;
//     });
//   }

//   _openHomeScreen(bool isAdmin, String role) async {
//     HomeScreenArgs argument =
//         HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);
//     Navigator.pushNamedAndRemoveUntil(
//         context, HomeScreen.routeName, (Route<dynamic> route) => false,
//         arguments: argument);
//   }

//   Widget _buildInfoRow(String label, dynamic value,
//       {Map<String, double>? fontSizes}) {
//     double fontSize =
//         fontSizes?[label] ?? 12.0; // Default to 14.0 if not specified
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 6.0),
//       child: Row(
//         children: [
//           Text(
//             '$label: ',
//             style: TextStyle(
//               fontWeight: FontWeight.bold,
//               color: Colors.black87,
//               fontSize: fontSize, // Use the specified font size
//             ),
//           ),
//           Expanded(
//             child: Text(
//               value?.toString() ?? 'N/A',
//               style: TextStyle(
//                 color: Colors.black54,
//                 fontSize: fontSize,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   String _formatDate(String? date) {
//     if (date == null || date.isEmpty) return 'N/A';
//     try {
//       final parsedDate = DateTime.parse(date);
//       return DateFormat('MMMM dd, yyyy', 'en_US').format(parsedDate);
//     } catch (e) {
//       return 'Invalid date';
//     }
//   }
// }
