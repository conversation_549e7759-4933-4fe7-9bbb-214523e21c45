import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

class WebSocketApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Real-time Payment Status',
      debugShowCheckedModeBanner: false,
      home: PaymentStatusScreen(memberId: 1), // Replace with actual user ID
    );
  }
}

class PaymentStatusScreen extends StatefulWidget {
  final int memberId;

  PaymentStatusScreen({required this.memberId});

  @override
  _PaymentStatusScreenState createState() => _PaymentStatusScreenState();
}

class _PaymentStatusScreenState extends State<PaymentStatusScreen> {
  late WebSocketChannel channel;
  String paymentStatus = "Waiting for payment...";
  int amount = 0;

  @override
  void initState() {
    super.initState();
    _connectWebSocket();
  }

  void _connectWebSocket() {
    final wsUrl = Uri.parse("ws://127.0.0.1:6001/app/d047c7fc730d19cc62b4");

    channel = WebSocketChannel.connect(wsUrl);

    // Listen for events from the WebSocket
    channel.stream.listen((message) {
      print("Received event: $message");
      final Map<String, dynamic> data = jsonDecode(message);

      if (data.containsKey("status") && data.containsKey("amount")) {
        setState(() {
          paymentStatus = data["status"];
          amount = data["amount"];
        });
      }
    }, onError: (error) {
      print("WebSocket Error: $error");
    });
  }

  @override
  void dispose() {
    channel.sink.close(status.goingAway);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Payment Status")),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text("Status: $paymentStatus",
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 10),
            Text("Amount: $amount ETB", style: TextStyle(fontSize: 18)),
          ],
        ),
      ),
    );
  }
}
