// This file provides a platform-independent way to execute JavaScript code
// It uses dart:js on web platforms and provides a stub implementation for other platforms

import 'dart:async';
import 'package:flutter/foundation.dart';

// Conditionally import dart:js
// ignore: avoid_web_libraries_in_flutter
import 'js_interop_web.dart' if (dart.library.io) 'js_interop_mobile.dart'
    as js_impl;

// Execute JavaScript code and return the result
Future<String> executeJavaScript(String code) async {
  try {
    return await js_impl.executeJavaScript(code);
  } catch (e) {
    print('Error executing JavaScript: $e');
    return 'Error: $e';
  }
}
