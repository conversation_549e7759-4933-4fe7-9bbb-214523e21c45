// ignore_for_file: must_be_immutable

import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../repository/ekub_localization.dart';
import '../../utils/constants.dart';

class NoConnectionWidget extends StatefulWidget {
  bool isLoading;
  final Function fun;
  NoConnectionWidget({super.key, required this.fun, required this.isLoading});

  @override
  State<NoConnectionWidget> createState() => _NoConnectionWidgetState();
}

class _NoConnectionWidgetState extends State<NoConnectionWidget> {
  @override
  Widget build(BuildContext context) {
    var themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
              margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
              child: Icon(
                Icons.wifi_off,
                color: bodyTextColor.withOpacity(0.5),
                size: 200,
              )),
          const Center(
              child: Padding(
            padding: EdgeInsets.only(bottom: 10.0),
            child: Text(
              "Opps!",
              style: TextStyle(
                  color: Colors.black, fontSize: 25, fontWeight: boldFont),
            ),
          )),
          Center(
              child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Text(
              "${EkubLocalization.of(context)!.translate("no_connection")}!",
              style: TextStyle(
                  color: bodyTextColor,
                  fontSize: fontMedium,
                  fontWeight: normalFontWeight),
            ),
          )),
          Center(
              child: Text(
            "${EkubLocalization.of(context)!.translate("connection_msg")}.",
            style: TextStyle(
                color: bodyTextColor,
                fontSize: fontMedium,
                fontWeight: normalFontWeight),
          )),
          const SizedBox(
            height: 30,
          ),
          Center(
            child: GestureDetector(
              onTap: () async {
                widget.fun();
                if (!await InternetConnectivity()
                    .checkInternetConnectivty(context, true)) {
                  return;
                } else {
                  setState(() {
                    widget.isLoading = false;
                  });
                }
              },
              child: Center(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  decoration: BoxDecoration(
                    // borderRadius: BorderRadius.circular(10),
                    color: themeProvider.getColor,
                  ),
                  width: 250,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      !widget.isLoading
                          ? const Icon(Icons.refresh)
                          : Container(),
                      const SizedBox(width: 15),
                      Text(EkubLocalization.of(context)!.translate("try_again"),
                          style: buttonText),
                      const SizedBox(width: 15),
                      Align(
                        widthFactor: 2,
                        alignment: Alignment.centerRight,
                        child: widget.isLoading
                            ? const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 8.0),
                                child: SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                  ),
                                ),
                              )
                            : Container(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
