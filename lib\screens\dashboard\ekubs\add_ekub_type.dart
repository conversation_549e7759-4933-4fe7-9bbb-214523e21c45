// ignore_for_file: must_be_immutable, use_build_context_synchronously

import 'dart:async';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../../repository/equb_repos.dart';
import '../../../utils/validator.dart';
import '../../settings/constant.dart';
import '../../themes/ThemeProvider.dart';
import '../../ui_kits/app_bar.dart';

class AddEqubType extends StatefulWidget {
  static const routeName = "/add_equb_type";
  AddEqubTypeArgs args;
  AddEqubType({super.key, required this.args});

  @override
  State<AddEqubType> createState() => _AddEqubState();
}

class _AddEqubState extends State<AddEqubType> {
  bool invisible = false;
  bool isSSubmitted = false;
  bool _onProcess = false;
  String _rote = "Daily";
  String _selectedType = "Manual";
  List<String> equbTypes = [];
  final List<String> _rotes = [
    'Daily',
    'Weekly',
    'Biweekly',
    'Monthly',
  ];
  final List<String> _types = [
    'Automatic',
    'Manual',
  ];

  late String equbTypeId;
  late ThemeProvider themeProvider;
  late TextEditingController nameControl;
  late TextEditingController roundControl;
  late TextEditingController lotteryDateControl;
  late TextEditingController remarkControl;
  final _appBar = GlobalKey<FormState>();
  final _registerFormKey = GlobalKey<FormState>();
  final dropdownControl = TextEditingController();
  final dropdownControl2 = TextEditingController();

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    nameControl = TextEditingController(
        text: widget.args.forEdit == true ? widget.args.equbType!.name : null);
    roundControl = TextEditingController(
        text: widget.args.forEdit == true ? widget.args.equbType!.round : null);
    lotteryDateControl = TextEditingController(
        text: widget.args.forEdit == true
            ? widget.args.equbType!.lotteryDate
            : null);
    remarkControl = TextEditingController(
        text:
            widget.args.forEdit == true ? widget.args.equbType!.remark : null);
    equbTypeId = widget.args.forEdit ? widget.args.equbType!.id.toString() : "";

    dropdownControl.text = widget.args.forEdit == true
        ? widget.args.equbType!.rote.toString()
        : "";
    dropdownControl2.text = widget.args.forEdit == true
        ? widget.args.equbType!.type.toString()
        : "";
    loadEqubType();
    super.initState();
  }

  void loadEqubType() async {
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, true)) {
        return;
      }
      await Provider.of<EqubDataProvider>(context, listen: false)
          .loadEqubTypes(context, 0, 1);
    } catch (e) {
      if (e is TimeoutException) {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: "Request Timeout",
              message: "The request took too long! Please try again.",
              confirmButtonText: "Try Again",
              cancelButtonText: "Cancel", onTapConfirm: () {
            Navigator.pop(context);
            loadEqubType();
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              message: "Unable to load equb types! Please try again.",
              confirmButtonText: "Try Again",
              cancelButtonText: "Cancel", onTapConfirm: () {
            loadEqubType();
            Navigator.pop(context);
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: EkubAppBar(
            key: _appBar,
            title: widget.args.forEdit ? "Edit Equb Type" : "Add Equb Type",
            widgets: const []),
        body: Form(
          key: _registerFormKey,
          autovalidateMode: isSSubmitted
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.disabled,
          child: Stack(
            children: <Widget>[
              Align(
                alignment: Alignment.bottomCenter,
                child: ListView(
                  children: <Widget>[
                    Container(
                      decoration: BoxDecoration(
                          color: Colors.grey[100],
                          //border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(10)),
                      margin: const EdgeInsets.fromLTRB(20, 40, 20, 10),
                      padding: const EdgeInsets.fromLTRB(10, 20, 10, 10),
                      child: Column(
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: TextFormField(
                              style: lableStyle,
                              controller: nameControl,
                              decoration: InputDecoration(
                                  prefixIcon: const Icon(
                                    Icons.payment,
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide:
                                        const BorderSide(color: Colors.white),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide: BorderSide(
                                        color: themeProvider.getColor),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                      color: Colors.red,
                                    ),
                                    borderRadius: BorderRadius.circular(18),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide: const BorderSide(
                                        color: Colors.red, width: 2),
                                  ),
                                  fillColor: Colors.white,
                                  filled: true,
                                  labelText: "Name",
                                  labelStyle: lableStyle),
                              validator: (value) => Sanitizer()
                                  .isValidField(value!, "Name", context),
                              onSaved: (value) {
                                nameControl.text = value!;
                              },
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: TextFormField(
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                      signed: true, decimal: false),
                              style: lableStyle,
                              controller: roundControl,
                              decoration: InputDecoration(
                                  prefixIcon: const Icon(
                                    Icons.rounded_corner,
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide:
                                        const BorderSide(color: Colors.white),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide: BorderSide(
                                        color: themeProvider.getColor),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                      color: Colors.red,
                                    ),
                                    borderRadius: BorderRadius.circular(18),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide: const BorderSide(
                                        color: Colors.red, width: 2),
                                  ),
                                  fillColor: Colors.white,
                                  filled: true,
                                  labelText: "Round",
                                  labelStyle: lableStyle),
                              validator: (value) => Sanitizer()
                                  .isValidField(value!, "Round", context),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: CustomDropdown<String>(
                              onChanged: (newValue) {
                                setState(() {
                                  _rote = newValue!;
                                });
                              },
                              hintText: "Select rote",
                              // hintStyle: lableStyle,
                              // selectedStyle: lableStyle,
                              items: _rotes,
                              // controller: dropdownControl,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: CustomDropdown<String>(
                              onChanged: (newValue) {
                                setState(() {
                                  _selectedType = newValue!;
                                });
                              },
                              hintText: "Select type",
                              // hintStyle: lableStyle,
                              // selectedStyle: lableStyle,
                              items: _types,
                              // controller: dropdownControl2,
                            ),
                          ),
                          _selectedType == "Automatic"
                              ? Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: TextFormField(
                                    controller: lotteryDateControl,
                                    decoration: InputDecoration(
                                        prefixIcon: const Icon(
                                          Icons.date_range_outlined,
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(18),
                                          borderSide: const BorderSide(
                                              color: Colors.white),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(18),
                                          borderSide: BorderSide(
                                              color: themeProvider.getColor),
                                        ),
                                        errorBorder: OutlineInputBorder(
                                          borderSide: const BorderSide(
                                            color: Colors.red,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(18),
                                        ),
                                        focusedErrorBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(18),
                                          borderSide: const BorderSide(
                                              color: Colors.red, width: 2),
                                        ),
                                        fillColor: Colors.white,
                                        filled: true,
                                        labelText: "Lottery Date",
                                        labelStyle: const TextStyle(
                                            color: Colors.grey)),
                                    readOnly: true,
                                    onTap: () async {
                                      DateTime? pickedDate =
                                          await showDatePicker(
                                              context: context,
                                              initialDate: DateTime.now(),
                                              firstDate: DateTime(1950),
                                              lastDate: DateTime(2100));

                                      if (pickedDate != null) {
                                        String formattedDate =
                                            DateFormat('yyyy-MM-dd')
                                                .format(pickedDate);
                                        setState(() {
                                          lotteryDateControl.text =
                                              formattedDate;
                                        });
                                      }
                                    },
                                    validator: (value) => Sanitizer()
                                        .isValidField(
                                            value!, "Lottery Date", context),
                                  ),
                                )
                              : Container(),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: TextFormField(
                              style: lableStyle,
                              controller: remarkControl,
                              maxLines: 5,
                              decoration: InputDecoration(
                                  prefixIcon: const Icon(
                                    Icons.note_outlined,
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide:
                                        const BorderSide(color: Colors.white),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide: BorderSide(
                                        color: themeProvider.getColor),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                      color: Colors.red,
                                    ),
                                    borderRadius: BorderRadius.circular(18),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    borderSide: const BorderSide(
                                        color: Colors.red, width: 2),
                                  ),
                                  fillColor: Colors.white,
                                  filled: true,
                                  labelText: "Remark",
                                  labelStyle: lableStyle),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width * 0.7,
                        height: 50,
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              gradient: LinearGradient(
                                  colors: [
                                    themeProvider.getColor,
                                    themeProvider.getLightColor
                                  ],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter)),
                          child: Material(
                            borderRadius: BorderRadius.circular(20),
                            color: Colors.transparent,
                            child: ElevatedButton(
                              onPressed: _onProcess
                                  ? null
                                  : () async {
                                      setState(() {
                                        isSSubmitted = true;
                                      });
                                      final form =
                                          _registerFormKey.currentState;
                                      if (form!.validate()) {
                                        if (!await InternetConnectivity()
                                            .checkInternetConnectivty(
                                                context, true)) {
                                          setState(() {
                                            _onProcess = false;
                                          });
                                          return;
                                        } else {
                                          setState(() {
                                            _onProcess = true;
                                          });
                                          form.save();
                                          _addEqubType(
                                              nameControl.text,
                                              roundControl.text,
                                              _rote,
                                              _selectedType,
                                              lotteryDateControl.text,
                                              remarkControl.text,
                                              equbTypeId);
                                        }
                                      }
                                    },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Spacer(),
                                  widget.args.forEdit
                                      ? Text("UPDATE", style: buttonText)
                                      : Text("ADD EQUB TYPE",
                                          style: buttonText),
                                  const Spacer(),
                                  Align(
                                    widthFactor: 2,
                                    alignment: Alignment.centerRight,
                                    child: _onProcess
                                        ? const Padding(
                                            padding: EdgeInsets.all(8.0),
                                            child: SizedBox(
                                              height: 20,
                                              width: 20,
                                              child: CircularProgressIndicator(
                                                color: Colors.white,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }

  _addEqubType(String name, String round, String rote, String type,
      String lotteryDate, String remark, String id) {
    var sender = EqubDataProvider(httpClient: http.Client());
    var res = widget.args.forEdit
        ? sender.editEqubType(context, name, int.parse(round), rote, type,
            lotteryDate, remark, id)
        : sender.addEqubType(
            context, name, round, rote, type, lotteryDate, remark);
    res
        .then((value) => {
              if (value.code == "200")
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value.message,
                    buttonText: "Okay",
                    imagePath: successDialogIcon,
                    onTapDismiss: () {
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                    panaraDialogType: PanaraDialogType.success,
                  ),
                  setState(() {
                    _onProcess = false;
                  })
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value.message,
                    buttonText: "Okay",
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                }
            })
        .onError((error, stackTrace) {
      PanaraConfirmDialog.show(context,
          // title: "Request Timeout",
          message: error.toString(),
          confirmButtonText: "Try Again",
          cancelButtonText: "Cancel", onTapConfirm: () {
        Navigator.pop(context);
        _addEqubType(name, round, rote, type, lotteryDate, remark, id);
      }, onTapCancel: () {
        Navigator.pop(context);
      }, panaraDialogType: PanaraDialogType.error);
      return {};
    });
  }
}

String? gender;
