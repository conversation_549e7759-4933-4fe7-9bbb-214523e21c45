// import 'dart:async';
// import 'package:floor/floor.dart';
// import 'package:ekub/exports/models.dart';
// import 'package:sqflite/sqflite.dart' as sqflite;

// import '../daos/request_dao.dart';

// part 'database.g.dart'; // the generated code will be there

// @Database(version: 1, entities: [ClientRequest])
// abstract class AppDatabase extends FloorDatabase {
//   RequestDao get requestDao;
// }