// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';

class Sleek extends StatelessWidget {
  final double max;
  final int date_num;
  const Sleek({required this.max, required this.date_num, super.key});
  @override
  Widget build(BuildContext context) {
    String data = "Loading";
    switch (date_num) {
      case 1:
        data = "Monday";
        break;
      case 2:
        data = "Tuesday";
        break;
      case 3:
        data = "Wednsday";
        break;
      case 4:
        data = "Thursday";
        break;
      case 5:
        data = "Friday";
        break;
      case 6:
        data = "Saturday";
        break;
      case 7:
        data = "Sunday";
        break;
    }
    return Column(
      children: [
        SleekCircularSlider(
          min: 0,
          max: 100,
          initialValue: max,
          appearance: CircularSliderAppearance(
            infoProperties: InfoProperties(
                mainLabelStyle: const TextStyle(
              fontSize: 25,
            )),
            customColors: CustomSliderColors(
              shadowMaxOpacity: 0.5,
              shadowStep: 3,
            ),
            spinnerDuration: 5,
            animDurationMultiplier: 5,
            animationEnabled: true,
            startAngle: 0.0,
            angleRange: 360,
          ),
        ),
        Center(child: Text(data)),
      ],
    );
  }
}
