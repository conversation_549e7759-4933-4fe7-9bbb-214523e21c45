// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

//COLORS
const Color profile_info_background = Color(0xFF3775FD);
const Color profile_info_categories_background = Color(0xFFF6F5F8);
const Color profile_info_address = Color(0xFF8D7AEE);
const Color profile_info_privacy = Color(0xFFF369B7);
const Color profile_info_general = Color(0xFFFFC85B);
const Color profile_info_notification = Color(0xFF5DD1D3);
const Color profile_item_color = Color(0xFFC4C5C9);
const String imagePath = 'assets/image';

const String devMausam = 'https://firebasestorage.google';
const String errorDialogIcon = "assets/icons/error.png";
const String warningDialogIcon = "assets/icons/warning.png";
const String successDialogIcon = "assets/icons/success.png";

List<ProfileMenu> lampList = [
  ProfileMenu(title: 'Landscape', subTitle: '384'),
  ProfileMenu(title: 'Discus Pendant', subTitle: '274'),
  ProfileMenu(title: 'Mushroom Lamp', subTitle: '374'),
  ProfileMenu(title: 'Titanic Pendant', subTitle: '562'),
  ProfileMenu(title: 'Torn Lighting', subTitle: '105'),
  ProfileMenu(title: 'Abduction Pendant', subTitle: '365'),
];
const List profileItems = [
  {'count': '846', 'name': 'Birr'},
  {'count': '51', 'name': 'Ekubs'},
  {'count': '267', 'name': 'Track'},
  {'count': '39', 'name': 'Coupons'},
];

List<String> dashboardData = ["daily", "weekly", "monthly", "yearly"];
List<ProfileMenu> profileMenuList = [
  // ProfileMenu(
  //   title: 'Developed By',
  //   subTitle: 'Vintage Technology PLC.',
  //   iconColor: profile_info_notification,
  //   icon: Icons.developer_board,
  // ),
  ProfileMenu(
    title: 'Logout',
    subTitle: '',
    iconColor: profile_info_notification,
    icon: Icons.logout,
  ),
];

class ProfileMenu {
  String title;
  String subTitle;
  IconData? icon;
  Color? iconColor;
  ProfileMenu(
      {this.icon, required this.title, this.iconColor, required this.subTitle});
}
