// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDN5YN4SftFZfTCSb45gXwZ58_MqJEfLV8',
    appId: '1:1006497021169:web:6c2dcf302f6105223b7d50',
    messagingSenderId: '1006497021169',
    projectId: 'virtual-equb',
    authDomain: 'virtual-equb.firebaseapp.com',
    storageBucket: 'virtual-equb.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC7Gm2ciancxSDNolbpfYqCy7OePRWUCpg',
    appId: '1:1006497021169:android:df4069253e1eb1243b7d50',
    messagingSenderId: '1006497021169',
    projectId: 'virtual-equb',
    storageBucket: 'virtual-equb.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBwqe4JGuIpnz2WJhv0oEW-Z6gaX9oYyaw',
    appId: '1:1006497021169:ios:59a0ab0fba177cca3b7d50',
    messagingSenderId: '1006497021169',
    projectId: 'virtual-equb',
    storageBucket: 'virtual-equb.firebasestorage.app',
    iosBundleId: 'com.vintechplc.virtualequb',
  );
}
