import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:intl/intl.dart';

class GridCard extends StatelessWidget {
  GridCard({
    super.key,
    required this.title,
    required this.svgSrc,
    required this.collected,
    required this.expected,
    required this.icon,
  });

  final String title, svgSrc;
  final double collected, expected;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    final language = EkubLocalization.of(context)!;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15.0),
      ),
      margin: const EdgeInsets.only(top: 10),
      child: Container(
        padding: const EdgeInsets.only(top: 10, bottom: 15, right: 5, left: 5),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(defaultPadding),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(5.0),
                  child: Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: fontMedium,
                      fontWeight: boldFont,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(
              height: 3,
            ),
            Padding(
              padding: const EdgeInsets.all(5),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${language.translate("collected")}:",
                    style: TextStyle(
                      fontSize: fontSmall,
                      color: Theme.of(context).primaryColor,
                      fontWeight: boldFont,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 0.5),
                    child: Text(
                      "${formatCurrency.format(collected)} ${language.translate("etb")}",
                      style: TextStyle(
                        fontSize: fontSmall,
                        color: bodyTextColor,
                        fontWeight: boldFont,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 10.0),
                    child: Text(
                      "${language.translate("expected")}:",
                      style: TextStyle(
                        fontSize: fontSmall,
                        color: Theme.of(context).primaryColor,
                        fontWeight: boldFont,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 0.5),
                    child: Text(
                      "${formatCurrency.format(expected)} ${language.translate("etb")}",
                      style: TextStyle(
                        fontSize: fontSmall,
                        color: bodyTextColor,
                        fontWeight: boldFont,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.all(1.0),
              child: SizedBox(
                child: LinearPercentIndicator(
                  animation: true,
                  lineHeight: 20.0,
                  animationDuration: 2500,
                  percent: _inDecimal(),
                  center: Text(
                    _inPercent(),
                    style: TextStyle(
                        color:
                            _inDecimal() > 0.5 ? Colors.white : Colors.black),
                  ),
                  barRadius: const Radius.circular(0.5),
                  progressColor: _inDecimal() != 1
                      ? Theme.of(context).primaryColor.withOpacity(0.5)
                      : Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _inDecimal() {
    var dec = collected / expected;
    if (dec > 1) {
      dec = 1;
    }
    if (dec.isNaN) {
      return 0.toDouble();
    }
    return dec;
  }

  String _inPercent() {
    double val = _inDecimal() * 100;
    String p = "%";
    String res = val.toString();
    String result = res.split(".")[0];
    return result + p;
  }

  final formatCurrency = NumberFormat("#,##0.00", "en_US");
}
