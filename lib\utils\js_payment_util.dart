import 'package:flutter/material.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/screens/payment/payment_success.dart';
import 'package:ekub/utils/tools.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class JSPaymentUtil {
  /// Process a payment using JavaScript bridge
  static Future<void> processPayment({
    required BuildContext context,
    required String title,
    required String amount,
    required Member member,
  }) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Processing payment...'),
            ],
          ),
        );
      },
    );

    try {
      // Log the payment details
      print('\n\n==== JAVASCRIPT PAYMENT PROCESSING ====');
      print('Title: $title');
      print('Amount: $amount');
      print('Member ID: ${member.id}');
      print('======================================\n');

      // Simulate getting a payment URL from the backend
      await Future.delayed(Duration(seconds: 2));

      // Close loading dialog
      Navigator.pop(context);

      // Simulate a payment URL
      final simulatedPaymentUrl =
          'https://api.virtualequb.com/payment?amount=$amount&title=${Uri.encodeComponent(title)}';

      // Log the payment URL
      print('\n\n==== OPENING PAYMENT URL ====');
      print('URL: $simulatedPaymentUrl');
      print('==============================\n');

      // Open the payment URL using the openSocialMedia function
      print('\n\n==== ATTEMPTING TO OPEN URL ====');
      print('URL: $simulatedPaymentUrl');
      print('==================================\n');

      // Call the openSocialMedia function
      final urlLaunched = await openSocialMedia(simulatedPaymentUrl);

      if (urlLaunched) {
        print('\n\n==== URL OPENED SUCCESSFULLY ====');
        print('==============================\n');
      } else {
        print('\n\n==== FAILED TO OPEN URL ====');
        print('==============================\n');

        // Show error dialog
        if (context.mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text('Error'),
                content: Text('Failed to open payment URL. Please try again.'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('OK'),
                  ),
                ],
              );
            },
          );
          return; // Exit the method if URL couldn't be launched
        }
      }

      // In a real implementation, you would handle the callback from the payment provider
      // For now, we'll just simulate a successful payment after a delay
      await Future.delayed(Duration(seconds: 3));

      // Navigate to success page
      if (context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PaymentSuccessPage(member: member),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog if it's still showing
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      // Show error dialog
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Error'),
              content: Text('Failed to process payment: $e'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('OK'),
                ),
              ],
            );
          },
        );
      }
    }
  }
}
