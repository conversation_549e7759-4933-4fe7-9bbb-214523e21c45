import 'package:intl/intl.dart';
import 'package:ekub/utils/constants.dart';
import 'package:jailbreak_root_detection/jailbreak_root_detection.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:flutter/material.dart';

String formatDate(String date, {BuildContext? context}) {
  // Use the current app locale if context is provided
  if (context != null) {
    final locale = Localizations.localeOf(context).toString();
    return DateFormat('MMMM-d-yyyy', locale).format(DateTime.parse(date));
  }
  // Fallback to default format
  return DateFormat('MMMM-d-yyyy').format(DateTime.parse(date));
}

NumberFormat formatCurrency(String amount, {BuildContext? context}) {
  // Use the current app locale if context is provided
  if (context != null) {
    final locale = Localizations.localeOf(context).toString();
    return NumberFormat("#,##0.00", locale);
  }
  // Fallback to default format
  return NumberFormat("#,##0.00", "en_US");
}

Future<bool> openSocialMedia(String url) async {
  try {
    print('\n\n==== OPENING URL IN BROWSER ====');
    print('URL: $url');

    // Parse the URL
    final uri = Uri.parse(url);
    print('URI: $uri');

    // Launch the URL
    final result = await launchUrl(
      uri,
      mode: LaunchMode.externalApplication,
    );

    print('Launch result: $result');
    print('==================================\n');

    if (!result) {
      throw 'Could not launch $url';
    }

    return true;
  } catch (e) {
    print('\n\n==== ERROR LAUNCHING URL ====');
    print('Error: $e');
    print('===============================\n');
    return false;
  }
}

void email() async {
  try {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: emailContact,
      query: encodeQueryParameters(<String, String>{
        'subject': '',
      }),
    );
    if (!await launchUrl(emailLaunchUri)) {
      throw 'Could not launch $emailLaunchUri';
    }
  } catch (_) {}
}

String? encodeQueryParameters(Map<String, String> params) {
  return params.entries
      .map((MapEntry<String, String> e) =>
          '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
      .join('&');
}

void makePhoneCall() async {
  try {
    var phoneNumber =
        phoneNumberContact; // Replace with the desired phone number
    if (await canLaunchUrl(Uri(scheme: 'tel', path: phoneNumber))) {
      await launchUrl(Uri(scheme: 'tel', path: phoneNumber));
    } else {
      throw 'Could not launch $phoneNumber';
    }
  } catch (_) {}
}

Future<bool> isSecureDevice() async {
  final jailbreakDetection = JailbreakRootDetection.instance;
  final isJailbroken = await jailbreakDetection.isJailBroken;
  final isRealDevice = await jailbreakDetection.isRealDevice;

  if (isJailbroken || !isRealDevice) {
    return false;
  } else {
    return true;
  }
}

String toCamelCase(String phrase) {
  if (phrase.isEmpty) {
    return phrase;
  }
  List<String> words = phrase.split(' ');
  for (int i = 0; i < words.length; i++) {
    if (words[i].isNotEmpty) {
      words[i] = words[i][0].toUpperCase() + words[i].substring(1);
    }
  }
  return words.join(' ');
}

void removePopupDialog(
    BuildContext? context, GlobalKey<NavigatorState>? navigatorKey) {
  if (context != null) {
    // Check if the context is mounted and can pop
    if (context.mounted && Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  } else if (navigatorKey != null && navigatorKey.currentContext != null) {
    // Use the global navigator key as a fallback
    try {
      Navigator.of(navigatorKey.currentContext!, rootNavigator: true).pop();
      // debugPrint("⚠️ Closed loading dialog using global navigator key");
    } catch (e) {
      debugPrint("⚠️ Error closing dialog with global key: $e");
    }
  } else {
    debugPrint(
        "⚠️ No valid context or global navigator key available to close the dialog.");
  }
}

// Helper function for safe number conversion
int? parseInt(dynamic value) {
  if (value == null) return null;
  return value is int ? value : int.tryParse(value.toString());
}

// Helper function for string conversion
String? parseString(dynamic value) {
  if (value == null) return null;
  return value is String ? value : value.toString();
}
