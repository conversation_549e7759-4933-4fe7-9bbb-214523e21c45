// ignore_for_file: empty_catches, use_build_context_synchronously

import 'dart:async';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/models/user.dart';
import 'package:ekub/repository/dashboard_repos.dart';
import 'package:ekub/repository/ekub_localization.dart';
// import 'package:ekub/repository/language.dart'; // Commented out import
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/account/change_password.dart';
import 'package:ekub/screens/dashboard/admin/actions/add_member.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/screens/ui_kits/no_internet_connection_found.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../../models/dashboard.dart';
import '../../../utils/constants.dart';
import '../../settings/constant.dart';
import '../../settings/profile.dart';
import '../../themes/ThemeProvider.dart';
import '../../ui_kits/app_bar.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  var proLoaded = false;
  bool isConnected = true;
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      gender: "loading",
      email: "loading",
      role: "loading",
      memberId: "loading");
  // List<String> languages = ["En", "አማ", "Or", "ትግ", "So"];
  List<String> languages = ["En"];
  String selectedLanguage = "Lan";
  String language = "";
  late ThemeProvider themeProvider;
  final _appBar = GlobalKey<FormState>();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  // late AppLanguage appLanguage; // Removed
  final dropdownControl = TextEditingController();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    // appLanguage = Provider.of<AppLanguage>(context, listen: false); // Removed

    _loadProfile();
    loadData();
    super.initState();
  }

  loadData() async {
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        setState(() {
          isConnected = false;
        });
        return;
      }

      Provider.of<DashboardProvider>(context, listen: false)
          .fetchDashboard(context);
      setState(() {
        isConnected = true;
      });
    } catch (e) {
      if (e is TimeoutException && mounted) {
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("time_out"),
            message: EkubLocalization.of(context)!.translate("timeout_message"),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          loadData();

          Navigator.pop(context);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("error"),
              message: e.toString(),
              // EkubLocalization.of(context)!.translate("error_load_data"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            loadData();
            Navigator.pop(context);
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  _openProfile() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: ((context) => const ProfilePage(member: null))));
  }

  _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());

    try {
      // _fetchLocale(); // Removed call
      await auth.getUserData().then((value) => {
            setState(() {
              user = value;
              proLoaded = true;
            })
          });
    } catch (e) {}
  }

  // Removed _fetchLocale method entirely
  // _fetchLocale() async {
  //   await appLanguage.fetchLocale();
  //   language = appLanguage.appLocal.languageCode.toLowerCase();

  //   if (language == "fr") {
  //     setState(() {
  //       selectedLanguage = "Or";
  //     });
  //   }
  //   if (language == "es") {
  //     setState(() {
  //       selectedLanguage = "ትግ";
  //     });
  //   }
  //   if (language == "am") {
  //     setState(() {
  //       selectedLanguage = "አማ";
  //     });
  //   } else if (language == "tl") {
  //     setState(() {
  //       selectedLanguage = "So";
  //     });
  //   }
  //   else if (language == "en") {
  //     setState(() {
  //       selectedLanguage = "En";
  //     });
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    // appLanguage = Provider.of<AppLanguage>(context, listen: true); // Removed

    final dashboard =
        Provider.of<DashboardProvider>(context, listen: true).dashboard;
    // final members = dashboard.tudayPaidMember?.members ?? [];
    final language = EkubLocalization.of(context)!;
    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: EkubAppBar(key: _appBar, title: user.fullName ?? "", widgets: [
        // SizedBox(
        //   width: 100,
        //   child: CustomDropdown<String>(
        //     onChanged: (value) {
        //       setState(() {
        //         selectedLanguage = value!;
        //         if (selectedLanguage == "En") {
        //           appLanguage.changeLanguage(const Locale("en"));
        //         } else if (selectedLanguage == "አማ") {
        //           appLanguage.changeLanguage(const Locale("am_ET"));
        //         } else if (selectedLanguage == "Or") {
        //           appLanguage.changeLanguage(const Locale("fr"));
        //         } else if (selectedLanguage == "So") {
        //           appLanguage.changeLanguage(const Locale("tl"));
        //         } else if (selectedLanguage == "ትግ") {
        //           appLanguage.changeLanguage(const Locale("es"));
        //         }
        //       });
        //     },
        //     decoration: CustomDropdownDecoration(
        //       closedFillColor: Colors.white,
        //       closedSuffixIcon: Icon(
        //         Icons.language,
        //         color: bodyTextColor,
        //         size: 25,
        //       ),
        //       hintStyle: TextStyle(
        //           color: themeProvider.getColor, fontWeight: boldFont),
        //     ),
        //     hintText: selectedLanguage.substring(0, 2),
        //     items: languages,
        //     excludeSelected: true,
        //   ),
        // ),
        IconButton(
            onPressed: () {
              loadData();
            },
            icon: const Icon(Icons.refresh)),
      ]),
      body: isConnected
          ? RefreshIndicator(
              onRefresh: () async {
                await Future.delayed(const Duration(seconds: 2));

                loadData();
              },
              key: _refreshIndicatorKey,
              child: SizedBox(
                height: MediaQuery.of(context).size.height,
                child: SingleChildScrollView(
                  child: Container(
                    padding: const EdgeInsets.all(defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: defaultPadding),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 20.0,
                          ),
                          decoration: BoxDecoration(
                            color: themeProvider.getColor,
                            borderRadius:
                                const BorderRadius.all(Radius.circular(20)),
                          ),
                          /*child: Chart(height: 200)*/
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Column(
                                children: [
                                  Text(
                                    language.translate("collected"),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        overflow: TextOverflow.clip,
                                        color: themeProvider.getLightColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 20.0),
                                  ),
                                  Text(
                                    language.translate("today"),
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                        overflow: TextOverflow.clip,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 30.0),
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    '${formatCurrency.format(dashboard.daylyPaidAmount)} ETB',
                                    style: const TextStyle(
                                        overflow: TextOverflow.clip,
                                        color: Colors.white,
                                        fontSize: 18.0),
                                  )
                                ],
                              ),
                              const SizedBox(
                                width: 50,
                              ),
                              CircularPercentIndicator(
                                radius: 50.0,
                                lineWidth: 10.0,
                                animation: true,
                                animationDuration: 2500,
                                percent: _inDecimal(
                                    dashboard.daylyPaidAmount ?? 0,
                                    double.parse(
                                        dashboard.daylyExpected ?? "0")),
                                center: Text(
                                  _inPercent(
                                      dashboard.daylyPaidAmount ?? 0,
                                      double.parse(
                                          dashboard.daylyExpected ?? "0")),
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: themeProvider.getLightColor,
                                      fontSize: 20.0),
                                ),
                                circularStrokeCap: CircularStrokeCap.butt,
                                progressColor: _inDecimal(
                                            dashboard.daylyPaidAmount ?? 0,
                                            double.parse(
                                                dashboard.daylyExpected ??
                                                    "0")) !=
                                        1
                                    ? themeProvider.getLightColor
                                        .withOpacity(0.5)
                                    : themeProvider.getLightColor,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Text(
                            "${language.translate("equbs")} ${language.translate("status")}",
                            style: const TextStyle(
                                fontSize: fontBig, fontWeight: FontWeight.bold),
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        SizedBox(
                          height: 700,
                          child: ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: 4,
                              itemBuilder: (context, index) {
                                return equbProgress(index, dashboard);
                              }),
                          //   child: GridView.count(
                          //     physics: const NeverScrollableScrollPhysics(),
                          //     shrinkWrap: true,
                          //     padding: const EdgeInsets.all(5),
                          //     crossAxisCount: 2,
                          //     scrollDirection: Axis.vertical,
                          //     childAspectRatio: 0.9,
                          //     children: [
                          //       for (var i = 0; i < 4; i++)
                          //         GestureDetector(
                          //           onTap: () {},
                          //           child: Padding(
                          //             padding: const EdgeInsets.all(3.0),
                          //             child: _getData(i, dashboard),
                          //           ),
                          //         )
                          //     ],
                          //   ),
                        ),
                        //     Column(
                        //         children:
                        //             List.generate(members.length, (int index) {
                        //       return GestureDetector(
                        //         onTap: () {},
                        //         child: Padding(
                        //           padding: const EdgeInsets.all(3.0),
                        //           child: MemberCard(
                        //             isAdmin: user.role == "admin",
                        //             member: members[index],
                        //             title: members[index].fullName ?? "Unavailable",
                        //             icon: Icons.person_pin,
                        //             theme: themeProvider.getColor,
                        //             phone: members[index].phone ?? "Unavailable",
                        //             status: members[index].gender ?? "Unavailable",
                        //             // widget: Container(),
                        //           ),
                        //         ),
                        //       );
                        //     }))
                      ],
                    ),
                  ),
                ),
              ),
            )
          : NoConnectionWidget(
              fun: loadData,
              isLoading: false,
            ),
      drawer: _drawer(context),
    );
  }

  Container equbProgress(int index, Dashboard dashboard) {
    List all = [
      {
        "svgSrc": "assets/icons/Documents.svg",
        "title": EkubLocalization.of(context)!.translate("daily"),
        "collected": dashboard.daylyPaidAmount ?? 0,
        "expected": double.parse(dashboard.daylyExpected ?? "0"),
        "icon": Icons.person_pin,
      },
      {
        "svgSrc": "assets/icons/Documents.svg",
        "title": EkubLocalization.of(context)!.translate("weekly"),
        "collected": double.parse(dashboard.weeklyPaidAmount ?? "0"),
        "expected": dashboard.weeklyExpected ?? 0,
        "icon": Icons.person_pin,
      },
      {
        "svgSrc": "assets/icons/Documents.svg",
        "title": EkubLocalization.of(context)!.translate("monthly"),
        "collected": dashboard.monthlyPaidAmount ?? 0,
        "expected": dashboard.monthlyExpected ?? 0,
        "icon": Icons.person_pin,
      },
      {
        "svgSrc": "assets/icons/Documents.svg",
        "title": EkubLocalization.of(context)!.translate("yearly"),
        "collected": dashboard.yearlyPaidAmount ?? 0,
        "expected": dashboard.yearlyExpected ?? 0,
        "icon": Icons.person_pin,
      }
    ];

    return Container(
      height: 145,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
      margin: const EdgeInsets.only(bottom: 10, left: 5, right: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5), //color of shadow
            spreadRadius: .1, //spread radius
            blurRadius: 2, // blur radius
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            all[index]["title"],
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 5,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    EkubLocalization.of(context)!.translate("collected"),
                    style: TextStyle(
                        color: Colors.grey.shade400, fontSize: fontSmall),
                  ),
                  Text(
                    '${formatCurrency.format(all[index]["collected"])} ETB',
                    style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: fontMedium),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(EkubLocalization.of(context)!.translate("expected"),
                      style: TextStyle(
                          color: Colors.grey.shade400, fontSize: fontSmall)),
                  Text('${formatCurrency.format(all[index]["expected"])} ETB',
                      style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: fontMedium))
                ],
              ),
            ],
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                _inPercent(all[index]["collected"] ?? 0,
                    double.parse(all[index]["expected"].toString())),
                style:
                    TextStyle(color: Colors.grey.shade400, fontSize: fontSmall),
              ),
            ],
          ),
          LinearPercentIndicator(
              // width: MediaQuery.of(context).size.width,
              alignment: MainAxisAlignment.start,
              animation: true,
              lineHeight: 5.0,
              animationDuration: 2500,
              percent: _inDecimal(all[index]["collected"] ?? 0,
                  double.parse(all[index]["expected"].toString())),
              backgroundColor: _inDecimal(all[index]["collected"] ?? 0,
                          double.parse(all[index]["expected"].toString())) !=
                      1
                  ? Colors.grey.shade300
                  : themeProvider.getLightColor.withOpacity(0.4),
              barRadius: const Radius.circular(3),
              progressColor: themeProvider.getLightColor),
        ],
      ),
    );
  }

  Drawer _drawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: const EdgeInsets.only(top: 30),
        children: [
          Padding(
            padding: const EdgeInsets.only(
                top: 30.0, left: 20, bottom: 25, right: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CircleAvatar(
                  backgroundColor: themeProvider.getLightColor,
                  radius: 30,
                  child: Text(
                    user.fullName!.substring(0, 1).toUpperCase(),
                    style: const TextStyle(fontSize: 30.0, color: Colors.white),
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        toCamelCase(proLoaded ? user.fullName! : ""),
                        style: TextStyle(
                            color: themeProvider.getColor,
                            fontWeight: FontWeight.bold,
                            fontSize: fontMedium),
                      ),
                      Text(
                        proLoaded ? user.email ?? "" : "",
                        style: TextStyle(
                            color: Colors.grey.shade500,
                            fontWeight: FontWeight.bold,
                            fontSize: fontMedium),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Divider(
            color: Colors.grey.shade400,
          ),
          const SizedBox(
            height: 15,
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.home_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("home"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pushReplacementNamed(context, HomeScreen.routeName,
                  arguments: HomeScreenArgs(
                      isAdmin: user.role == "admin",
                      isOnline: true,
                      role: user.role!));
              // Navigator.pop(context);
            },
          ),
          // Connectivity Test Page
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.wifi_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: const Text(
              "Connectivity Test",
              style:
                  TextStyle(fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pushNamed(context, '/connectivity_test');
            },
          ),

          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.group_add_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("add_member"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) =>
                        AddMember(args: AddMemberArgs(forEdit: false))),
              );
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.person_outline,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("my_profile"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pop(context);
              _openProfile();
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.lock_outline,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("change_password"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              Navigator.pushNamed(context, ChangePassword.routeName,
                  arguments: ChangePasswordArgs(
                      isOnline: true, role: user.role, fromDrawer: true));
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.logout,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("logout"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              PanaraConfirmDialog.show(
                context,
                title: EkubLocalization.of(context)!.translate("warning"),
                message:
                    EkubLocalization.of(context)!.translate("confirm_logout"),
                confirmButtonText:
                    EkubLocalization.of(context)!.translate("confirm"),
                cancelButtonText:
                    EkubLocalization.of(context)!.translate("cancel"),
                onTapCancel: () {
                  Navigator.pop(context);
                },
                onTapConfirm: () {
                  gotoSignIn(context);
                },
                imagePath: warningDialogIcon,

                panaraDialogType: PanaraDialogType.warning,
                barrierDismissible:
                    false, // optional parameter (default is true)
              );
            },
          ),
        ],
      ),
    );
  }

  _inDecimal(double collected, double expected) {
    var dec = collected / expected;
    if (dec > 1) {
      dec = 1;
    }
    if (dec.isNaN) {
      return 0.toDouble();
    }

    return dec;
  }

  String _inPercent(double collected, double expected) {
    double val = _inDecimal(collected, expected) * 100;
    String p = "%";
    String res = val.toString();
    String result = res.split(".")[0];
    return result + p;
  }

  final formatCurrency = NumberFormat("#,##0.00", "en_US");
}
