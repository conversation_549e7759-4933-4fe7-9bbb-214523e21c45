import 'package:ekub/screens/comming_soon.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';

class EqubFrequencySelection extends StatelessWidget {
  final Function(String) onFrequencySelected;

  EqubFrequencySelection({required this.onFrequencySelected});

  @override
  Widget build(BuildContext context) {
    final frequencyOptions = [
      {'label': 'Daily', 'icon': Icons.calendar_today},
      {'label': 'Weekly', 'icon': Icons.calendar_view_week},
      {'label': 'Monthly', 'icon': Icons.calendar_month},
    ];
    final themeProvider = Provider.of<ThemeProvider>(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Frequency'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: StaggeredGrid.count(
          crossAxisCount: 2,
          mainAxisSpacing: 10,
          crossAxisSpacing: 10,
          children: List.generate(frequencyOptions.length, (index) {
            final isLastItem = index == frequencyOptions.length - 1;
            final frequency = frequencyOptions[index]['label'] as String;
            final icon = frequencyOptions[index]['icon'] as IconData;

            return StaggeredGridTile.count(
              crossAxisCellCount:
                  isLastItem && frequencyOptions.length.isOdd ? 2 : 1,
              mainAxisCellCount: 1,
              child: GestureDetector(
                onTap: () {
                  if (frequency == 'Weekly' || frequency == 'Monthly') {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ComingSoonPage(),
                      ),
                    );
                  } else {
                    onFrequencySelected(frequency);
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: themeProvider.getLightColor, // Example color
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.5),
                        spreadRadius: 0.1,
                        blurRadius: 2,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        color: Colors.white,
                        size: 40,
                      ),
                      const SizedBox(height: 10),
                      const SizedBox(height: 10),
                      Text(
                        frequency,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}
