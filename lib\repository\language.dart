import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppLanguage extends ChangeNotifier {
  Locale _appLocale = const Locale('en', 'US');

  Locale get appLocale => _appLocale;

  // Helper method to get the country code for a language
  String? _getCountryCodeForLanguage(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'US';
      case 'am':
        return 'ET';
      case 'es': // Tigrinya
      case 'fr': // Oromo
      case 'tl': // Somali
        return ''; // These languages don't need country codes
      default:
        return null;
    }
  }

  Future<void> fetchLocale() async {
    var prefs = await SharedPreferences.getInstance();
    if (prefs.getString('language_code') == null) {
      _appLocale = const Locale('en', 'US');
      return;
    }

    String languageCode = prefs.getString('language_code')!;
    String? countryCode = prefs.getString('country_code');

    // If country code is empty but we need one for this language
    if ((countryCode == null || countryCode.isEmpty) &&
        (languageCode == 'en' || languageCode == 'am')) {
      countryCode = _getCountryCodeForLanguage(languageCode);
    }

    _appLocale = Locale(languageCode, countryCode);
    notifyListeners();
  }

  Future<void> changeLanguage(Locale locale) async {
    if (_appLocale == locale) return;

    // Ensure we're using the correct locale format
    // For Amharic, we need to use 'am' not 'am_ET' to match the translation file
    String languageCode = locale.languageCode;
    String? countryCode = locale.countryCode;

    var prefs = await SharedPreferences.getInstance();

    // If it's Amharic with country code, adjust it
    if (languageCode == 'am' && countryCode == 'ET') {
      // Use just 'am' to match the translation file name
      _appLocale = const Locale('am');
      await prefs.setString('language_code', 'am');
      await prefs.setString('country_code', '');
    } else {
      _appLocale = locale;
      await prefs.setString('language_code', locale.languageCode);
      await prefs.setString('country_code', locale.countryCode ?? '');
    }

    notifyListeners();
  }

  // This method is now defined above

  bool get isEnglish => _appLocale.languageCode == 'en';
}
