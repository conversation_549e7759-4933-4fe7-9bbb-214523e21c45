import 'package:ekub/utils/colors.dart';
import 'package:flutter/material.dart';

class ComingSoonApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Coming Soon',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: ComingSoonPage(),
    );
  }
}

class ComingSoonPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Coming Soon'),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.hourglass_empty,
              size: 100,
              color: ColorProvider().primaryOrange,
            ),
            SizedBox(height: 20),
            Text(
              'Coming Soon!',
              style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: ColorProvider().primaryOrange),
            ),

            const SizedBox(height: 20),
            Text(
              'Stay tuned for updates!',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 40),
            // ElevatedButton(
            //   onPressed: () {
            //     // Add your action here, like navigating to a contact form or social media
            //   },
            //   child: Text('Notify Me'),
            //   style: ElevatedButton.styleFrom(
            //     foregroundColor: Colors.white,
            //     backgroundColor: Colors.blue,
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
