// ignore_for_file: prefer_final_fields, use_build_context_synchronously

import 'dart:async';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/js_authentication.dart';
import 'package:ekub/utils/js_payment_executor.dart';
// // import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../exports/screens.dart';
import '../../repository/ekub_localization.dart';
import '../../repository/language.dart';
import '../../repository/user_repos.dart';
import '../../utils/network.dart';
import '../themes/ThemeProvider.dart';

class WelcomeScreen extends StatefulWidget {
  static const routeName = "/welcome";

  const WelcomeScreen({super.key, required LoginScreenArgs args});
  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  bool isOffline = false;
  bool _isLocked = false;
  var invisible = true;
  bool isSubmitted = false;
  int loginAttempts = 0;
  late Timer _lockTimer;
  bool _onProcess = false;
  List<String> languages = ["English", "አማርኛ", "Oromic", "ትግሪኛ", "Somaali"];
  String selectedLanguage = "Language";
  String language = "";
  late ThemeProvider themeProvider;
  final Map<String, dynamic> _doctor = {};
  final _loginFormKey = GlobalKey<FormState>();
  final passwordControl = TextEditingController();
  final emailControl = TextEditingController();
  late StreamSubscription _connectionChangeStream;
  final dropdownControl = TextEditingController();
  // AppLanguage appLanguage = AppLanguage();
  // FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  // Future<String> getToken() async {
  //   final token = await _firebaseMessaging.getToken();
  //   return token.toString();
  // }

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  @override
  void initState() {
    _onProcess = false;
    NetworkManager connectionStatus = NetworkManager.getInstance();
    _connectionChangeStream =
        connectionStatus.connectionChange.listen(connectionChanged);
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    getAttempt();
    super.initState();
  }

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  @override
  dispose() {
    _connectionChangeStream.cancel();
    _onProcess = false;
    super.dispose();
  }

  String? attempts = "0";
  getAttempt() async {
    var sender = AuthDataProvider(httpClient: http.Client());
    attempts = await sender.getloginAttempts();
    setState(() {
      loginAttempts = int.parse(attempts ?? "0");
    });

    // Removed AppLanguage fetch and language setting logic
    // await appLanguage.fetchLocale();
    // language = appLanguage.appLocal.languageCode.toLowerCase();

    // Removed language setting based on appLanguage
    // if (language == "fr") {
    //   setState(() {
    //     selectedLanguage = "Oromic";
    //   });
    // }
    // if (language == "es") {
    //   setState(() {
    //     selectedLanguage = "ትግሪኛ";
    //   });
    // }
    // if (language == "am") {
    //   setState(() {
    //     selectedLanguage = "አማርኛ";
    //   });
    // } else if (language == "tl") {
    //   setState(() {
    //     selectedLanguage = "Somaali";
    //   });
    // } else if (language == "en") {
    //   setState(() {
    //     selectedLanguage = "English";
    //   });
    // }
  }

  @override
  Widget build(BuildContext context) {
    // Removed AppLanguage provider access
    // var appLanguage = Provider.of<AppLanguage>(context);
    var language = EkubLocalization.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBar(backgroundColor: Colors.white, elevation: 0, actions: [
        // Container(
        //   margin: const EdgeInsets.only(right: 10),
        //   width: 130,
        //   child: CustomDropdown<String>(
        //     onChanged: (value) {
        //       setState(() {
        //         selectedLanguage = value!;
        //         if (selectedLanguage == "English") {
        //           appLanguage.changeLanguage(const Locale("en"));
        //         } else if (selectedLanguage == "አማርኛ") {
        //           appLanguage.changeLanguage(const Locale("am_ET"));
        //         } else if (selectedLanguage == "Oromic") {
        //           appLanguage.changeLanguage(const Locale("fr"));
        //         } else if (selectedLanguage == "Somaali") {
        //           appLanguage.changeLanguage(const Locale("tl"));
        //         } else if (selectedLanguage == "ትግሪኛ") {
        //           appLanguage.changeLanguage(const Locale("es"));
        //         }
        //       });
        //     },
        //     decoration: CustomDropdownDecoration(
        //       closedFillColor: Colors.white,
        //       closedSuffixIcon: Icon(
        //         Icons.language,
        //         color: bodyTextColor,
        //         size: 25,
        //       ),
        //       hintStyle: TextStyle(
        //           color: themeProvider.getColor, fontWeight: boldFont),
        //       // selectedStyle: TextStyle(
        //       //     color: themeProvider.getColor, fontWeight: boldFont),
        //     ),
        //     hintText: selectedLanguage,
        //     items: languages,
        //     excludeSelected: true,
        //   ),
        // )
      ]),
      body: SafeArea(
        child: Container(
          width: size.width,
          height: size.height,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Logo Section
              Image.asset(
                'assets/icons/icon.png', // Replace with your logo path
                height: 200,
                width: 200,
              ),
              const SizedBox(height: 20),

              // Title Section
              const Text(
                "Virtual Equb",
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),

              // Subtitle Section
              Text(
                "Make Your Dreams a Reality with Virtual Equb",
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),

              // Sign In Button
              _align(language),
            ],
          ),
        ),
      ),
    );
  }

  Align _align(EkubLocalization language) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // Container(
          //     margin: const EdgeInsets.fromLTRB(20, 20, 20, 10),
          //     padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          //     child: _emailAndPassword(language)),
          Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10)),
            // margin: const EdgeInsets.fromLTRB(20, 30, 20, 10),
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 10,
                ),
                _startLoginProcess(language),
                const SizedBox(
                  height: 10,
                ),
                // _newUser(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Column _emailAndPassword(EkubLocalization language) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            language.translate("phone_number"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          maxLength: 9,
          controller: emailControl,
          keyboardType: TextInputType.phone,
          style: lableStyle,
          decoration: InputDecoration(
              border: const OutlineInputBorder(
                  borderSide: BorderSide(style: BorderStyle.solid)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide:
                    BorderSide(color: themeProvider.getColor.withOpacity(0.3)),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              fillColor: Colors.white,
              filled: true,
              counterText: "",
              floatingLabelBehavior: FloatingLabelBehavior.never,
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.phone_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              prefix: const Text(
                "+251 ",
              ),
              labelText: '+2519-09-89-89-21',
              labelStyle: hintStyle),
          validator: (value) {
            if (value!.isEmpty) {
              return language.translate("phone_empty_member");
            } else if (value.length < 9) {
              return language.translate("phone_length");
            }
            return null;
          },
          onSaved: (value) {
            _doctor["phone_number"] = "+251${value!}";
          },
        ),
        Padding(
          padding: const EdgeInsets.only(left: 8.0, top: 20),
          child: Text(
            language.translate("password"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 0),
          child: TextFormField(
            style: lableStyle,
            controller: passwordControl,
            obscureText: invisible,
            decoration: InputDecoration(
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              border: const OutlineInputBorder(
                  borderSide: BorderSide(style: BorderStyle.solid)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide:
                    BorderSide(color: themeProvider.getColor.withOpacity(0.3)),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.lock_outline,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              labelText: "********",
              labelStyle: hintStyle,
              fillColor: Colors.white,
              filled: true,
              floatingLabelBehavior: FloatingLabelBehavior.never,
              suffix: GestureDetector(
                onTap: _onProcess ? null : changeSate,
                child: Icon(
                  invisible ? Icons.visibility : Icons.visibility_off,
                  color: themeProvider.getColor,
                  size: 15,
                ),
              ),
            ),
            validator: (value) {
              if (value!.isEmpty) {
                return EkubLocalization.of(context)!
                    .translate("password_empty");
              } else if (value.length < 6) {
                return EkubLocalization.of(context)!
                    .translate("password_length");
              } else if (value.length > 25) {
                return EkubLocalization.of(context)!
                    .translate("password_limit");
              } else {
                return null;
              }
            },
            onSaved: (value) {
              _doctor["password"] = value;
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            InkWell(
              borderRadius: BorderRadius.circular(20),
              splashColor: Colors.amber,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PhoneVerification(
                      status: "forgotPassword",
                    ),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
                child: Text(
                  "${language.translate("forget_password")} ?",
                  style: TextStyle(
                      color: themeProvider.getColor,
                      fontWeight: boldFont,
                      fontSize: fontMedium),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  _startLoginProcess(EkubLocalization language) {
    return ElevatedButton(
      onPressed: _onProcess
          ? null
          : () async {
              setState(() {
                _onProcess = true;
              });
              if (!await InternetConnectivity()
                  .checkInternetConnectivty(context, true)) {
                setState(() {
                  _onProcess = false;
                });
                return;
              } else {
                _showLoadingDialog(context);
                _loginUser(context);
              }
            },
      child: SizedBox(
        height: 60,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            // EkubLocalization.of(context)!.translate("sign_in")
            Text("Continue with telebirr", style: buttonText),
            const Spacer(),
            Align(
              widthFactor: 2,
              alignment: Alignment.centerRight,
              child: _onProcess
                  ? const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                    )
                  : Container(),
            ),
          ],
        ),
      ),
    );
  }

  static void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Container(
            width: 120, // Small square size
            height: 120, // Matches width
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  color: secondaryColor,
                  strokeWidth: 3, // Slightly thinner stroke for small size
                ),
                const SizedBox(height: 12), // Reduced spacing
                Text(
                  'Loading...',
                  style: Theme.of(context).textTheme.titleMedium,
                  // style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  //       fontSize: 12, // Smaller text
                  //     ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  _newUser() {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: SizedBox(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                "${EkubLocalization.of(context)!.translate("don't_have_account")}? ",
                style: TextStyle(
                    fontSize: fontMedium,
                    color: bodyTextColor,
                    fontWeight: normalFontWeight),
              ),
              InkWell(
                borderRadius: BorderRadius.circular(20),
                splashColor: Colors.amber,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          PhoneVerification(status: "newUser"),
                    ),
                  );
                },
                child: Text(
                  EkubLocalization.of(context)!.translate("sign_up"),
                  style: TextStyle(
                      color: themeProvider.getLightColor,
                      fontWeight: boldFont,
                      fontSize: fontMedium),
                ),
              ),
            ],
          ),
        ));
  }

  _loginUser(BuildContext context) {
    if (!isOffline) {
      prepareRequest(context, loginAttempts);
    } else {}
  }

  void prepareRequest(BuildContext context, int loginAttempts) async {
    if (_isLocked) {
      PanaraInfoDialog.show(
        context,
        title: EkubLocalization.of(context)!.translate("blocked"),
        message: EkubLocalization.of(context)!.translate("blocked_msg"),
        buttonText: EkubLocalization.of(context)!.translate("okay"),
        onTapDismiss: () {
          Navigator.pop(context);
        },
        imagePath: warningDialogIcon,
        panaraDialogType: PanaraDialogType.warning,
      );
    } else {
      var login = await JSPaymentExecutor.executeJavaScriptAutologin(context);
    }
    setState(() {
      _onProcess = false;
    });
  }

  _openHomeScreen(bool isAdmin, String role) async {
    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);

    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }

  _checkLogin(String message) {
    var sender = AuthDataProvider(httpClient: http.Client());

    setState(() {
      loginAttempts++;

      sender.setloginAttempts(loginAttempts.toString());
    });
    if (loginAttempts > 5) {
      _lockAccount();
    } else {
      PanaraInfoDialog.show(
        context,
        message: message,
        buttonText: EkubLocalization.of(context)!.translate("okay"),
        onTapDismiss: () {
          Navigator.pop(context);
        },
        imagePath: errorDialogIcon,
        panaraDialogType: PanaraDialogType.error,
      );
    }
  }

  void _lockAccount() {
    _isLocked = true;
    PanaraInfoDialog.show(
      context,
      title: "${EkubLocalization.of(context)!.translate("blocked")} !",
      message: EkubLocalization.of(context)!.translate("blocked_msg"),
      buttonText: EkubLocalization.of(context)!.translate("okay"),
      onTapDismiss: () {
        Navigator.pop(context);
      },
      imagePath: warningDialogIcon,
      panaraDialogType: PanaraDialogType.warning,
    );
    _lockTimer = Timer(const Duration(minutes: 5), () {
      _unlockAccount();
    });
  }

  void _unlockAccount() {
    _isLocked = false;
    _lockTimer.cancel();
    var sender = AuthDataProvider(httpClient: http.Client());
    setState(() {
      loginAttempts = 0;
      sender.setloginAttempts("0");
    });
    PanaraInfoDialog.show(
      context,
      title: EkubLocalization.of(context)!.translate("unlocked"),
      message: EkubLocalization.of(context)!.translate("unlocked_msg"),
      buttonText: EkubLocalization.of(context)!.translate("okay"),
      onTapDismiss: () {
        Navigator.pop(context);
      },
      panaraDialogType: PanaraDialogType.normal,
    );
  }
}
