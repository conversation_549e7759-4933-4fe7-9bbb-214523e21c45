{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9864f150b8432f17d4cb38c3dfec6066e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810bdb3018dba7b3fc87b085e5c7a18ec", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fff1cb93f8a6477fbe45c40de547071d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808213667fea3cf0e28f6feb0339126c5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fff1cb93f8a6477fbe45c40de547071d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892b119b956a581fc1e399b4ce8515719", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983180d4497ed1424649cc6d2725aad777", "guid": "bfdfe7dc352907fc980b868725387e9849c2bbdfb92a37264644bfdb6f39d4be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e8ec1ddd9de73ae18b9b121f97ce36", "guid": "bfdfe7dc352907fc980b868725387e98f9dbadbf407630120c8921892415ad41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfa61e28738a5bf3cde0bedc11a8158d", "guid": "bfdfe7dc352907fc980b868725387e9857b7416e38a5a8af0949225f5c53dd82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845902a35fc3cf0ffe5d8d71d559d5326", "guid": "bfdfe7dc352907fc980b868725387e98bc834cd36c5e3c383d22bb31e7d37dc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987830440cfea84880e4b69dd645f6bf84", "guid": "bfdfe7dc352907fc980b868725387e9879835c21dac4e8cb24727f112474fb74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c9c9967eb41ddcec133b4f51eefe32", "guid": "bfdfe7dc352907fc980b868725387e98f1f7f176296122a7f4f8c18bb60ed8d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cbfad25a3fdcf2589afc924d0c2eea9", "guid": "bfdfe7dc352907fc980b868725387e98ee4a09254a82380b0d24ae1d9c5356b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869660172554d5ff8b788866b6fa9fccc", "guid": "bfdfe7dc352907fc980b868725387e98daa0ec84f1d33a4f929dcecd0ad9693c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e192d4caf92cb00ecd0d627aa495de95", "guid": "bfdfe7dc352907fc980b868725387e98db02f333f3b1fa6f87de44a6af22655d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98213999fc7d1a475a1392751b16eec17c", "guid": "bfdfe7dc352907fc980b868725387e98d4932a53abf06b9847975a55ad32f157", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cef3d227ba5d08521cf81355dce014e", "guid": "bfdfe7dc352907fc980b868725387e98957740a6a2f8878f837c03c1b1185ba5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e423b086afc9c11a3bc698374f75fe81", "guid": "bfdfe7dc352907fc980b868725387e984881bfea083ad442c45634f9bbaf6cc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e62948c586b5770976a313cacea7e13", "guid": "bfdfe7dc352907fc980b868725387e9875abc8b4f55f7643dc64d5796186e603", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872f1c80bb11bd6b950ff986b5e05ad84", "guid": "bfdfe7dc352907fc980b868725387e98723d1717c6510cb6c67f8b6fd65bd66d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff45070974f2a402fa5d8d0b1f74183f", "guid": "bfdfe7dc352907fc980b868725387e98e5695bb167d2708c9cb9bd589291a4b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984600ed172973dc37accf47b9a7ead73e", "guid": "bfdfe7dc352907fc980b868725387e9853c3de001d6194b521547405eae33793", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c605c4331d545986fd2fdd000c28af8", "guid": "bfdfe7dc352907fc980b868725387e98874056eda877f607d282dfd457fdc7b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987375a14450c9f5d171f32e619c39d62f", "guid": "bfdfe7dc352907fc980b868725387e981e18ae0dedae513b271f22bc6c2dc6d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebf1d5692823a18c0d313b2cf09be656", "guid": "bfdfe7dc352907fc980b868725387e9800291a066e506384ac7b5257094c2631", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f1f99f82a43a6b1382208e99ce83bf3", "guid": "bfdfe7dc352907fc980b868725387e98b91dfaec0b30e36f7abec6816e2cb952", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981688b211278f4bdbf9f9d233021283f2", "guid": "bfdfe7dc352907fc980b868725387e98c1da18c0887fd0ccb9365a7fe815c3ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cbf26ce40eadcd48310339ecbe962de", "guid": "bfdfe7dc352907fc980b868725387e98474bb346df609ddbd419cb8e60660c89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98385986feb88f662aea51562f9b268746", "guid": "bfdfe7dc352907fc980b868725387e987c6cdd73400cb3e2668fa484c121224e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c30ca91174d7e168eaefa0733b03cae3", "guid": "bfdfe7dc352907fc980b868725387e982995237075f66627be5a5705fdf9dbc7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b809dbfa4c6712e1c19e51590af7fa04", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986718a5e0c19a4eccabb879fbf220a258", "guid": "bfdfe7dc352907fc980b868725387e98b79dc1fc6d54725e3d38d9e3116075bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f9861d6605c3792c7e219600f7a0af3", "guid": "bfdfe7dc352907fc980b868725387e98f2e7ce9aa967a78bb896ad365099d560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b19057e7e186ba7e65db5ec004de6c6", "guid": "bfdfe7dc352907fc980b868725387e98f790ad54b746f3a60dc61fb04ebf71ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e48810ec79f778c836f7f1c7ad901c7", "guid": "bfdfe7dc352907fc980b868725387e98ab6a92dff7cd5c8bff6f4f5facb34166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891d5d820341694d65398300e1b8be7bd", "guid": "bfdfe7dc352907fc980b868725387e985c9e5877ea5799e543aec9e8816d8345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a23964269fa426e1d9901f1897a18fd2", "guid": "bfdfe7dc352907fc980b868725387e98df81a735fe35b8f2ac61e346b8ab26a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb90772c82c99b50abfac7836cd2f545", "guid": "bfdfe7dc352907fc980b868725387e98c2ddc120f5e99a3d5519108905adbf77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a62cdeb1d5513d980441338973a2b8", "guid": "bfdfe7dc352907fc980b868725387e9834f913396dd06ce4db6de1954651e9c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c38d6953117953006489fed3639aa6", "guid": "bfdfe7dc352907fc980b868725387e98f2b6aef184d1abf967a3c90a74ac1fed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7bf18b40fcc42e4310eda8512ed5dfa", "guid": "bfdfe7dc352907fc980b868725387e9807ae561113ab8023c286f926ac89943d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ad2ba76d3920dc2571a42a7bb369b6e", "guid": "bfdfe7dc352907fc980b868725387e9811a2f5e15b7abc36c104b5a918efad99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812eaca8bfeeb55cfe4ba189513299436", "guid": "bfdfe7dc352907fc980b868725387e98db5ea2cc360c1891df59db5af69cefd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed11a91f5c56d94e0d80eee115ae3237", "guid": "bfdfe7dc352907fc980b868725387e98a4a6bbc3d8fc9516436beda6dad8541a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b69d2ad6e94241f52db105601d65f847", "guid": "bfdfe7dc352907fc980b868725387e9815b21e1a576646f4f6f353ebf5d8dd41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863f1c2cbc063edbfb82c07709beb5902", "guid": "bfdfe7dc352907fc980b868725387e986e68bda176abf1036ae84e6d7c78dc8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca7378b2ab48a01847d68bc9a10c60d9", "guid": "bfdfe7dc352907fc980b868725387e98208a7e1377c703d467722c88a122b379"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f67df19dfbc832a22c1d728e4f482dc", "guid": "bfdfe7dc352907fc980b868725387e98c886baa7c330b0534c503e3b06e5ab08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98007200f57597db2ca06ed3468d74a78f", "guid": "bfdfe7dc352907fc980b868725387e9873b5c06dad9ce022b0689ef504a89c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f593cd221df0e7e0c3829517d46484a0", "guid": "bfdfe7dc352907fc980b868725387e980dbd3b3babea95c261d6e95ce5f96f86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a98762a928785ecd666ecbdfde78cd9", "guid": "bfdfe7dc352907fc980b868725387e98ef7820a760791d3dc99f9f4f7d86e30e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e36e0427a440906dfc8706120860a3f1", "guid": "bfdfe7dc352907fc980b868725387e98c1dc0cb6ed46e790d399c8a4957a146b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eb89d850707711391079d03d5eaab73", "guid": "bfdfe7dc352907fc980b868725387e98c3abfe24b0aa34aa8aa9ff552c2f7e95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812676cb6ca590ef116ace5aeee97dd4b", "guid": "bfdfe7dc352907fc980b868725387e989980e3343a39ecfb8cc0522bda25f465"}], "guid": "bfdfe7dc352907fc980b868725387e98295a6a912ea2f996619108451429afc3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a15b41da5b9cf00eaf9e8a12fb6fc89a", "guid": "bfdfe7dc352907fc980b868725387e98d69d3f754ff2570528c4de0715d27ea9"}], "guid": "bfdfe7dc352907fc980b868725387e986ff48b1d20b26e526e96f1291209d3a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ca04b2f98dda4aa3ea7d695df2288e55", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98b319dc9b9bc94b538d198b41cff1b6d9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}