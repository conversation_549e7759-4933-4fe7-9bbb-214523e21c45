{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a3c1ca71646a312b8ce47e5c0fcdfa29", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f292fa1a2e602e72cbae03ca4381d0ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869873f5694a44e2370536e4c133717e2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c2c7bb83ef85df2b5738875e12a3b9e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869873f5694a44e2370536e4c133717e2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802786e96202a989faf8737401d76d959", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9897c000044e981e8669d0b6d89a0af87c", "guid": "bfdfe7dc352907fc980b868725387e98e37596068807202609d79cb2ba55aed5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d999e5632daacf18ab1bfbcaf0833c", "guid": "bfdfe7dc352907fc980b868725387e98f347fe2256ffb0d406b16a100335440c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98007cba181cfaab851432283717820c37", "guid": "bfdfe7dc352907fc980b868725387e980bb8cfd586aad425185870f74ce7a2cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980071a336a3dddc01870bf8023ee8e4dc", "guid": "bfdfe7dc352907fc980b868725387e98922a609a7a779b99b95f6a6a07cd1448"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986261ea303a3ef394f38eaea7915d19d1", "guid": "bfdfe7dc352907fc980b868725387e984224ead50c1c6cc8e468cf55d4928324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987708294a54824a7386fe44783d00450f", "guid": "bfdfe7dc352907fc980b868725387e981b5b5ba5bfab73c28722b20cb531bdba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c45a463efa3cb7960e028a78314a7ab4", "guid": "bfdfe7dc352907fc980b868725387e9807f696e2f3850a30c238647ab34c7d6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98771b445d6b311af715de444add43a84a", "guid": "bfdfe7dc352907fc980b868725387e983430432858a5c82423b86d6e147ee29c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbc260b0fe696f878646be9af1c6f7c8", "guid": "bfdfe7dc352907fc980b868725387e98b5809c52dbb14b8f329e14a04ad55180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889c6304675a34c2145a3611afab7027b", "guid": "bfdfe7dc352907fc980b868725387e9858b704f6de85105c4b0351c63e9876a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861acb01d08734303785ea24b9baa0042", "guid": "bfdfe7dc352907fc980b868725387e981c6c87a31575b88e11b411ed31040992", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daae2cee363926edb2298002ddb9425e", "guid": "bfdfe7dc352907fc980b868725387e9893af6cd0592a538e90476661a84e3037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a67664866d2ebfed315f57d2826c359d", "guid": "bfdfe7dc352907fc980b868725387e987bccb7d2f7b224860a7e18a12c02ec4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868898419dda699ebbd68ef123311ecbc", "guid": "bfdfe7dc352907fc980b868725387e980ad7eda625b06b980f6d242a1774a7a4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9732a048f02ac03affb4b4fa7b710bf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fff8ecb4be912c913b7b3437959d7bc0", "guid": "bfdfe7dc352907fc980b868725387e98db6db7db22361c5e7722392e4da662e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98507863edf002d84db6c6448a1a1779b1", "guid": "bfdfe7dc352907fc980b868725387e98f37037bc31d844f72a3647e902a9ab73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804f6bdc97fec50c4b785b5513d5e870c", "guid": "bfdfe7dc352907fc980b868725387e98c2b532744b022f108a705695da2f6786"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98419b3e2246604bcb15ef290bfb07425c", "guid": "bfdfe7dc352907fc980b868725387e9804bcd32ada162fc79b6a1bc8a9513fe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814e4718e72548a29a8107cc48b51d405", "guid": "bfdfe7dc352907fc980b868725387e98b653f9dd0691029ac2291903e9622573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b87195f4b41b764eafe3cfe19588ba6", "guid": "bfdfe7dc352907fc980b868725387e98d2c249c8db0ea92eacd5c9cf2c1aae44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba67827e05393df709aabe5f4ce18fe", "guid": "bfdfe7dc352907fc980b868725387e9896ef49c0e2e0bfe1e26758e0f6f6b7a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afcb5ada25bbf950dac9b06e2ab7fbd2", "guid": "bfdfe7dc352907fc980b868725387e985581e2c02f178c4ad00e3031c1c985df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b4d1b031ccb5024edc7734da601a1ad", "guid": "bfdfe7dc352907fc980b868725387e9831f17952ed6c01dbefb9fa3193164dd3"}], "guid": "bfdfe7dc352907fc980b868725387e98964da8c41b24636d9d338de21fb08fe2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a15b41da5b9cf00eaf9e8a12fb6fc89a", "guid": "bfdfe7dc352907fc980b868725387e98015c062afdb30ba698f98b6ea63a1d36"}], "guid": "bfdfe7dc352907fc980b868725387e9856f337a0270b8ff9a09062fba0a9859f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98301a0f773483e7a62c7a59d2ce137d3c", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e9877cd28b09d57fbb5f9abe0ec343ccf66", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}