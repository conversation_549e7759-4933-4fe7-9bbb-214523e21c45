// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

@immutable
class User extends Equatable {
  String? token;
  String? id;
  String? fullName;
  String? phoneNumber;
  String? password;
  String? email;
  double? rating;
  int? balance;
  String? gender;
  String? role;
  String? memberId;

  User(
      {this.id,
      this.token,
      this.email,
      this.fullName,
      required this.phoneNumber,
      this.password,
      this.balance,
      this.memberId,
      this.gender,this.role});
  @override
  List<Object?> get props => [phoneNumber, password];

  factory User.fromStorage(Map<String, dynamic> storage) {
    return User(
        id: storage["id"],
        token: storage["token"],
        phoneNumber: storage["phone_number"],
        fullName: storage['full_name'],
        email: storage["email"],
        memberId: storage["member_id"],
        role: storage["role"]);
  }

  @override
  String toString() => 'User {fullName: $fullName \n Phone Number: $phoneNumber \n'
      'email: $email\n role: $role\n memberId: $memberId}';
}
