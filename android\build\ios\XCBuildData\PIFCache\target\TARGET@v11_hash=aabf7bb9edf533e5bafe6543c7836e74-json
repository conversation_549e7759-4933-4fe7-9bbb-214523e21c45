{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dddedc137fb092d0ab5c6f149700ef3d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection-prefix.pch", "INFOPLIST_FILE": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "jailbreak_root_detection", "PRODUCT_NAME": "jailbreak_root_detection", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ce82e252e69571e6bbe5b24e3e9d4a16", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f0da793d6d935998164fa240dafc02cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection-prefix.pch", "INFOPLIST_FILE": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "jailbreak_root_detection", "PRODUCT_NAME": "jailbreak_root_detection", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98048ae14918440434900187220687f4c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f0da793d6d935998164fa240dafc02cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection-prefix.pch", "INFOPLIST_FILE": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/jailbreak_root_detection/jailbreak_root_detection.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "jailbreak_root_detection", "PRODUCT_NAME": "jailbreak_root_detection", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816a8a6590c5df9d2a4474386ad561757", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c73ed2b65e37224e723004dfb78fb9bc", "guid": "bfdfe7dc352907fc980b868725387e980a70c8d9d13932b947ddf9fea7496fcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b52f2d300346d5b6e6965e58d6dadc4b", "guid": "bfdfe7dc352907fc980b868725387e980306a1a9950b3a96fe1e348156d0ceae", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c924bc517710d7c603ea228396ca074b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d78d1a495f7852fff932683b8d29ee37", "guid": "bfdfe7dc352907fc980b868725387e98aa3964ac75e46482add8cbd8257813a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988066154f1d2bd2754371f5aca4a2026a", "guid": "bfdfe7dc352907fc980b868725387e9838a7234a40e43418f7768918fd368a53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce33018be02ba5ec1fcaa2816885b7b0", "guid": "bfdfe7dc352907fc980b868725387e985cdb193020839dee36094b7ab11729b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831ae3c76724f4eb75eda71e9c6988f6b", "guid": "bfdfe7dc352907fc980b868725387e98792bbf02f9aa021bcb2ca614f6c41101"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c86c710ce4931b3679196385f0eb92d", "guid": "bfdfe7dc352907fc980b868725387e987179aaaeb2a45696b03321e82f772698"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a24ed5f348fcc39ad34a72f2a48b971e", "guid": "bfdfe7dc352907fc980b868725387e9873119c8908427e61d981d08c8e2f01a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808597182f25f6caee98d1cc73ff29d9f", "guid": "bfdfe7dc352907fc980b868725387e982f6855a10a082e06e624104ac2219f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4d8c9953f6bfec2bf3d3ec0972ece4b", "guid": "bfdfe7dc352907fc980b868725387e9832258065594377177df9656e6b442152"}], "guid": "bfdfe7dc352907fc980b868725387e98049fe52b2a88020ffdd13e0c67ab5211", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a15b41da5b9cf00eaf9e8a12fb6fc89a", "guid": "bfdfe7dc352907fc980b868725387e98176e2def4b00ab1158a74467debf0ae2"}], "guid": "bfdfe7dc352907fc980b868725387e985c0898885477f4ea33e572672e7727ed", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d8b637660b182e514f4426769c26c313", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e980c47d9cc97d9fb26be834bb86d7b9565", "name": "IOSSecuritySuite"}], "guid": "bfdfe7dc352907fc980b868725387e98a09adf1a2db886d74f3421b8cd0b9a0f", "name": "jailbreak_root_detection", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987c55486778267749743c7c852178d579", "name": "jailbreak_root_detection.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}