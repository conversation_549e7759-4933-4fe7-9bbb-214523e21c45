import 'package:flutter/material.dart';
import 'package:panara_dialogs/panara_dialogs.dart';

import '../../../utils/device.dart';
import '../../ui_kits/app_bar.dart';
import '../../ui_kits/list_view.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  final _appBar = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          EkubAppBar(key: _appBar, title: "Notification", widgets: const []),
      body: SingleChildScrollView(
          child: Column(children: [
        SizedBox(
          height: Device.body(context),
          width: MediaQuery.of(context).size.width,
          child: ListView(
              padding: EdgeInsets.zero,
              scrollDirection: Axis.vertical,
              children: List.generate(15, (int index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: GestureDetector(
                    onTap: () {
                      PanaraInfoDialog.show(
                        context,
                        message: "Hello! there is no message for you",
                        buttonText: "Okay",
                        onTapDismiss: () {
                          Navigator.pop(context);
                        },
                        panaraDialogType: PanaraDialogType.normal,
                      );
                    
                    },
                    child: ListCard(
                      svgSrc: "assets/icons/Documents.svg",
                      title: "Documents Files",
                      amountOfFiles: "230 Birr",
                      numOfFiles: 1328 + index,
                      icon: Icons.notifications,
                      theme: Theme.of(context).primaryColor,
                    ),
                  ),
                );
              })),
        ),
      ])),
    );
  }
}
