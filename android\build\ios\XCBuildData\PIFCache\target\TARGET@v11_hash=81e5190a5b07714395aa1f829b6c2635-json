{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808537e94303435bb54b9db3af37b4310", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e3e2049827a806a1333b438c6492aaf4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fffcd4dbfd8442f8d1e0b3cd29f2a285", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a1e9946e4bbe9bd9bb8ecb0d135e8fdd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fffcd4dbfd8442f8d1e0b3cd29f2a285", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f31aec525fe0c257c4ec6345383eb30", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f34356c9c9e450759266ede08f904005", "guid": "bfdfe7dc352907fc980b868725387e98b4488b52d0b51b7a177920a9402bb567", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa8c096085f0e21ae5dd691b2567387", "guid": "bfdfe7dc352907fc980b868725387e98cfbaad0ae8b7916ccbc96de4c95299cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f70538118b9227d7a7124fffe1ef0b", "guid": "bfdfe7dc352907fc980b868725387e984376b2eb66135a87a53541186fc066c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f00dc51659f400d598a6fe0628a568d7", "guid": "bfdfe7dc352907fc980b868725387e989f03c0f3549ee310c6b0712acead5ddc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ce2cd2ef85b038cb1cba526cbf552c", "guid": "bfdfe7dc352907fc980b868725387e98f8bca5c66988c499a98a7f9de3ebd605", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b46bb9a1d8c5815225b8bdd95b67b593", "guid": "bfdfe7dc352907fc980b868725387e98658ab28c7691b960a19aa9beb5747460", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d14fee9ee2162eae5c8985f65e797f1", "guid": "bfdfe7dc352907fc980b868725387e9849eb45cdb2e17670112ac58d1a93a84e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ff7594d7fcaa6c345d124239980a16", "guid": "bfdfe7dc352907fc980b868725387e981efe0525a704504225e6d6c60b933c99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989691b2aad3119aa5c75a636216b9d46a", "guid": "bfdfe7dc352907fc980b868725387e98674bb82f7e23094184df7ef74dfc8581", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804118023cc88fdb149bbb44f4793a1f2", "guid": "bfdfe7dc352907fc980b868725387e987be506376e45a7d09faf17c227464572", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840f374f418020ce9171113ea9f135748", "guid": "bfdfe7dc352907fc980b868725387e986f8de82df4cf3f12a276a16f76a175e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98871e51527d3157e73f95c3276e139fc1", "guid": "bfdfe7dc352907fc980b868725387e9844a8a0d7ce238fb4af605a2f4f979f0a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989c38509ce3b7e6ea4e1732b0720d153b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98afe66efb916ef458bc376eee718420a7", "guid": "bfdfe7dc352907fc980b868725387e98fd2e144cdd9e2af55e225b7f16d239df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998c9504279227a88a292b73ad61772c", "guid": "bfdfe7dc352907fc980b868725387e986d695ce11f9a90bbcf4c4f7342bd7cf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bace933fd04f4b57377f0850c3f2d5cc", "guid": "bfdfe7dc352907fc980b868725387e98453543da3b7d52924444cad93da5c13e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989235fa30ffb1f3303305f8a2d5166f73", "guid": "bfdfe7dc352907fc980b868725387e98070f76c45906f81648a13aae0a606c9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c67a310a392891dad8a431197f7da4", "guid": "bfdfe7dc352907fc980b868725387e98d2efb8420022f779080232d56b9e460b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815799cfe5f18599234099b4e96d6c94f", "guid": "bfdfe7dc352907fc980b868725387e9859b79c4314462258644ebeb4fca10011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988186fbfb1cda4b0aeac61a08f9cc78e3", "guid": "bfdfe7dc352907fc980b868725387e987cd87142d8b8bd5b5f9f6932befd6ef2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c55faf3494d83f35094e9fa141398a", "guid": "bfdfe7dc352907fc980b868725387e9875d7bac25a44eaba4d86aecf1170f789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984735cf585c793f82daa426a6dc08d051", "guid": "bfdfe7dc352907fc980b868725387e98918891f7f69630c3ae70d6d1eedad45e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c1c25825faf02f954e2d46394ab65e5", "guid": "bfdfe7dc352907fc980b868725387e982b0a13f7cf51906ac108a966df5cfc36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f1eb0db4d7b5034e34d424aad54fd9d", "guid": "bfdfe7dc352907fc980b868725387e98ad6613eafeb53249a93a35e401df345d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986763e9d0d1e9875e638dae9cb7b8eafb", "guid": "bfdfe7dc352907fc980b868725387e98bb692668a273ce42cc81414180fef3f4"}], "guid": "bfdfe7dc352907fc980b868725387e9866c986ce25a9aea339b33823adac69f8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a15b41da5b9cf00eaf9e8a12fb6fc89a", "guid": "bfdfe7dc352907fc980b868725387e9849c4430517862e9655cfc524db7fcb8b"}], "guid": "bfdfe7dc352907fc980b868725387e98e5e82c3c0c3793c248fd6696e87e811a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cc713144cb09f0af34f847a1de6f8285", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}