// ignore_for_file: use_build_context_synchronously, unused_catch_clause

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:ekub/screens/dashboard/ekubs/equb_provider.dart';
import 'package:ekub/service/headers.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';

import '../exports/models.dart';
import '../screens/dashboard/root/root_screen.dart';
import 'ekub_localization.dart';

class EqubDataProvider with ChangeNotifier {
  final _baseUrl = RequestHeader.baseApp;
  final http.Client httpClient;
  final secureStorage = const FlutterSecureStorage();

  EqubDataProvider({required this.httpClient});
  EqubTypes _equbsHolder = EqubTypes(totalMember: 0, equbTypes: []);
  EqubTypes get equbTypes => _equbsHolder;
  Future loadMainEqubTypes(BuildContext context, int page, int offset) async {
    try {
      final response = await http
          .get(Uri.parse('$_baseUrl/mainequb'),
              headers: await RequestHeader().authorisedHeader())
          .timeout(timeout);

      debugPrint("Response from loadmainequbtype is: ${response.statusCode}");
      if (response.statusCode == 200) {
        // print("Response body data from loadmainequbtype is: ${response.body}");
        final List allEqubs = jsonDecode(response.body)['data'];
        List<EqubType> equbs =
            allEqubs.map((job) => EqubType.fromJson(job)).toList();
        _equbsHolder = EqubTypes(totalMember: equbs.length, equbTypes: equbs);

        notifyListeners();
        return _equbsHolder.equbTypes;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }

    notifyListeners();
  }

  Future loadEqubTypes(BuildContext context, int page, int offset) async {
    // Obtain the EqubProvider instance
    final equbProvider = Provider.of<EqubProvider>(context, listen: false);

    try {
      final response = await http
          .get(Uri.parse('$_baseUrl/mainequb'),
              headers: await RequestHeader().authorisedHeader())
          .timeout(timeout);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final List mainEqubs = jsonResponse['data'];

        List<EqubType> allEqubTypes = [];
        //print(   "Current Equb ID from provider from equb_repos.dart : ${equbProvider.equbId}");
        // Filter to only include the Equb where name is "Ordinary"
        var ordinaryEqub = mainEqubs.firstWhere(
          (equb) => equb['id'].toString() == equbProvider.equbId.toString(),
          orElse: () => null,
        );

        if (ordinaryEqub != null) {
          final List subEqubs = ordinaryEqub['subEqub'];
          allEqubTypes.addAll(subEqubs.map((equb) => EqubType.fromJson(equb)));

          _equbsHolder = EqubTypes(
              totalMember: allEqubTypes.length, equbTypes: allEqubTypes);

          notifyListeners();
          return _equbsHolder.equbTypes;
        } else {
          throw ('Equb with name "Ordinary" not found.');
        }
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
    } on SocketException catch (e) {
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }

    notifyListeners();
  }

  Future loadAllEqubTypes(BuildContext context, int page, int offset) async {
    // Obtain the EqubProvider instance
    //   final equbProvider = Provider.of<EqubProvider>(context, listen: false);
    try {
      final response = await http
          .get(Uri.parse('$_baseUrl/equbType'),
              headers: await RequestHeader().authorisedHeader())
          .timeout(timeout);

      if (response.statusCode == 200) {
        final List allEqubs = jsonDecode(response.body)['equbTypes'];
        List<EqubType> equbs =
            allEqubs.map((job) => EqubType.fromJson(job)).toList();
        _equbsHolder = EqubTypes(totalMember: equbs.length, equbTypes: equbs);

        notifyListeners();
        return _equbsHolder.equbTypes;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }

    notifyListeners();
  }

  Equbs _equbs = Equbs(totalMember: 0, equbs: []);
  Equbs get equbs => _equbs;

  Equbs _passedEqubs = Equbs(totalMember: 0, equbs: []);
  Equbs get passedEqubs => _passedEqubs;

  Future<Map<String, dynamic>> loadMemberEqub(
      BuildContext context, int page, int offset, int memberId) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/member/get-equbs/$memberId'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      print("Response from loadmemberequb is: ${response.statusCode}");
      print("Response body: ${response.body}");
      
      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        
        // Check if response body is null or empty
        if (responseBody == null) {
          print("Response body is null");
          return {
            'code': response.statusCode,
            'data': [],
          };
        }

        // Ensure responseBody is a List
        if (responseBody is! List) {
          print("Response body is not a List: ${responseBody.runtimeType}");
          return {
            'code': response.statusCode,
            'data': [],
          };
        }

        List<Equb> equbs = [];
        for (var item in responseBody) {
          try {
            if (item != null) {
              // Validate required fields before parsing
              if (item is! Map<String, dynamic>) {
                print("Item is not a Map: ${item.runtimeType}");
                continue;
              }

              // Check if equb_type exists and has required fields
              if (item['equb_type'] == null) {
                print("Warning: equb_type is null for item: $item");
                continue;
              }

              final equb = Equb.fromJson(item);
              if (equb != null) {
                equbs.add(equb);
              }
            }
          } catch (e, stackTrace) {
            print("Error parsing equb item: $e");
            print("Stack trace: $stackTrace");
            print("Problematic item: $item");
            // Continue with next item
          }
        }

        _equbs = Equbs(totalMember: equbs.length, equbs: equbs);
        notifyListeners();
        
        return {
          'code': response.statusCode,
          'data': equbs,
        };
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return {
          'code': response.statusCode,
          'data': [],
        };
      }
    } on SocketException catch (e) {
      print("Socket error: $e");
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      print("HTTP error: $e");
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      print("Format error: $e");
      throw ('Format error: $e');
    } catch (e, stackTrace) {
      print("Unexpected error: $e");
      print("Stack trace: $stackTrace");
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
  }

  Future<void> loadPassedMemberEqub(
      BuildContext context, int page, int offset, int memberId) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/member/get-passed-equbs/$memberId'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        final List allEqubs = jsonDecode(response.body);
        List<Equb> equbs = allEqubs.map((job) => Equb.fromJson(job)).toList();

        _passedEqubs = Equbs(totalMember: equbs.length, equbs: equbs);
        notifyListeners();
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        List<Equb> active = [];
        _passedEqubs = Equbs(totalMember: active.length, equbs: active);
      }
      notifyListeners();
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
  }

  Future loadEqubWinner(BuildContext context, String equbTypeId) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/equbType/get-winner/$equbTypeId'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        // print("Response body data from load equb winner is: ${response.body}");
        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return jsonDecode(response.body);
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
  }

  Future addEqubToMember(
      BuildContext context,
      int memberId,
      int typeId,
      String amount,
      String totalAmount,
      String startDate,
      String endDate,
      String lotteryDate,
      String timeline,
      String type) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/equb/equb-register'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              'member_id': memberId,
              'equb_type_id': typeId,
              'amount': amount,
              'total_amount': totalAmount,
              'start_date': startDate,
              'end_date': endDate,
              'lottery_date': lotteryDate,
              'timeline': timeline,
              'status': 'Active',
              'type': type,
            }),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadMemberEqub(context, 0, 1, memberId);
        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return jsonDecode(response.body);
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
  }

  Future<Result> addEqubType(BuildContext context, String name, String round,
      String rote, String type, String lotteryDate, String remark) async {
    try {
      if (type == "Automatic") {
        lotteryDate = lotteryDate;
      } else {
        lotteryDate = "";
      }
      final response = await http
          .post(
            Uri.parse('$_baseUrl/equbType/register'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              'name': name,
              'round': round,
              'rote': rote,
              'type': type,
              'lottery_date': lotteryDate,
              'remark': remark,
            }),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadEqubTypes(context, 0, 1);
        return Result(response.statusCode.toString(), true,
            jsonDecode(response.body)['message']);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
    return Result("error", false,
        EkubLocalization.of(context)!.translate("error_message"));
  }

  Future<Result> editEqubType(
      BuildContext context,
      String name,
      int round,
      String rote,
      String type,
      String lotteryDate,
      String remark,
      String id) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/equbType/update/$id'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              'update_name': name,
              'update_round': round,
              'update_rote': rote,
              'update_type': type,
              'update_lottery_date': lotteryDate,
              'update_remark': remark,
            }),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadEqubTypes(context, 0, 1);
        return Result(response.statusCode.toString(), true,
            jsonDecode(response.body)['message']);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return Result(response.statusCode.toString(), false,
            jsonDecode(response.body)['message']);
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
  }

  // Future<Result> addEqub(
  //     BuildContext context,
  //     int equbId,
  //     DateTime startDate,
  //     DateTime endDate,
  //     DateTime lotteryDate,
  //     int amount,
  //     int expectedAmount) async {
  //   final response = await http.post(
  //     Uri.parse('$_baseUrl/equb/equb-register'),
  //     headers: await RequestHeader().authorisedHeader(),
  //     body: json.encode({
  //       "equb_type_id": equbId,
  //       "amount": amount,
  //       "total_amount": expectedAmount,
  //       "start_date": startDate.toIso8601String(),
  //       "end_date": endDate.toIso8601String(),
  //       "lottery_date": lotteryDate.toIso8601String(),
  //     }),
  //   );
  ////   if (response.statusCode == 200) {
  //     return Result(response.statusCode.toString(), true,
  //         jsonDecode(response.body)['message']);
  //   } else {
  //     if (response.statusCode == 401) {
  //       gotoSignIn(context);
  //     }
  //     return Result(response.statusCode.toString(), false,
  //         jsonDecode(response.body)['message']);
  //   }
  // }

  Dashboard _dashboardData = Dashboard(
      title: "Dashboard",
      daylyPaidAmount: 0,
      daylyExpected: "0",
      weeklyPaidAmount: "0",
      weeklyExpected: 0,
      monthlyPaidAmount: 0,
      monthlyExpected: 0,
      yearlyPaidAmount: 0,
      yearlyExpected: 0,
      tudayPaidMember: Members(totalMember: 0, members: []));
  List<Member> members = [];

  Dashboard get dashboard => _dashboardData;

  Future<void> fetchDashboard(BuildContext context, String equbTypeId) async {
    try {
      final http.Response response = await httpClient
          .get(
            Uri.parse('$_baseUrl/equbTypeDashboard/$equbTypeId'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        var dashboard = Dashboard.fromJson(jsonDecode(response.body));
        _dashboardData = dashboard;
        members = dashboard.tudayPaidMember?.members ?? [];
      } else if (response.statusCode == 401) {
        gotoSignIn(context);
      }
      notifyListeners();
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
  }

  Future<String> checkLotteryDate(
      BuildContext context, DateTime lotteryDate) async {
    final response = await http
        .post(
          Uri.parse('$_baseUrl/dateEqubLotteryCheck'),
          headers: await RequestHeader().authorisedHeader(),
          body: json.encode({
            'lottery_date': lotteryDate.toIso8601String(),
          }),
        )
        .timeout(timeout);

    if (response.statusCode == 200) {
      return response.body;
    } else {
      if (response.statusCode == 401) {
        gotoSignIn(context);
      }
      return response.toString();
    }
  }

  Uint8List? equbtypeImage;
  Uint8List? get equbImage => equbtypeImage;
  Future getEqubtypePicture(BuildContext context, String equbTypeId) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/equbType/$equbTypeId/icon'),
            headers: await RequestHeader().defaultHeader(),
          )
          .timeout(const Duration(minutes: 3));

      if (response.statusCode == 200) {
        equbtypeImage = response.bodyBytes;
        notifyListeners();
        return equbtypeImage;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
      notifyListeners();
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
    return equbtypeImage ?? Uint8List(0);
  }

  Future deleteEqubType(BuildContext context, String id) async {
    try {
      final response = await http
          .delete(
            Uri.parse('$_baseUrl/equbType/delete/$id'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadEqubTypes(context, 0, 1);
        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }

        return jsonDecode(response.body);
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
  }

  Future<List<EqubType>> loadEqubsByFrequency(
      BuildContext context, String frequency, int memberId) async {
    debugPrint(
        "EqubProvider equbId: ${Provider.of<EqubProvider>(context, listen: false).equbId}");
    debugPrint("Frequency: $frequency");
    debugPrint("Member ID: $memberId");

    try {
      // Use default equbId as 1
      final equbProvider = Provider.of<EqubProvider>(context, listen: false);
      debugPrint("Default EqubProvider equbId: 1");

      // Fetch the equbs the user has already joined
      final joinedResponse = await http
          .get(Uri.parse('$_baseUrl/member/get-equbs/$memberId'),
              headers: await RequestHeader().authorisedHeader())
          .timeout(timeout);

      if (joinedResponse.statusCode != 200) {
        if (joinedResponse.statusCode == 401) {
          gotoSignIn(context);
        }
        throw ('Failed to load joined equbs');
      }

      final List joinedEqubs = jsonDecode(joinedResponse.body);
      final Set<String> joinedEqubTypeIds =
          joinedEqubs.map((equb) => equb['equb_type']['id'].toString()).toSet();
      debugPrint("Joined equbs: ${joinedEqubTypeIds.length}");

      // Fetch all equbs
      final response = await http
          .get(Uri.parse('$_baseUrl/mainequb'),
              headers: await RequestHeader().authorisedHeader())
          .timeout(timeout);

      debugPrint(
          "Response from loadEqubsByFrequency is: ${response.statusCode}");
      if (response.statusCode == 200) {
        // print(
        //     "Response body data from loadEqubsByFrequency is: ${response.body}");
        final List mainEqubs = jsonDecode(response.body)['data'];
        List<EqubType> filteredEqubs = [];

        for (var mainEqub in mainEqubs) {
          final mainEqubId = mainEqub['id']?.toString();
          final subEqubList = mainEqub['subEqub'];

          // Check if mainEqub id == "1" and subEqub is a list
          if (mainEqubId == "1" && subEqubList is List) {
            for (var equb in subEqubList) {
              try {
                final rote = equb['rote']?.toString();
                final equbId = equb['id']?.toString();

                // Skip if required fields are missing or conditions don't match
                if (rote == null || equbId == null) continue;

                if (rote == frequency && !joinedEqubTypeIds.contains(equbId)) {
                  // Only add if frequency matches and not joined before
                  filteredEqubs.add(EqubType.fromJson(equb));
                }
              } catch (e, stackTrace) {
                debugPrint("Error converting equb to EqubType: $e");
                debugPrint("Stack trace: $stackTrace");
              }
            }
            debugPrint("Equb data: ${subEqubList[0]}");
          }
        }

        // Print the filtered equbs
        // print(
        //     'Filtered Equbs: ${filteredEqubs.map((e) => e.toJson()).toList()}');

        debugPrint(
            'Equbs loaded successfully: ${filteredEqubs.length} equbs found.');
        return filteredEqubs;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        print('Data is not loaded successfully.');
        throw ('Failed to load equbs');
      }
    } on SocketException catch (e) {
      print('Connection error: $e');
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      print('HTTP error: $e');
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      print('Format error: $e');
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        print('Timeout error: $e');
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        print('Unexpected error: $e');
        throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
      }
    }
  }
  //this is the old code for loadEqubsByFrequency with the original equb id
  // Future<List<EqubType>> loadEqubsByFrequency(
  //     BuildContext context, String frequency, int memberId) async {
  //   print(
  //       "EqubProvider equbId: ${Provider.of<EqubProvider>(context, listen: false).equbId}");
  //   print("Frequency: $frequency");
  //   print("Member ID: $memberId");

  //   try {
  //     final equbProvider = Provider.of<EqubProvider>(context, listen: false);
  //     print("EqubProvider equbId: ${equbProvider.equbId}");

  //     // Fetch the equbs the user has already joined
  //     final joinedResponse = await http
  //         .get(Uri.parse('$_baseUrl/member/get-equbs/$memberId'),
  //             headers: await RequestHeader().authorisedHeader())
  //         .timeout(timeout);

  //     if (joinedResponse.statusCode != 200) {
  //       if (joinedResponse.statusCode == 401) {
  //         gotoSignIn(context);
  //       }
  //       throw ('Failed to load joined equbs');
  //     }

  //     final List joinedEqubs = jsonDecode(joinedResponse.body);
  //     final Set<String> joinedEqubTypeIds =
  //         joinedEqubs.map((equb) => equb['equb_type']['id'].toString()).toSet();

  //     // Fetch all equbs
  //     final response = await http
  //         .get(Uri.parse('$_baseUrl/mainequb'),
  //             headers: await RequestHeader().authorisedHeader())
  //         .timeout(timeout);

  //     if (response.statusCode == 200) {
  //       print(
  //           "Response body data from loadEqubsByFrequency is: ${response.body}");
  //       final List mainEqubs = jsonDecode(response.body)['data'];
  //       List<EqubType> filteredEqubs = [];

  //       for (var mainEqub in mainEqubs) {
  //         if (mainEqub['id'].toString() == equbProvider.equbId.toString() &&
  //             mainEqub['subEqub'] != null) {
  //           List subEqubs = mainEqub['subEqub'];

  //           filteredEqubs.addAll(
  //             subEqubs
  //                 .where((equb) =>
  //                     equb['rote'] == frequency &&
  //                     !joinedEqubTypeIds.contains(equb['id'].toString()))
  //                 .map((equb) => EqubType.fromJson(equb))
  //                 .toList(),
  //           );
  //         }
  //       }

  //       // Print the filtered equbs
  //       print(
  //           'Filtered Equbs: ${filteredEqubs.map((e) => e.toJson()).toList()}');

  //       print(
  //           'Equbs loaded successfully: ${filteredEqubs.length} equbs found.');
  //       return filteredEqubs;
  //     } else {
  //       if (response.statusCode == 401) {
  //         gotoSignIn(context);
  //       }
  //       print('Data is not loaded successfully.');
  //       throw ('Failed to load equbs');
  //     }
  //   } on SocketException catch (e) {
  //     print('Connection error: $e');
  //     throw ('Connection error: Verify your internet connection and try again.');
  //   } on HttpException catch (e) {
  //     print('HTTP error: $e');
  //     throw ('HTTP error: $e');
  //   } on FormatException catch (e) {
  //     print('Format error: $e');
  //     throw ('Format error: $e');
  //   } catch (e) {
  //     if (e is TimeoutException) {
  //       print('Timeout error: $e');
  //       throw EkubLocalization.of(context)!.translate("time_out");
  //     } else {
  //       print('Unexpected error: $e');
  //       throw '${EkubLocalization.of(context)!.translate("error_message")} Error: $e';
  //     }
  //   }
  // }
}
