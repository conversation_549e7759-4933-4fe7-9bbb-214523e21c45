// // ignore_for_file: must_be_immutable

// import 'dart:async';

// import 'package:ekub/models/ekub.dart';
// import 'package:ekub/models/members.dart';
// import 'package:ekub/repository/member_repos.dart';
// import 'package:ekub/routs/shared.dart';
// import 'package:ekub/screens/dashboard/admin/member_equbs.dart';
// import 'package:flutter/material.dart';
// import 'package:http/http.dart' as http;
// import 'package:intl/intl.dart';
// import 'package:panara_dialogs/panara_dialogs.dart';
// import 'package:webview_flutter/webview_flutter.dart';

// import '../../../settings/constant.dart';

// class PaymentWithChapa extends StatefulWidget {
//   final Map<String, dynamic> args;
//   Member? member;
//   Equb? equb;
//   int index;
//   PaymentWithChapa(
//       {super.key,
//       required this.args,
//       required this.index,
//       required this.equb,
//       required this.member});
//   @override
//   State<PaymentWithChapa> createState() => _PaymentWithChapaState();
// }

// class _PaymentWithChapaState extends State<PaymentWithChapa> {
//   WebViewController _controller = WebViewController();
//   String checkoutUrl = "";
//   String referenceNumber = "";
//   bool loading = true;
//   bool isPaid = false;
//   int amount = 0;
//   int loadingPercentage = 0;
//   var formatter = NumberFormat('#,###,###,###.##');
//   showPaymentErrorInfo() {
//     PanaraInfoDialog.show(
//       context,
//       title: "Something went wrong. Please try again.",
//       message: "Payment unsuccessful.",
//       buttonText: "Okay",
//       onTapDismiss: () {
//         Navigator.pushNamedAndRemoveUntil(
//           context,
//           MemberEqubs.routeName,
//           (Route<dynamic> route) => false,
//           arguments: MemberEqubsArgs(
//               isOnline: true, member: widget.member as Member, isAdmin: false),
//         );
//       },
//       imagePath: errorDialogIcon,

//       panaraDialogType: PanaraDialogType.error,
//       barrierDismissible: false, // optional parameter (default is true)
//     );
//   }

//   showPaymentSuccessInfo() {
//     PanaraInfoDialog.show(
//       context,
//       title: "Success",
//       message:
//           "Congratulations! You have paid ${formatter.format(amount)} ETB successfully.",
//       buttonText: "okay",
//       onTapDismiss: () {
//         Navigator.pushNamedAndRemoveUntil(
//           context,
//           MemberEqubs.routeName,
//           (Route<dynamic> route) => false,
//           arguments: MemberEqubsArgs(
//               isOnline: true, member: widget.member as Member, isAdmin: false),
//         );
//       },
//       imagePath: successDialogIcon,

//       panaraDialogType: PanaraDialogType.success,
//       barrierDismissible: false, // optional parameter (default is true)
//     );
//   }

//   changePayementStatus() async {
//     var sender = MemberProvider(httpClient: http.Client());

//     var res = sender.checkTransaction(referenceNumber, context);

//     res
//         .then((value) => {
//               if (value == true)
//                 {
//                   showPaymentSuccessInfo(),
//                 },
//             })
//         .onError((error, stackTrace) {
//       if (mounted) {
//         PanaraConfirmDialog.show(context,
//             // title: "Request Timeout",
//             message: error.toString(),
//             confirmButtonText: "Try Again",
//             cancelButtonText: "Cancel", onTapConfirm: () {
//           Navigator.pop(context);
//           changePayementStatus();
//         }, onTapCancel: () {
//           Navigator.pop(context);
//         }, panaraDialogType: PanaraDialogType.error);
//       }
//       return {};
//     });
//   }

//   initializeWebWiew(BuildContext context) async {
//     _controller = WebViewController()
//       ..setJavaScriptMode(JavaScriptMode.unrestricted)
//       ..setBackgroundColor(const Color(0x00000000))
//       ..setNavigationDelegate(
//         NavigationDelegate(
//           onProgress: (int progress) {
//             if (mounted) {
//               setState(() {
//                 loadingPercentage = progress;
//               });
//             }
//           },
//           onPageStarted: (String url) {},
//           onPageFinished: (String url) async {
//             if (mounted) {
//               setState(() {
//                 loadingPercentage = 100;
//               });
//             }
//             if (checkoutUrl != url) {
//               changePayementStatus();
//             }
//           },
//           onWebResourceError: (WebResourceError error) {
//             showPaymentErrorInfo();
//           },
//           onNavigationRequest: (NavigationRequest request) {
//             if (request.url.startsWith('https://www.youtube.com/')) {
//               return NavigationDecision.prevent;
//             }
//             return NavigationDecision.navigate;
//           },
//         ),
//       )
//       ..addJavaScriptChannel(
//         'Chapa',
//         onMessageReceived: (JavaScriptMessage message) {
//           ScaffoldMessenger.of(context).showSnackBar(
//             SnackBar(content: Text(message.message)),
//           );
//         },
//       )
//       ..loadRequest(Uri.parse(checkoutUrl));
//     //
//   }

//   @override
//   void initState() {
//     super.initState();
//     checkoutUrl = widget.args["checkout_url"].toString();
//     amount = int.parse(widget.args["amount"].toString());
//     // referenceNumber = widget.args["referenceNumber"];
//     // changePayementStatus();
//     initializeWebWiew(context);
//   }

//   goBackPermisson() {
//     PanaraConfirmDialog.show(
//       context,
//       title: "Warning",
//       message: "Do you want to go back?",
//       confirmButtonText: "Confirm",
//       cancelButtonText: "Cancel",
//       onTapCancel: () {
//         Navigator.pop(context);
//       },
//       onTapConfirm: () {
//         Navigator.pushNamedAndRemoveUntil(
//           context,
//           MemberEqubs.routeName,
//           (Route<dynamic> route) => false,
//           arguments: MemberEqubsArgs(
//               isOnline: true, member: widget.member as Member, isAdmin: false),
//         );
//       },
//       imagePath: warningDialogIcon,

//       panaraDialogType: PanaraDialogType.warning,
//       barrierDismissible: false, // optional parameter (default is true)
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: () async {
//         if (isPaid) {
//         } else {
//           goBackPermisson();
//         }
//         return Future.value(isPaid);
//       },
//       child: Scaffold(
//         appBar: AppBar(
//           backgroundColor: Colors.transparent,
//           iconTheme: const IconThemeData(color: Colors.black),
//           title: const Text(
//             "Chapa",
//             style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//           ),
//           leading: IconButton(
//               icon: const Icon(
//                 Icons.arrow_back_ios,
//               ),
//               onPressed: () {
//                 if (isPaid) {
//                 } else {
//                   goBackPermisson();
//                 }
//               }),
//           elevation: 0,
//         ),
//         body: loadingPercentage < 100
//             ? Column(
//                 children: [
//                   if (loadingPercentage < 100)
//                     LinearProgressIndicator(
//                       backgroundColor: Colors.white,
//                       value: loadingPercentage / 100.0,
//                     ),
//                   SizedBox(
//                     height: MediaQuery.of(context).size.height * .3,
//                   ),
//                   // const LoadingIndicator(),
//                 ],
//               )
//             : Stack(
//                 children: [
//                   WebViewWidget(
//                     controller: _controller,
//                   ),
//                 ],
//               ),
//       ),
//     );
//   }
// }
