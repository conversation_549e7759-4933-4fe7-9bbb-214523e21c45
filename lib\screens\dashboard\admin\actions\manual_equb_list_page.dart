import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/account/privacy_screen.dart';
import 'package:flutter/material.dart';
import 'package:ekub/models/equb_type.dart';
import 'package:ekub/screens/dashboard/admin/member_equbs.dart';
import 'package:provider/provider.dart';
import 'package:ekub/repository/equb_repos.dart';
import 'package:http/http.dart' as http;

class ManualEqubList extends StatefulWidget {
  final EqubType equbType;
  final AddEqubArgs args;

  const ManualEqubList({
    Key? key,
    required this.equbType,
    required this.args,
  }) : super(key: key);

  @override
  _ManualEqubListState createState() => _ManualEqubListState();
}

class _ManualEqubListState extends State<ManualEqubList> {
  final _formKey = GlobalKey<FormState>();
  bool _agreedToTerms = false;
  String? _selectedTimeline;
  DateTime? _startDate;
  DateTime? _endDate;
  DateTime? _lotteryDate;
  final TextEditingController _endDateController = TextEditingController();
  final TextEditingController _amountController =
      TextEditingController(text: "0");
  final TextEditingController _expectedTotalController =
      TextEditingController();
  final TextEditingController _lotteryDateController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    print("member id is ${widget.equbType.id.toString()}");
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Frequency'),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStepField(
                  label: "Start Date*",
                  stepNumber: 1,
                  enabled: true,
                  child: TextFormField(
                    controller: TextEditingController(
                      text: _startDate != null
                          ? "${_startDate!.year}-${_startDate!.month.toString().padLeft(2, '0')}-${_startDate!.day.toString().padLeft(2, '0')}"
                          : "${DateTime.now().year}-${DateTime.now().month.toString().padLeft(2, '0')}-${DateTime.now().day.toString().padLeft(2, '0')}",
                    ),
                    readOnly: true,
                    onTap: () async {
                      DateTime? pickedDate = await showDatePicker(
                        context: context,
                        initialDate: _startDate ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2100),
                        builder: (BuildContext context, Widget? child) {
                          return Theme(
                            data: ThemeData.light().copyWith(
                              colorScheme: ColorScheme.light(
                                primary: Colors.green,
                              ),
                            ),
                            child: child!,
                          );
                        },
                      );
                      if (pickedDate != null) {
                        setState(() {
                          _startDate = pickedDate;
                        });
                      }
                    },
                    decoration: InputDecoration(
                      suffixIcon: Icon(Icons.calendar_today),
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                _buildStepField(
                  label: "End Date",
                  stepNumber: 2,
                  child: TextFormField(
                    readOnly: true,
                    controller: _endDateController,
                    decoration: InputDecoration(
                      hintText: "End Date",
                      border: OutlineInputBorder(),
                      filled: true,
                      fillColor: Colors.grey[300],
                    ),
                  ),
                ),
                _buildStepField(
                  label: "Amount*",
                  stepNumber: 3,
                  enabled: true,
                  child: TextFormField(
                    controller: _amountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      _calculateExpectedTotal();
                    },
                  ),
                ),
                _buildStepField(
                  label: "Expected Total",
                  stepNumber: 4,
                  child: TextFormField(
                    readOnly: true,
                    controller: _expectedTotalController,
                    decoration: InputDecoration(
                      hintText: "Total amount",
                      border: OutlineInputBorder(),
                      filled: true,
                      fillColor: Colors.grey[300],
                    ),
                  ),
                ),
                _buildStepField(
                  label: "Timeline*",
                  stepNumber: 5,
                  enabled: true,
                  child: DropdownButtonFormField<String>(
                    value: _selectedTimeline,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(),
                    ),
                    hint: Text("Choose Timeline"),
                    onChanged: (value) {
                      setState(() {
                        _selectedTimeline = value;
                        if (_startDate != null && value != null) {
                          int daysToAdd = int.parse(value.split(' ')[0]);
                          _endDate = _startDate!.add(Duration(days: daysToAdd));
                          _endDateController.text =
                              "${_endDate!.year}-${_endDate!.month.toString().padLeft(2, '0')}-${_endDate!.day.toString().padLeft(2, '0')}";
                          _calculateExpectedTotal();
                        }
                      });
                    },
                    items: ["105 days", "210 days", "315 days"]
                        .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                        .toList(),
                  ),
                ),
                _buildStepField(
                  label: "Lottery Date*",
                  stepNumber: 6,
                  enabled: true,
                  child: TextFormField(
                    controller: _lotteryDateController,
                    readOnly: true,
                    onTap: () async {
                      if (_startDate == null) {
                        // Show a message if start date is not selected
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content:
                                  Text("Please select a start date first.")),
                        );
                        return;
                      }

                      DateTime firstValidDate =
                          _startDate!.add(Duration(days: 45));
                      DateTime initialDate = _lotteryDate != null &&
                              _lotteryDate!.isAfter(firstValidDate)
                          ? _lotteryDate!
                          : firstValidDate;

                      DateTime? pickedDate = await showDatePicker(
                        context: context,
                        initialDate: initialDate, // Ensure initialDate is valid
                        firstDate: firstValidDate, // 45 days after start date
                        lastDate: DateTime(2100),
                        builder: (BuildContext context, Widget? child) {
                          return Theme(
                            data: ThemeData.light().copyWith(
                              colorScheme: ColorScheme.light(
                                primary: Colors.green,
                              ),
                            ),
                            child: child!,
                          );
                        },
                      );
                      if (pickedDate != null) {
                        setState(() {
                          _lotteryDate = pickedDate;
                          _lotteryDateController.text =
                              "${_lotteryDate!.year}-${_lotteryDate!.month.toString().padLeft(2, '0')}-${_lotteryDate!.day.toString().padLeft(2, '0')}";
                        });
                      }
                    },
                    decoration: InputDecoration(
                      suffixIcon: Icon(Icons.calendar_today),
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                CheckboxListTile(
                  title: Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const PrivacyScreen(),
                              ),
                            );
                          },
                          child: Text(
                            EkubLocalization.of(context)!
                                .translate("accept_terms_conditions"),
                            style: TextStyle(
                              decoration: TextDecoration.underline,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  value: _agreedToTerms,
                  onChanged: (bool? value) {
                    setState(() {
                      _agreedToTerms = value ?? false;
                    });
                  },
                ),
                SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: Colors.green,
                      textStyle: TextStyle(fontSize: 18),
                    ),
                    onPressed: _agreedToTerms ? _registerEqub : null,
                    child: Text("Join"),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _calculateExpectedTotal() {
    if (_selectedTimeline != null) {
      int days = int.parse(_selectedTimeline!.split(' ')[0]);
      double amount = double.tryParse(_amountController.text) ?? 0;
      double expectedTotal = days * amount;
      _expectedTotalController.text = expectedTotal.toStringAsFixed(2);
    }
  }

  void _registerEqub() {
    if (_formKey.currentState!.validate()) {
      int memberId = widget.args.member.id ?? 0;
      int typeId = widget.equbType.id ?? 0;
      String amount = _amountController.text;
      String totalAmount = _expectedTotalController.text;
      String startDate = _startDate != null
          ? "${_startDate!.year.toString().padLeft(4, '0')}-${_startDate!.month.toString().padLeft(2, '0')}-${_startDate!.day.toString().padLeft(2, '0')}"
          : "2025-01-01";
      String endDate = _endDate != null
          ? "${_endDate!.year.toString().padLeft(4, '0')}-${_endDate!.month.toString().padLeft(2, '0')}-${_endDate!.day.toString().padLeft(2, '0')}"
          : "2025-01-01";
      String lotteryDate = _lotteryDate != null
          ? "${_lotteryDate!.year.toString().padLeft(4, '0')}-${_lotteryDate!.month.toString().padLeft(2, '0')}-${_lotteryDate!.day.toString().padLeft(2, '0')}"
          : "''";
      String timeline = _selectedTimeline != null
          ? "'${_selectedTimeline!.split(' ')[0]}'"
          : "'0'";

      // Set default type to "Automatic" if null
      String equbType = widget.equbType.type ?? 'Manual';

      // Print the data that will be sent
      print("Data to be sent:");
      print("Member ID: $memberId");
      print("Equb Type ID: $typeId");
      print("Amount: $amount");
      print("Total Amount: $totalAmount");
      print("Start Date: $startDate");
      print("End Date: $endDate");
      print("Lottery Date: $lotteryDate");
      print("Timeline is: $timeline");
      print("Type: $equbType");

      final equbDataProvider =
          Provider.of<EqubDataProvider>(context, listen: false);
      equbDataProvider
          .addEqubToMember(
        context,
        memberId,
        typeId,
        amount,
        totalAmount,
        startDate,
        endDate,
        lotteryDate,
        timeline,
        equbType, // Use the defaulted equbType here
      )
          .then((result) {
        if (result != null) {
          print("Equb registration successful: $result");
          // Show success alert
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text("Success"),
                content: Text("Equb registration was successful."),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text("OK"),
                  ),
                ],
              );
            },
          );
        } else {
          print("Equb registration failed.");
        }
      }).catchError((error) {
        print("Error during equb registration: $error");
        if (error is http.Response) {
          print("Response body: ${error.body}");
        } else {
          print("Unexpected error: $error");
        }
      });
    }
  }

  Widget _buildStepField({
    required String label,
    required int stepNumber,
    required Widget child,
    bool enabled = false,
  }) {
    // Determine if the step should be highlighted
    bool isStepCompleted = false;
    switch (stepNumber) {
      case 1:
        isStepCompleted = _startDate != null;
        break;
      case 2:
        isStepCompleted =
            _endDate != null && _endDateController.text.isNotEmpty;
        break;
      case 3:
        isStepCompleted =
            _amountController.text.isNotEmpty && _amountController.text != "0";
        break;
      case 4:
        isStepCompleted = _expectedTotalController.text.isNotEmpty;
        break;
      case 5:
        isStepCompleted = _selectedTimeline != null;
        break;
      case 6:
        isStepCompleted =
            _lotteryDate != null && _lotteryDateController.text.isNotEmpty;
        break;
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            CircleAvatar(
              radius: 12,
              backgroundColor: isStepCompleted ? Colors.green : Colors.white,
              child: Text(
                stepNumber.toString(),
                style: TextStyle(color: Colors.black),
              ),
            ),
            if (stepNumber < 6) // Draw line except for the last step
              Container(
                width: 2,
                height: 40,
                color: Colors.grey,
              ),
          ],
        ),
        SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: enabled ? Colors.black : Colors.grey,
                ),
              ),
              child,
              SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }
}
