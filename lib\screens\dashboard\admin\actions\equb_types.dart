// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:typed_data';

import 'package:ekub/screens/dashboard/admin/actions/equb_subtypes.dart';
import 'package:ekub/screens/ui_kits/app_bar.dart';
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../../../models/equb_type.dart';
import '../../../../repository/ekub_localization.dart';
import '../../../../repository/equb_repos.dart';
import '../../../../routs/shared.dart';
import '../../../../utils/tools.dart';
import '../../../themes/ThemeProvider.dart';
import 'add_ekub.dart';

class EqubTypeSelect extends StatefulWidget {
  final AddEqubArgs args;
  const EqubTypeSelect({super.key, required this.args});

  @override
  State<EqubTypeSelect> createState() => _EqubTypeSelectState();
}

class _EqubTypeSelectState extends State<EqubTypeSelect> {
  List<EqubType> _items = [];
  bool loading = true;
  EqubType? selctedType;
  int selectedIndex = 0;
  List<String> equbTypes = [];
  late ThemeProvider themeProvider;
  final _appBar = GlobalKey<FormState>();

  Uint8List? image;

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    loadEqubType();
    super.initState();
  }

  void loadEqubType() async {
    try {
      equbTypes = [];
      print("Loading equb types...");
      _items = await Provider.of<EqubDataProvider>(context, listen: false)
          .loadEqubTypes(context, 0, 1);
      // image = await Provider.of<EqubDataProvider>(context, listen: false)
      //     .getEqubtypePicture(context, widget.user.id.toString());

      print("Equb types loaded: ${_items.length} items");

      setState(() {
        selctedType = _items.isEmpty ? null : _items[0];
        for (var element in _items) {
          if (element.status == "Active") {
            equbTypes.add(element.name.toString());
            print("Added active equb type: ${element.name}");
          }
        }
        print("Total active equb types: ${equbTypes.length}");
        loading = false;
      });
    } catch (e) {
      if (e is TimeoutException) {
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("time_out"),
            message: EkubLocalization.of(context)!.translate("timeout_message"),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          loadEqubType();
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      } else {
        PanaraConfirmDialog.show(context,
            message: e.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          if (mounted) {
            loadEqubType();
            Navigator.pop(context);
          }
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: EkubAppBar(
        key: _appBar,
        // backgroundColor: themeProvider.getColor,
        title: "Select equb type",
        // elevation: 0,
        widgets: const [],
      ),
      body: loading
          ? searchLoading()
          : Container(
              margin: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  const Text(
                    "Choose equb type you want to subscribe",
                    style: TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.normal,
                        fontSize: fontMedium),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Expanded(
                      // height: MediaQuery.of(context).size.height * 0.6,
                      child: GridView.builder(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount:
                                      2, // Number of columns in the grid
                                  crossAxisSpacing:
                                      10.0, // Spacing between columns
                                  mainAxisSpacing: 2.0, // Spacing between rows
                                  childAspectRatio: .7),
                          itemCount: _items.length,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                                onTap: () {
                                  setState(() {
                                    selectedIndex =
                                        index; // Update selected index on tap
                                    selctedType = _items[index];
                                  });
                                  // print(selctedType!.name.toString());
                                  // equbTypes.isNotEmpty
                                  //     ? showEqubInfo(context)
                                  //     : null;
                                  if (selctedType!.name!.toLowerCase() ==
                                      "house") {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) => AddEqub(
                                                slectedEqubType: selctedType!,
                                                args: AddEqubArgs(
                                                    isOnline: true,
                                                    member: widget.args.member,
                                                    isAdmin: false,
                                                    user: widget.args.user))));
                                  } else {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                EqubSubtypeScreen(
                                                    // slectedEqubType: selctedType!,
                                                    args: AddEqubArgs(
                                                        isOnline: true,
                                                        member:
                                                            widget.args.member,
                                                        isAdmin: false,
                                                        user: widget
                                                            .args.user))));
                                  }
                                },
                                child: roleContainer(context, _items[index],
                                    selectedIndex == index, themeProvider));
                          })),
                  const SizedBox(height: 20),
                ],
              ),
            ),
      // GestureDetector(
      //   onTap: selctedType == null
      //       ? null
      //       : () {
      //           if (selctedType!.name!.toLowerCase() == "house") {
      //             Navigator.push(
      //                 context,
      //                 MaterialPageRoute(
      //                     builder: (context) => AddEqub(
      //                         slectedEqubType: selctedType!,
      //                         args: AddEqubArgs(
      //                             isOnline: true,
      //                             member: widget.args.member,
      //                             isAdmin: false,
      //                             user: widget.args.user))));
      //           } else {
      //             Navigator.push(
      //                 context,
      //                 MaterialPageRoute(
      //                     builder: (context) => EqubSubtypeScreen(
      //                         // slectedEqubType: selctedType!,
      //                         args: AddEqubArgs(
      //                             isOnline: true,
      //                             member: widget.args.member,
      //                             isAdmin: false,
      //                             user: widget.args.user))));
      //           }
      //         },
      //   child: selctedType == null
      //       ? buttonWidget("Continue", 20, false, themeProvider)
      //       : buttonWidget("Continue", 20, true, themeProvider),
      // )
    );
  }

  Future<dynamic> showEqubInfo(BuildContext context) {
    return PanaraInfoDialog.show(
      context,
      title: selctedType!.name!,
      message: selctedType!.type == "Automatic"
          ? "${EkubLocalization.of(context)!.translate("automatic_equb_msg1")}. ${EkubLocalization.of(context)!.translate("starts")} ${formatDate(selctedType!.startDate!)} ${EkubLocalization.of(context)!.translate("and")} ${EkubLocalization.of(context)!.translate("ends")} ${formatDate(selctedType!.endDate!)}. ${EkubLocalization.of(context)!.translate("automatic_equb_msg2")}."
          : EkubLocalization.of(context)!.translate("manual_equb_msg"),
      buttonText: EkubLocalization.of(context)!.translate("okay"),
      onTapDismiss: () {
        Navigator.pop(context);
      },
      panaraDialogType: PanaraDialogType.normal,
    );
  }
}

roleContainer(BuildContext context, EqubType type, bool isSelected, theme) {
  return Container(
    padding: const EdgeInsets.all(20),
    margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
    alignment: Alignment.center,
    decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: const BorderRadius.all(Radius.circular(12))),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: 50,
          margin: const EdgeInsets.only(bottom: 15),
          child: type.imageUrl != null
              ? ClipOval(
                  child: Image.network(
                    type.imageUrl!,
                    width: 50,
                    height: 50,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.image_not_supported,
                        color: theme.primaryColor,
                      );
                    },
                  ),
                )
              : Icon(
                  Icons.image_not_supported,
                  color: theme.primaryColor,
                ),
        ),
        Text(
          type.name!,
          overflow: TextOverflow.clip,
          style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: fontMedium,
              overflow: TextOverflow.clip),
        ),
        const SizedBox(
          height: 10,
        ),
        Text(
          'Round :${type.round!}',
          overflow: TextOverflow.clip,
          style: TextStyle(
              color: Colors.grey.shade500,
              fontWeight: FontWeight.normal,
              fontSize: fontSmall,
              overflow: TextOverflow.clip),
        ),
      ],
    ),
  );
}

Container buttonWidget(String text, double marginBottom, bool enable, theme) {
  return Container(
    margin: EdgeInsets.only(bottom: marginBottom),
    alignment: Alignment.center,
    height: 56,
    decoration: BoxDecoration(
        color: enable ? theme.getColor : Colors.grey.shade400,
        borderRadius: const BorderRadius.all(Radius.circular(5))),
    child: Text(
      text,
      style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: fontMedium),
    ),
  );
}
