{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989ad07f65af31b5fcd695b1d63ad46212", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806a5f3f82a5e8830383e3c2dca80399d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869ed143612e17e763485740ab6949fd8", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983ba988c26e4b0dc2789b6a9e5874d9ef", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869ed143612e17e763485740ab6949fd8", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986972837704cd8bc64efa981fee2bbd76", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ff426bbbf20555beffb8a789f26f02f", "guid": "bfdfe7dc352907fc980b868725387e9856971f609333f584bb7103727d5c6e84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98763dfdc6afca55c1123402b9f33909c3", "guid": "bfdfe7dc352907fc980b868725387e98eafd907233fca24168a906935a094d61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7356f80feea10919391cb00449a2b27", "guid": "bfdfe7dc352907fc980b868725387e98deeec50809e485d4a5e8216e55d905ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982586c28aca11b54f117941003c4bb709", "guid": "bfdfe7dc352907fc980b868725387e981cdfeb7f9238ef44afe076eb58ef88ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d0f500d0a5446fee407d743aec48d33", "guid": "bfdfe7dc352907fc980b868725387e98e96940090a25cbe7d7479da727bd5266", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832dc0dca01ceeebc0dad25408d74995b", "guid": "bfdfe7dc352907fc980b868725387e981d5f280dcfeea3696b16bc62f3ffea9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ba6fbfcd68ad3e211f3515a4177a8e", "guid": "bfdfe7dc352907fc980b868725387e98c189b015e188e35211fdad4514bb5a2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdf2733d180ab6b33f8741b78c9b5736", "guid": "bfdfe7dc352907fc980b868725387e982da57b72b812894a66c74b74e9497c52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a4fc62761178c2d7928faf5a33435f", "guid": "bfdfe7dc352907fc980b868725387e98bf905b3eaeda42528f86805e26d7797b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888f299618cc24d1e8f9355df4eecba44", "guid": "bfdfe7dc352907fc980b868725387e984beeb6548c19e65a509f03c6fed475ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985afe83f2c7e7a278aa8176537a290130", "guid": "bfdfe7dc352907fc980b868725387e98bf599d35acee98a7c0aa4f4aa148d1c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9235ba4fd32979879e6365dbb3bc736", "guid": "bfdfe7dc352907fc980b868725387e98a761a77258981814f7d533d4329d4e4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851d29f5ed90f7da40372ca7e5eb4243d", "guid": "bfdfe7dc352907fc980b868725387e985ef7c28040c794d4544473c6352f6360", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fbc658a0663c8d46cf1fe937b27e9b7", "guid": "bfdfe7dc352907fc980b868725387e98cce84d9bf9fe53b79499b4fc21b6d4d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be4a68f59511379962a3536c92c32c26", "guid": "bfdfe7dc352907fc980b868725387e986420fcce55ebd268ae8225d78233822e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98662d02c3f2b59c780245c31ece118a15", "guid": "bfdfe7dc352907fc980b868725387e985f5af8bc35cf39ff688a6da0fa892d94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b1887b047a737433e10d8074da752c", "guid": "bfdfe7dc352907fc980b868725387e98508ab161700792018a9b41ff7ed382e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984485cdbc16f3d8b07c9ef45253b2f6ed", "guid": "bfdfe7dc352907fc980b868725387e986b1d6d65a835edd31d23c925bfa841fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989165bb0f657316952edcf1c02857178c", "guid": "bfdfe7dc352907fc980b868725387e98180a754174789a8e7ea8aca61dedc6d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985732fadda53564c8cae5d8c137614838", "guid": "bfdfe7dc352907fc980b868725387e9812b65370b6f404f9ec56a36d6b7ce47b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988314ac00c29d814b0d58ed45c76dda0f", "guid": "bfdfe7dc352907fc980b868725387e98c6455b0a45678360a38f8b613ce415c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b285f0a3556a40dd81e113817477f2e", "guid": "bfdfe7dc352907fc980b868725387e980adca6ec94f1bd68b1f64424ab8250b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859db256758f435dec9deb6012998ef7e", "guid": "bfdfe7dc352907fc980b868725387e984c7241216dfa1c8c7a6a0f6ddba4e8ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcfe5ae5026aaea682e8313c550744d9", "guid": "bfdfe7dc352907fc980b868725387e98f500550405ddf6dcdd7116418a117438", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98636089998a5cc2ff1db0f8c7b2718bc9", "guid": "bfdfe7dc352907fc980b868725387e98aafadc6ce671169eba6e037dfa2f9c3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a7282f3152490bb90421a81ea37034", "guid": "bfdfe7dc352907fc980b868725387e98314c4637228aaad44cb1df5dc8fa318d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877b6ba5f22492163c901a1ff4f3556e9", "guid": "bfdfe7dc352907fc980b868725387e98e31921aa1310be9fe378224943a0a884", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b6461674e83b98e043539b2dec8f37", "guid": "bfdfe7dc352907fc980b868725387e987083617cc682ca751248648e07305fad", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98226731c2a289244acdf15bb7acfea879", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e4fd4e936271840b3b8cdac9694b1ef6", "guid": "bfdfe7dc352907fc980b868725387e9824c0c87e7a0e8569e3468c7c3d2ad613"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b960d637d4b214d39f7abb7e8742332", "guid": "bfdfe7dc352907fc980b868725387e987f0e6dd7a06e9ca38d13d883945dc818"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac51d1d1defcf5bec128393e8b627e5c", "guid": "bfdfe7dc352907fc980b868725387e98165794fbebab011a99a54e922a0e920d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff925444f991a04426426a0204c2b058", "guid": "bfdfe7dc352907fc980b868725387e9896f51a502296e1250c84b09530c5017b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab2f32044690e04eca938d14bb0e93e", "guid": "bfdfe7dc352907fc980b868725387e9861c5998b3b7a5e126ee18cd2e273fe50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf4df38d95b47ff930209928b56f704", "guid": "bfdfe7dc352907fc980b868725387e9843337c2ec20c53ec220c9841262929a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d20af97c430d6eefe4e108b3d6681a3", "guid": "bfdfe7dc352907fc980b868725387e987650a8d4dd545a07f0d9227868094caf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0bf8d42d3f5d5c6c4b19da96102d083", "guid": "bfdfe7dc352907fc980b868725387e98ab800d0b2d6ec94c973d257953dc1f9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffbf0e3fdd0fffa1c3afd3afc268ccea", "guid": "bfdfe7dc352907fc980b868725387e9870ec963f501c7abee816552a42847cf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b1f31a4b55fb0684173c175ad540d93", "guid": "bfdfe7dc352907fc980b868725387e98cfad07b7a886d084514a86b2aee39546"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d92f3ef3ec47af70e6e55aab4849e0", "guid": "bfdfe7dc352907fc980b868725387e98ef3415ced7c4e689af2f059c0217a915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c524fabef1838a797869cb13ea727710", "guid": "bfdfe7dc352907fc980b868725387e985206f9701a3c2171515226b321fa1a09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5850e22d09608d3b4f3145ccc56855a", "guid": "bfdfe7dc352907fc980b868725387e98c5dc6f9782a08cc0e0d636e82db5a00e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987844550a5052efe013969c9799ef4a10", "guid": "bfdfe7dc352907fc980b868725387e984659d7a1e6670b59e965cf94640a8003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa76d4a575d624ebb8f8b74d81a0abe8", "guid": "bfdfe7dc352907fc980b868725387e98df6a23649290a548feb970da8dc2a4ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98618f0a3122a2fead7c68dd195dab2d8d", "guid": "bfdfe7dc352907fc980b868725387e98154af32ab9084a6c5305bc25fd2e62ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe7b7fa3d2e8d4413b855324169151e", "guid": "bfdfe7dc352907fc980b868725387e984014c6143f3f4d4775844a90d181442b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984984e9a7458b327176fcfe4b6b0673fa", "guid": "bfdfe7dc352907fc980b868725387e98f58955f6ff589324b0cfa277f77e9cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820226a0195adf8d42bcaee8588608881", "guid": "bfdfe7dc352907fc980b868725387e989b1d797e045ae040a148a937a84f7b73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843b9a3684caa4bee75c32b13088c51cf", "guid": "bfdfe7dc352907fc980b868725387e98448b43835fe489100be49b9650be39df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986600e3f7085285d6b5849680c2c3a758", "guid": "bfdfe7dc352907fc980b868725387e98ef576f326ad396e70c776511eaa75eb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814e6816a40b477a4cb8f7c522c1bc1db", "guid": "bfdfe7dc352907fc980b868725387e987f5c89bed3ff88e50f11236cdac7262a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f3317ef85c6b94ad707f8d98144fb4c", "guid": "bfdfe7dc352907fc980b868725387e984952426927c272504cc4c0ccf448ec0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875cee4553b79354b6277e7e77b91ee70", "guid": "bfdfe7dc352907fc980b868725387e989f6a38c6d301c56978a355af2968068f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d60c252f6cc534d01f16859cfad3498", "guid": "bfdfe7dc352907fc980b868725387e98e95353344eda64ace3ffeeab8be5ac78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829eceb8ec0e3351a52e6876fc423e768", "guid": "bfdfe7dc352907fc980b868725387e9859babf625f6bcc000446e39a6123cea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a43e27f7f3251157ddcae6e379c9fbc", "guid": "bfdfe7dc352907fc980b868725387e986b1c121f41d42f40c7cd443c0b8522df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcad7a47f7bb6eef029f769f0c5eb264", "guid": "bfdfe7dc352907fc980b868725387e98bcd6f0ad842c899e9fe26bd8134bf8d9"}], "guid": "bfdfe7dc352907fc980b868725387e98aae45d596a019bf549ef1a8c5aad7064", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a15b41da5b9cf00eaf9e8a12fb6fc89a", "guid": "bfdfe7dc352907fc980b868725387e98af0349885e5e5401512da9725f5120de"}], "guid": "bfdfe7dc352907fc980b868725387e98b38bdd542b1991417bd9ff386034da2a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9851c5bdeb1f0e1032246f6c780a1ea631", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}