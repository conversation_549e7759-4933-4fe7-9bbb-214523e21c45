// ignore_for_file: must_be_immutable

import 'dart:async';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/admin/members.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../../exports/models.dart';
import '../../../../repository/equb_repos.dart';
import '../../../../utils/tools.dart';
import '../../../../utils/validator.dart';
import '../../../themes/ThemeProvider.dart';
import '../../../ui_kits/app_bar.dart';

class AddEqub extends StatefulWidget {
  static const routeName = "/add_equb";
  AddEqubArgs args;
  EqubType slectedEqubType;
  AddEqub({super.key, required this.args, required this.slectedEqubType});

  @override
  State<AddEqub> createState() => _AddEqubState();
}

class _AddEqubState extends State<AddEqub> {
  int totalEqubTypes = 0;
  int timeline = 0;
  bool agree = false;
  bool invisible = false;
  String? gender;
  bool isSubmitted = false;
  bool _onProcess = false;
  String selectedTimeline = "";
  String selectedRote = "";
  Member? member;
  // List<String> equbTypes = [];
  List<String> timelines = ["105 days", "210 days", "315 days"];
  List<String> weeklyTimelines = ["50 weeks", "100 weeks", "150 weeks"];
  List<String> monthlyTimelines = ["12 months", "24 months", "36 months"];
  List<String> rotes = ["Daily", "Weekly", "Monthly"];
  DateTime startDate = DateTime.now();
  DateTime initialDate = DateTime.now();
  late ThemeProvider themeProvider;
  final _appBar = GlobalKey<FormState>();
  final _addEqubFormKey = GlobalKey<FormState>();
  final typeController = TextEditingController();
  final startDateController = TextEditingController();
  final endDateController = TextEditingController();
  final lotteryDateController = TextEditingController();
  final amountControl = TextEditingController();
  final expectedControl = TextEditingController();
  // final dropdownControl = TextEditingController();
  final dropdownControl2 = TextEditingController();
  // final dropdownControl3 = TextEditingController();

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  @override
  void initState() {
    startDateController.text = "";
    endDateController.text = "";
    lotteryDateController.text = "";
    member = widget.args.member;
    loadProfile();
    _onProcess = false;
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    if (widget.args.isAdmin) {
      agree = true;
    }

    if (widget.slectedEqubType.type == "Automatic") {
      String startDate = DateFormat('yyyy-MM-dd')
          .format(DateTime.parse(widget.slectedEqubType.startDate!));
      String endDate = DateFormat('yyyy-MM-dd')
          .format(DateTime.parse(widget.slectedEqubType.endDate!));
      startDateController.text = startDate;
      endDateController.text = endDate;
    }

    super.initState();
  }

  loadProfile() async {
    member = await Provider.of<MemberProvider>(context, listen: false)
        .getMember(context, member!.id.toString());
  }

  @override
  Widget build(BuildContext context) {
    var language = EkubLocalization.of(context)!;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: EkubAppBar(
          // /: themeProvider.getColor,
          key: _appBar,
          title: toCamelCase(widget.slectedEqubType.name ?? ""),
          widgets: [
            widget.slectedEqubType.type == "Manual"
                ? Tooltip(
                    message: 'This is information about this equb',
                    child: TextButton(
                      onPressed: () {
                        PanaraInfoDialog.show(context,
                            // title: "",
                            message: language.translate("more_info"),
                            buttonText: EkubLocalization.of(context)!
                                .translate("okay"), onTapDismiss: () {
                          Navigator.pop(context);
                        }, panaraDialogType: PanaraDialogType.normal);
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info,
                              color: themeProvider.getColor,
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            Text(language.translate("more_info_title"),
                                style: TextStyle(
                                  fontSize: fontMedium,
                                  color: themeProvider.getColor,
                                )),
                          ],
                        ),
                      ),
                    ),
                  )
                : Container()
          ]),
      body: Form(
        key: _addEqubFormKey,
        autovalidateMode: isSubmitted
            ? AutovalidateMode.onUserInteraction
            : AutovalidateMode.disabled,
        child: Container(
          margin: const EdgeInsets.fromLTRB(20, 5, 20, 10),
          padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
          child: ListView(
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    widget.args.isAdmin
                        ? language.translate("add_ekub")
                        : "${language.translate("join")} ${widget.slectedEqubType.name}",
                    style: const TextStyle(
                      fontSize: 25,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              widget.slectedEqubType.type == "Manual"
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Text(
                            language.translate("select_rote"),
                            style: TextStyle(
                                color: themeProvider.getColor,
                                fontWeight: FontWeight.bold,
                                fontSize: fontMedium),
                          ),
                        ),
                        const SizedBox(
                          height: 3,
                        ),
                        CustomDropdown<String>(
                          onChanged: (value) {
                            setState(() {
                              dropdownControl2.text = value!;
                              selectedRote = value;
                              selectedTimeline = "";
                            });
                          },
                          validator: (p0) {
                            if (selectedRote == "") {
                              return language.translate("rote");
                            }
                            return null;
                          },

                          decoration: CustomDropdownDecoration(
                            closedBorder:
                                Border.all(color: Colors.grey.shade400),
                            closedSuffixIcon: Container(
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 3, vertical: 0),
                              decoration: BoxDecoration(
                                  color: ColorProvider.backgroundColor,
                                  shape: BoxShape.circle),
                              child: Icon(
                                Icons.keyboard_arrow_down,
                                size: 25,
                                color: themeProvider.getColor,
                              ),
                            ),
                          ),
                          // borderSide: BorderSide(color: Colors.grey.shade400),
                          hintText: language.translate("select_rote"),
                          // hintStyle: lableStyle,
                          // selectedStyle: lableStyle,
                          items: rotes,
                          // controller: dropdownControl3,
                        ),
                      ],
                    )
                  : Container(),
              widget.slectedEqubType.type == "Manual"
                  ? const SizedBox(
                      height: 15,
                    )
                  : Container(),
              _datePicker(startDateController, language.translate("start_date"),
                  initialDate, initialDate, DateTime(2100)),
              const SizedBox(
                height: 15,
              ),
              widget.slectedEqubType.type == "Manual"
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Text(
                            language.translate("select_timeline"),
                            style: TextStyle(
                                color: themeProvider.getColor,
                                fontWeight: FontWeight.bold,
                                fontSize: fontMedium),
                          ),
                        ),
                        const SizedBox(
                          height: 3,
                        ),
                        Row(
                          children: [
                            Flexible(
                              flex: 1,
                              child: CustomDropdown<String>(
                                onChanged: checkTimeline,
                                validator: (p0) {
                                  if (selectedTimeline == "") {
                                    return language
                                        .translate("select_timeline_error");
                                  }
                                  return null;
                                },
                                hintText: language.translate("select_timeline"),
                                decoration: CustomDropdownDecoration(
                                  closedBorder:
                                      Border.all(color: Colors.grey.shade400),
                                  closedSuffixIcon: Container(
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 3, vertical: 0),
                                    decoration: BoxDecoration(
                                        color: ColorProvider.backgroundColor,
                                        shape: BoxShape.circle),
                                    child: Icon(
                                      Icons.keyboard_arrow_down,
                                      size: 25,
                                      color: themeProvider.getColor,
                                    ),
                                  ),

                                  // errorText: language.translate("select_rote"),
                                ),
                                items: selectedRote == "Daily"
                                    ? timelines
                                    : selectedRote == "Weekly"
                                        ? weeklyTimelines
                                        : monthlyTimelines,
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Container(),
              widget.slectedEqubType.type == "Manual"
                  ? const SizedBox(
                      height: 15,
                    )
                  : Container(),
              _datePicker(
                  endDateController,
                  language.translate("end_date"),
                  startDate.add(const Duration(days: 5)),
                  startDate.add(const Duration(days: 5)),
                  DateTime(2100)),
              const SizedBox(
                height: 15,
              ),
              widget.args.isAdmin && widget.slectedEqubType.type == "Manual"
                  ? _datePicker(
                      lotteryDateController,
                      language.translate("lottery_date"),
                      startDate.add(const Duration(days: 5)),
                      startDate.add(const Duration(days: 5)),
                      DateTime(2100))
                  : Container(),
              widget.args.isAdmin && widget.slectedEqubType.type == "Manual"
                  ? const SizedBox(
                      height: 15,
                    )
                  : const SizedBox(
                      height: 0,
                    ),
              amountField(),
              const SizedBox(
                height: 15,
              ),
              expectedAmountField(),
              widget.args.isAdmin
                  ? Container()
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Checkbox(
                          checkColor: Colors.white,
                          activeColor: themeProvider.getColor,
                          value: agree,
                          onChanged: (value) {
                            setState(() {
                              agree = value ?? false;
                            });
                          },
                        ),
                        Expanded(child: termsAndConditions(context))
                      ],
                    ),
              GestureDetector(
                onTap: _onProcess
                    ? null
                    : () async {
                        if (!agree) {
                          // Show alert if terms and conditions are not accepted
                          PanaraInfoDialog.show(
                            context,
                            title: EkubLocalization.of(context)!
                                .translate("alert"),
                            message: EkubLocalization.of(context)!
                                .translate("please_accept_terms"),
                            buttonText:
                                EkubLocalization.of(context)!.translate("okay"),
                            onTapDismiss: () {
                              Navigator.pop(context);
                            },
                            panaraDialogType: PanaraDialogType.warning,
                          );
                          return;
                        }

                        final form = _addEqubFormKey.currentState;
                        setState(() {
                          isSubmitted = true;
                        });
                        if (form!.validate()) {
                          form.save();
                          if (!await InternetConnectivity()
                              .checkInternetConnectivty(context, true)) {
                            setState(() {
                              _onProcess = false;
                            });
                            return;
                          } else {
                            setState(() {
                              _onProcess = true;
                            });
                            var memberId = member!.id;
                            var amount = amountControl.text;
                            var totalAmount = expectedControl.text;
                            var startDate = startDateController.text;
                            var endDate = endDateController.text;
                            var lotteryDate = lotteryDateController.text;

                            _addEqub(
                                memberId ?? 0,
                                widget.slectedEqubType.id ?? 0,
                                amount,
                                totalAmount,
                                startDate,
                                endDate,
                                lotteryDate,
                                timeline.toString(),
                                widget.slectedEqubType.type!);
                          }
                        }
                      },
                child: Container(
                  height: 60,
                  margin: const EdgeInsets.only(top: 15),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: agree
                          ? themeProvider.getColor
                          : themeProvider.getColor.withOpacity(0.5)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Spacer(),
                      widget.args.isAdmin
                          ? Text(language.translate("add_ekub"),
                              style: buttonText)
                          : Text(language.translate("join_ekub"),
                              style: buttonText),
                      const Spacer(),
                      Align(
                        widthFactor: 2,
                        alignment: Alignment.centerRight,
                        child: _onProcess
                            ? const Padding(
                                padding: EdgeInsets.all(8.0),
                                child: SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                  ),
                                ),
                              )
                            : Container(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  checkTimeline(value) {
    setState(() {
      selectedTimeline = value;
    });
    if (startDateController.text.isNotEmpty &&
        dropdownControl2.text.isNotEmpty) {
      if (amountControl.text.isEmpty) {
        expectedControl.text = "0.0";
        if (selectedTimeline == "105 days") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: 105));
          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 105;
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "210 days") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: 210));

          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 210;
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "315 days") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: 315));

          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 315;
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "50 weeks") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: (50 * 7)));

          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 50 * 7;
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "100 weeks") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: (100 * 7)));

          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 100 * 7;
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "150 weeks") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: (150 * 7)));

          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 150 * 7;
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "12 months") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: (12 * 30)));

          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 12 * 30;
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "24 months") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: (24 * 30)));

          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 24 * 30;
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "36 months") {
          var endDate = DateTime.parse(startDateController.text)
              .add(const Duration(days: (36 * 30)));

          String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
          setState(() {
            timeline = 36 * 30;
            endDateController.text = formattedDate;
          });
        }
      } else {
        if (selectedTimeline == "105 days") {
          setState(() {
            timeline = 105;

            expectedControl.text =
                (double.parse(amountControl.text) * 105.0).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 105));
            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "210 days") {
          setState(() {
            timeline = 210;

            expectedControl.text =
                (double.parse(amountControl.text) * 210.0).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 210));

            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "315 days") {
          setState(() {
            timeline = 315;

            expectedControl.text =
                (double.parse(amountControl.text) * 315.0).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 315));

            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "50 weeks") {
          setState(() {
            timeline = 50 * 7;

            expectedControl.text =
                (double.parse(amountControl.text) * 50 * 7).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 50 * 7));

            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "100 weeks") {
          setState(() {
            timeline = 100 * 7;

            expectedControl.text =
                (double.parse(amountControl.text) * 100 * 7).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 100 * 7));

            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "150 weeks") {
          setState(() {
            timeline = 150 * 7;

            expectedControl.text =
                (double.parse(amountControl.text) * 150 * 7).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 150 * 7));

            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "12 months") {
          setState(() {
            timeline = 12 * 30;

            expectedControl.text =
                (double.parse(amountControl.text) * 12 * 30).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 12 * 30));

            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "24 months") {
          setState(() {
            timeline = 24 * 30;

            expectedControl.text =
                (double.parse(amountControl.text) * 24 * 30).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 24 * 30));

            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
        if (selectedTimeline == "36 months") {
          setState(() {
            timeline = 36 * 30;

            expectedControl.text =
                (double.parse(amountControl.text) * 36 * 30).toString();
            var endDate = DateTime.parse(startDateController.text)
                .add(const Duration(days: 36 * 30));

            String formattedDate = DateFormat('yyyy-MM-dd').format(endDate);
            endDateController.text = formattedDate;
          });
        }
      }
    }
  }

  Future<dynamic> showEqubInfo(BuildContext context) {
    return PanaraInfoDialog.show(
      context,
      title: widget.slectedEqubType.name!,
      message: widget.slectedEqubType.type == "Automatic"
          ? "${EkubLocalization.of(context)!.translate("automatic_equb_msg1")}. ${EkubLocalization.of(context)!.translate("starts")} ${formatDate(widget.slectedEqubType.startDate!)} ${EkubLocalization.of(context)!.translate("and")} ${EkubLocalization.of(context)!.translate("ends")} ${formatDate(widget.slectedEqubType.endDate!)}. ${EkubLocalization.of(context)!.translate("automatic_equb_msg2")}."
          : EkubLocalization.of(context)!.translate("manual_equb_msg"),
      buttonText: EkubLocalization.of(context)!.translate("okay"),
      onTapDismiss: () {
        Navigator.pop(context);
      },
      panaraDialogType: PanaraDialogType.normal,
    );
  }

  TextButton termsAndConditions(BuildContext context) {
    return TextButton(
      onPressed: () {
        showModalBottomSheet(
            context: context,
            builder: (builder) {
              return Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 20),
                        child: Text(
                            "${EkubLocalization.of(context)!.translate("terms")} ${widget.slectedEqubType.name}",
                            style: TextStyle(
                              color: themeProvider.getColor,
                              fontWeight: FontWeight.bold,
                              fontSize: fontBig,
                            )),
                      ),
                      Divider(
                        color: Colors.grey.shade400,
                      ),
                      widget.slectedEqubType.term == null
                          ? Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 15),
                              child: Text(
                                  "${EkubLocalization.of(context)!.translate("no_terms")}.",
                                  style: const TextStyle(
                                    color: Colors.black38,
                                    fontWeight: normalFontWeight,
                                    fontSize: fontMedium,
                                  )),
                            )
                          : Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 15),
                              child: HtmlWidget(
                                "${widget.slectedEqubType.term}",
                              )),
                    ],
                  ));
            });
      },
      child: Text(
        EkubLocalization.of(context)!.translate("accept_terms_conditions"),
        overflow: TextOverflow.clip,
        style: TextStyle(
          color: Colors.blue[600],
          fontSize: fontMedium,
          decoration: TextDecoration.underline,
          overflow: TextOverflow.clip,
        ),
      ),
    );
  }

  expectedAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("expected"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          style: lableStyle,
          readOnly: true,
          controller: expectedControl,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
                height: 10,
                width: 10,
                decoration: BoxDecoration(
                    color: ColorProvider.backgroundColor,
                    shape: BoxShape.circle),
                child: const Icon(
                  Icons.money,
                  size: 20,
                ),
              ),
              enabled: false,
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(18),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              enabledBorder: InputBorder.none,
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              fillColor: Colors.grey.shade200,
              filled: true,
              hintText: "5000",
              hintStyle: hintStyle),
          // validator: (value) => Sanitizer().isValidField(value!, "Expected"),
        ),
      ],
    );
  }

  amountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            EkubLocalization.of(context)!.translate("amount"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
            style: lableStyle,
            controller: amountControl,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
                prefixIcon: Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 3, vertical: 3),
                  height: 10,
                  width: 10,
                  decoration: BoxDecoration(
                      color: ColorProvider.backgroundColor,
                      shape: BoxShape.circle),
                  child: const Icon(
                    Icons.payment,
                    size: 20,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(18),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(18),
                  borderSide: BorderSide(color: themeProvider.getColor),
                ),
                errorBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                    color: Colors.red,
                  ),
                  borderRadius: BorderRadius.circular(18),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(18),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
                fillColor: Colors.white,
                filled: true,
                hintText: "1000",
                hintStyle: hintStyle),
            validator: (value) => Sanitizer().isValidField(value!,
                EkubLocalization.of(context)!.translate("amount"), context),
            onChanged: (value) {
              if (dropdownControl2.text.isNotEmpty) {
                if (amountControl.text.isEmpty) {
                  expectedControl.text = "0.0";
                } else {
                  if (selectedTimeline == "105 days") {
                    setState(() {
                      timeline = 105;
                      expectedControl.text =
                          (double.parse(amountControl.text) * 105.0).toString();
                    });
                  }
                  if (selectedTimeline == "210 days") {
                    setState(() {
                      timeline = 210;

                      expectedControl.text =
                          (double.parse(amountControl.text) * 210.0).toString();
                    });
                  }
                  if (selectedTimeline == "315 days") {
                    setState(() {
                      timeline = 315;

                      expectedControl.text =
                          (double.parse(amountControl.text) * 315.0).toString();
                    });
                  }
                  if (selectedTimeline == "50 weeks") {
                    setState(() {
                      timeline = 50 * 7;

                      expectedControl.text =
                          (double.parse(amountControl.text) * 50 * 7)
                              .toString();
                    });
                  }
                  if (selectedTimeline == "100 weeks") {
                    setState(() {
                      timeline = 100 * 7;

                      expectedControl.text =
                          (double.parse(amountControl.text) * 100 * 7)
                              .toString();
                    });
                  }
                  if (selectedTimeline == "150 weeks") {
                    setState(() {
                      timeline = 150 * 7;

                      expectedControl.text =
                          (double.parse(amountControl.text) * 150 * 7)
                              .toString();
                    });
                  }
                  if (selectedTimeline == "12 months") {
                    setState(() {
                      timeline = 12 * 30;

                      expectedControl.text =
                          (double.parse(amountControl.text) * 12 * 30)
                              .toString();
                    });
                  }
                  if (selectedTimeline == "24 months") {
                    setState(() {
                      timeline = 24 * 30;

                      expectedControl.text =
                          (double.parse(amountControl.text) * 24 * 30)
                              .toString();
                    });
                  }
                  if (selectedTimeline == "36 months") {
                    setState(() {
                      timeline = 36 * 30;

                      expectedControl.text =
                          (double.parse(amountControl.text) * 36 * 30)
                              .toString();
                    });
                  }
                }
              } else if (startDateController.text.isNotEmpty &&
                  endDateController.text.isNotEmpty) {
                int days = calculateDateDifference(
                    startDateController.text, endDateController.text);
                setState(() {
                  expectedControl.text =
                      (double.parse(value) * days).toString();
                });
              }
            }),
      ],
    );
  }

  _datePicker(TextEditingController controller, String text,
      DateTime initialDate, DateTime firstDate, DateTime lastDate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            text,
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          keyboardType: TextInputType.none,
          controller: controller,
          style: lableStyle,
          decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
                height: 10,
                width: 10,
                decoration: BoxDecoration(
                    color: ColorProvider.backgroundColor,
                    shape: BoxShape.circle),
                child: const Icon(
                  Icons.date_range_outlined,
                  size: 20,
                ),
              ),
              enabled: widget.slectedEqubType.type == "Automatic"
                  ? false
                  : controller != endDateController,
              border: OutlineInputBorder(
                borderSide: BorderSide(color: themeProvider.getColor),
                borderRadius: BorderRadius.circular(18),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              fillColor: widget.slectedEqubType.type!.toLowerCase() ==
                          "automatic" ||
                      (widget.slectedEqubType.type!.toLowerCase() == "manual" &&
                          controller == endDateController)
                  ? Colors.grey.shade200
                  : Colors.white,
              filled: true,
              hintText: formatDate(DateTime.now().toString()),
              hintStyle: hintStyle),
          validator: (value) => Sanitizer().isValidField(value!, text, context),
          onChanged: (value) {
            if (controller.text.isNotEmpty &&
                dropdownControl2.text.isNotEmpty) {
              if (amountControl.text.isEmpty) {
                expectedControl.text = "0.0";
              } else {
                if (selectedTimeline == "105 days") {
                  timeline = 105;
                  expectedControl.text =
                      (double.parse(amountControl.text) * 105.0).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 105));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                if (selectedTimeline == "210 days") {
                  timeline = 210;

                  expectedControl.text =
                      (double.parse(amountControl.text) * 210.0).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 210));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                if (selectedTimeline == "315 days") {
                  timeline = 315;

                  expectedControl.text =
                      (double.parse(amountControl.text) * 315.0).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 315));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                if (selectedTimeline == "50 weeks") {
                  timeline = 50 * 7;

                  expectedControl.text =
                      (double.parse(amountControl.text) * 50 * 7).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 50 * 7));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                if (selectedTimeline == "100 weeks") {
                  timeline = 100 * 7;

                  expectedControl.text =
                      (double.parse(amountControl.text) * 100 * 7).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 100 * 7));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                if (selectedTimeline == "150 weeks") {
                  timeline = 150 * 7;

                  expectedControl.text =
                      (double.parse(amountControl.text) * 150 * 7).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 150 * 7));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                if (selectedTimeline == "12 months") {
                  timeline = 12 * 30;

                  expectedControl.text =
                      (double.parse(amountControl.text) * 12 * 30).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 12 * 30));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                if (selectedTimeline == "24 months") {
                  timeline = 24 * 30;

                  expectedControl.text =
                      (double.parse(amountControl.text) * 24 * 30).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 24 * 30));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                if (selectedTimeline == "36 months") {
                  timeline = 36 * 30;

                  expectedControl.text =
                      (double.parse(amountControl.text) * 36 * 30).toString();
                  var endDate = DateTime.parse(startDateController.text)
                      .add(const Duration(days: 36 * 30));

                  String formattedDate =
                      DateFormat('yyyy-MM-dd').format(endDate);
                  endDateController.text = formattedDate;
                }
                setState(() {});
              }
            }
          },
          onTap: () async {
            DateTime? pickedDate = await showDatePicker(
                locale: const Locale("en"),
                context: context,
                initialDate: initialDate,
                firstDate: firstDate,
                lastDate: lastDate);

            if (pickedDate != null) {
              setState(() {
                startDate = initialDate;
                startDate = pickedDate;
              });

              String formattedDate =
                  DateFormat('yyyy-MM-dd').format(pickedDate);

              controller.text = formattedDate;
              if (selectedTimeline == "105 days") {
                var endDate = pickedDate.add(const Duration(days: 105));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              if (selectedTimeline == "210 days") {
                var endDate = pickedDate.add(const Duration(days: 210));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              if (selectedTimeline == "315 days") {
                var endDate = pickedDate.add(const Duration(days: 315));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              if (selectedTimeline == "50 weeks") {
                var endDate = pickedDate.add(const Duration(days: 50 * 7));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              if (selectedTimeline == "100 weeks") {
                var endDate = pickedDate.add(const Duration(days: 100 * 7));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              if (selectedTimeline == "150 weeks") {
                var endDate = pickedDate.add(const Duration(days: 150 * 7));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              if (selectedTimeline == "12 months") {
                var endDate = pickedDate.add(const Duration(days: 12 * 30));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              if (selectedTimeline == "24 months") {
                var endDate = pickedDate.add(const Duration(days: 24 * 30));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              if (selectedTimeline == "36 months") {
                var endDate = pickedDate.add(const Duration(days: 36 * 30));
                String formattedEnd = DateFormat('yyyy-MM-dd').format(endDate);
                endDateController.text = formattedEnd;
              }
              setState(() {});
            } else {}
          },
        ),
      ],
    );
  }

  _addEqub(
      int memberId,
      int typeId,
      String amount,
      String totalAmount,
      String startDate,
      String endDate,
      String lotteryDate,
      String timeline,
      String type) async {
    print('Member ID: $memberId');
    print('Type ID: $typeId');
    print('Amount: $amount');
    print('Total Amount: $totalAmount');
    print('Start Date: $startDate');
    print('End Date: $endDate');
    print('Lottery Date: $lotteryDate');
    print('Timeline: $timeline');

    var sender = EqubDataProvider(httpClient: http.Client());
    var response = MemberProvider(httpClient: http.Client())
        .getMember(context, member!.id.toString());

    response.then((val) {
      if (val.status == "Active" || widget.args.user!.role != "member") {
        var res = sender.addEqubToMember(
          context,
          memberId,
          typeId,
          amount,
          totalAmount,
          startDate,
          endDate,
          lotteryDate,
          timeline,
          type,
        );
        res
            .then((value) => {
                  if (value["code"] == 200)
                    {
                      PanaraInfoDialog.show(
                        context,
                        title:
                            EkubLocalization.of(context)!.translate("success"),
                        message: widget.args.isAdmin
                            ? value["message"]
                            : EkubLocalization.of(context)!
                                .translate("subscribe_equb_msg"),
                        buttonText:
                            EkubLocalization.of(context)!.translate("okay"),
                        onTapDismiss: () {
                          Navigator.pop(context);
                          // Navigator.pop(context);
                          if (widget.args.user!.role == "equb_collector") {
                            Navigator.pushAndRemoveUntil(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => MembersScreen(
                                            args: MemberEqubsArgs(
                                          member: Member(
                                              id: int.parse(
                                                  widget.args.user!.id ?? "0"),
                                              fullName:
                                                  widget.args.user!.fullName,
                                              phone:
                                                  widget.args.user!.phoneNumber,
                                              gender: widget.args.user!.gender),
                                          isOnline: true,
                                          // role: args.role,
                                        ))),
                                (route) => false);
                          } else {
                            _openHomeScreen(
                                widget.args.user!.role!.toLowerCase() ==
                                    "admin",
                                widget.args.user!.role!);
                          }
                        },
                        imagePath: successDialogIcon,
                        panaraDialogType: PanaraDialogType.success,
                      ),
                      setState(() {
                        _onProcess = false;
                      })
                    }
                  else
                    {
                      PanaraInfoDialog.show(
                        context,
                        title: EkubLocalization.of(context)!.translate("error"),
                        message: value["message"],
                        buttonText:
                            EkubLocalization.of(context)!.translate("okay"),
                        onTapDismiss: () {
                          Navigator.pop(context);
                          // Navigator.pop(context);
                        },
                        imagePath: errorDialogIcon,
                        panaraDialogType: PanaraDialogType.error,
                      ),
                      setState(() {
                        _onProcess = false;
                      })
                    }
                })
            .onError((error, stackTrace) {
          setState(() {
            _onProcess = false;
          });
          if (mounted) {
            PanaraConfirmDialog.show(context,
                // title: "Error ",
                message: error.toString(),
                confirmButtonText:
                    EkubLocalization.of(context)!.translate("try_again"),
                imagePath: errorDialogIcon,
                cancelButtonText: EkubLocalization.of(context)!
                    .translate("cancel"), onTapConfirm: () {
              Navigator.pop(context);
              _addEqub(memberId, typeId, amount, totalAmount, startDate,
                  endDate, lotteryDate, timeline, type);
            }, onTapCancel: () {
              Navigator.pop(context);
            }, panaraDialogType: PanaraDialogType.error);
          }
          setState(() {
            _onProcess = false;
          });
          return {};
        });
      } else {
        setState(() {
          _onProcess = false;
        });
        PanaraInfoDialog.show(
          context,
          title: '',
          message: EkubLocalization.of(context)!.translate("inactive_user"),
          buttonText: EkubLocalization.of(context)!.translate("okay"),
          onTapDismiss: () {
            Navigator.pop(context);
            // Navigator.pop(context);
          },
          // imagePath: errorDialogIcon,
          panaraDialogType: PanaraDialogType.normal,
        );
      }
    });
  }

  int calculateDateDifference(String startDate, String endDate) {
    return DateTime.parse(endDate).difference(DateTime.parse(startDate)).inDays;
  }

  _openHomeScreen(bool isAdmin, String role) async {
    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);
    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }
}
