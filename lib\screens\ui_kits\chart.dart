import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import '../../utils/constants.dart';

class Chart extends StatelessWidget {
  final double height;
  const Chart({
    super.key,required this.height
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: Stack(
        children: [
          PieChart(
            PieChartData(
              sectionsSpace: 0,
              centerSpaceRadius: 70,
              startDegreeOffset: -90,
              sections: paiChartSelectionDatas,
            ),
          ),
          Positioned.fill(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: defaultPadding),
                Text(
                  "29.1 Birr",
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                        //color: Colors.white,
                        fontWeight: FontWeight.w600,
                        height: 0.5,fontSize: 13
                      ),
                ),
                const Text("of 128 Birr",style: TextStyle(fontSize: 13),)
              ],
            ),
          ),
        ],
      ),
    );
  }
}

List<PieChartSectionData> paiChartSelectionDatas = [
  PieChartSectionData(
    color: const Color(0xFF26E5FF),
    value: 25,
    showTitle: false,
    radius: 20,
  ),
  PieChartSectionData(
    color: const Color(0xFFFFCF26),
    value: 25,
    showTitle: false,
    radius: 20,
  ),
  PieChartSectionData(
    color: const Color(0xFFEE2727).withOpacity(0.1),
    value: 25,
    showTitle: false,
    radius: 20,
  ),
  PieChartSectionData(
    color: const Color(0xFFEE2727),
    value: 25,
    showTitle: false,
    radius: 20,
  ),
];

