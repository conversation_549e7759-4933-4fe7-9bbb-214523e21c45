// include ':app'

// def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
// def properties = new Properties()

// assert localPropertiesFile.exists()
// localPropertiesFile.withReader("UTF-8") { reader -> properties.load(reader) }

// def flutterSdkPath = properties.getProperty("flutter.sdk")
// assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
// apply from: "$flutterSdkPath/packages/flutter_tools/gradle/app_plugin_loader.gradle"


// settings.gradle

// Configure Flutter SDK and plugins
pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }
    settings.ext.flutterSdkPath = flutterSdkPath()

    includeBuild("${settings.ext.flutterSdkPath}/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.2"
    id "com.android.application" version "7.3.0" apply false
    // START: FlutterFire Configuration
    id "com.google.gms.google-services" version "4.3.15" apply false
    // END: FlutterFire Configuration
    id "org.jetbrains.kotlin.android" version "1.8.10" apply false
   // id("com.google.gms.google-services") version "4.4.1" apply false

}

include ":app"