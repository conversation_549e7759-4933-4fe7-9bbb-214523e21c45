import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

extension ToRadians on int {
  double get toRadians => this * (math.pi / 180.0);
}

enum _ButtonType { like, dislike }

class LikeOrNot extends StatelessWidget {
  static const routeName = "/like_not";
  final VoidCallback onLike;
  final VoidCallback onDislike;

  final _gapSizeRatio = 0.02;

  final _likeIconColor = const Color(0xffb85076);
  final _dislikeIconColor = Colors.white;

  const LikeOrNot({
    super.key,
    required this.onLike,
    required this.onDislike,
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 2,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final buttonPaddings = constraints.maxHeight * 0.1;
          final halfWidth = constraints.maxWidth / 2;
          return Stack(
            children: [
              Positioned.fill(
                child: CustomPaint(
                  painter: R<PERSON><PERSON>ustomPainter(
                    gapSizeRatio: _gapSizeRatio,
                  ),
                ),
              ),
              Positioned(
                left: 0,
                bottom: 0,
                top: 0,
                right: halfWidth + constraints.maxWidth * _gapSizeRatio,
                child: SizedBox.expand(
                  child: Padding(
                    padding: EdgeInsets.all(buttonPaddings),
                    child: _buildButton(_ButtonType.dislike),
                  ),
                ),
              ),
              Positioned(
                right: 0,
                bottom: 0,
                top: 0,
                left: halfWidth + constraints.maxWidth * _gapSizeRatio,
                child: SizedBox.expand(
                  child: Padding(
                    padding: EdgeInsets.all(buttonPaddings),
                    child: _buildButton(_ButtonType.like),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildButton(_ButtonType buttonType) {
    final isPositiveAction = buttonType == _ButtonType.like;

    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        foregroundColor: isPositiveAction ? _likeIconColor : _dislikeIconColor, backgroundColor: isPositiveAction ? _dislikeIconColor : _likeIconColor, shape: const CircleBorder(),
        padding: EdgeInsets.zero,
        elevation: 10,
        shadowColor: Colors.black54,
      ),
      onPressed: onDislike,
      child: FractionallySizedBox(
        widthFactor: 0.35,
        heightFactor: 0.35,
        child: FittedBox(
          child: isPositiveAction
              ? const FaIcon(FontAwesomeIcons.heart)
              : const Icon(Icons.close),
        ),
      ),
    );
  }
}

class RPSCustomPainter extends CustomPainter {
  final double gapSizeRatio;

  RPSCustomPainter({
    required this.gapSizeRatio,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.08)
      ..style = PaintingStyle.fill
      ..strokeWidth = 1;

    final path = Path();

    final gapSize = size.width * gapSizeRatio;
    final arcRadius = size.height / 2 - gapSize / 2;

    final leftCircleCenter = Offset(
      size.width * 0.25 - gapSize / 2,
      size.height / 2,
    );
    final rightCircleCenter = Offset(
      size.width * 0.75 + gapSize / 2,
      size.height / 2,
    );

    path.arcTo(
      Rect.fromCircle(
        center: leftCircleCenter,
        radius: arcRadius,
      ),
      45.toRadians,
      270.toRadians,
      false,
    );

    final bezierOffset = arcRadius * (105 / 360);

    path.quadraticBezierTo(
      size.width / 2,
      size.height * 0.30,
      rightCircleCenter.dx - arcRadius + bezierOffset,
      rightCircleCenter.dy - arcRadius + bezierOffset,
    );

    path.arcTo(
      Rect.fromCircle(
        center: rightCircleCenter,
        radius: arcRadius,
      ),
      225.toRadians,
      270.toRadians,
      false,
    );

    path.quadraticBezierTo(
      size.width / 2,
      size.height * 0.70,
      leftCircleCenter.dx + arcRadius - bezierOffset,
      leftCircleCenter.dy + arcRadius - bezierOffset,
    );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}