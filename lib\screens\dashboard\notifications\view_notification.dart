// ignore_for_file: must_be_immutable

import 'package:ekub/routs/shared.dart';
import 'package:flutter/material.dart';

import '../../ui_kits/app_bar.dart';

class ViewNTFS extends StatefulWidget {
  static const routeName = "/view_ntfs";
  NtfsDetailArgs args;
  ViewNTFS({super.key, required this.args});

  @override
  State<ViewNTFS> createState() => _ViewNTFSState();
}

class _ViewNTFSState extends State<ViewNTFS> {
  final _appBar = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: EkubAppBar(key: _appBar,title: "Notification",widgets: const []),
      body: SizedBox(
        width: double.infinity,
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          Text(
            'Notification Detail',
            style: Theme.of(context).textTheme.displaySmall,
          ),
        ]),
      ),
    );
  }
}
