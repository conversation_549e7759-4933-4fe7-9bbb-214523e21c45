// ignore_for_file: must_be_immutable

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/ui_kits/app_bar.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/global_constants.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';

import '../../../../repository/ekub_localization.dart';
import '../../../../repository/member_repos.dart';
import '../../../../utils/constants.dart';
import '../../../../utils/validator.dart';
import '../../../settings/constant.dart';
import '../../../themes/ThemeProvider.dart';

class EditMember extends StatefulWidget {
  static const routeName = "/add_member";
  EditMemberArgs args;
  EditMember({super.key, required this.args});

  @override
  State<EditMember> createState() => _EditMemberState();
}

class _EditMemberState extends State<EditMember> {
  bool invisible = false;
  bool _onProcess = false;
  bool isSubmitted = false;
  late bool forEdit;
  Member? member;
  String _selectedCity = "";
  String _selectedGender = "Male";
  final Map<String, dynamic> _member = {};

  late ThemeProvider themeProvider;
  late TextEditingController userNameControl;
  late TextEditingController emailControl;
  late TextEditingController phoneControl;
  late TextEditingController locationControl;
  late TextEditingController woredaControl;
  late TextEditingController subCityControl;
  late TextEditingController cityControl;
  late TextEditingController houseControl;
  final dropdownControl = TextEditingController();
  final _registerFormKey = GlobalKey<FormState>();
  final _appBar = GlobalKey<FormState>();

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  @override
  void initState() {
    _onProcess = false;
    userNameControl = TextEditingController(
        text: widget.args.forEdit ? widget.args.member.fullName ?? "" : "");
    emailControl = TextEditingController(
        text: widget.args.forEdit ? widget.args.member.email ?? "" : "");
    phoneControl = TextEditingController(
        text: widget.args.forEdit ? widget.args.member.phone ?? "" : "");
    locationControl = TextEditingController(
        text: widget.args.forEdit ? widget.args.member.location ?? "" : "");
    woredaControl = TextEditingController(
        text: widget.args.forEdit ? widget.args.member.woreda ?? "" : "");
    subCityControl = TextEditingController(
        text: widget.args.forEdit ? widget.args.member.subCity ?? "" : "");
    _selectedCity = widget.args.forEdit ? widget.args.member.city ?? "" : "";
    dropdownControl.text =
        widget.args.forEdit ? widget.args.member.subCity ?? "" : "";
    houseControl = TextEditingController(
        text: widget.args.forEdit ? widget.args.member.houseNumber ?? "" : "");
    _selectedCity = widget.args.forEdit ? widget.args.member.city ?? "" : "";
    _selectedGender = widget.args.member.gender ?? "Male";

    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: themeProvider.getColor,
      statusBarIconBrightness: Brightness.light,
    ));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorProvider.backgroundColor,
        appBar: EkubAppBar(
          key: _appBar,
          title: EkubLocalization.of(context)!.translate("edit_member"),
          widgets: const [],
        ),
        body: Form(
          key: _registerFormKey,
          autovalidateMode: isSubmitted
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.disabled,
          child: Stack(
            children: <Widget>[
              Align(
                alignment: Alignment.center,
                child: ListView(
                  children: <Widget>[
                    Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25)),
                      margin: const EdgeInsets.fromLTRB(25, 20, 25, 10),
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                      child: Column(
                        children: <Widget>[
                          const SizedBox(
                            height: 15,
                          ),
                          _nameTextField(),
                          const SizedBox(
                            height: 10,
                          ),
                          _phoneNumberTextField(),
                          const SizedBox(
                            height: 10,
                          ),
                          _emailField(),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(18),
                            ),
                            child: Row(
                              children: [
                                Radio<String>(
                                  activeColor: themeProvider.getColor,
                                  value: 'Male',
                                  groupValue: _selectedGender,
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedGender = value!;
                                      _member["gender"] = value;
                                    });
                                  },
                                ),
                                Text(
                                    EkubLocalization.of(context)!
                                        .translate("male"),
                                    style: hintStyle),
                                Radio<String>(
                                  value: 'Female',
                                  activeColor: themeProvider.getColor,
                                  groupValue: _selectedGender,
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedGender = value!;
                                      _member["gender"] = value;
                                    });
                                  },
                                ),
                                Text(
                                  EkubLocalization.of(context)!
                                      .translate("female"),
                                  style: hintStyle,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          _cityDropdown(),
                          const SizedBox(
                            height: 10,
                          ),
                          _subCity(),
                          const SizedBox(
                            height: 10,
                          ),
                          _woredaField(),
                          const SizedBox(
                            height: 10,
                          ),
                          _houseNumberField(),
                          const SizedBox(
                            height: 10,
                          ),
                          _locationField(),
                          const SizedBox(
                            height: 25,
                          ),
                          ElevatedButton(
                              onPressed: _onProcess
                                  ? null
                                  : () async {
                                      setState(() {
                                        isSubmitted = true;
                                      });
                                      final form =
                                          _registerFormKey.currentState;
                                      if (form!.validate()) {
                                        form.save();
                                        if (!await InternetConnectivity()
                                            .checkInternetConnectivty(
                                                context, true)) {
                                          setState(() {
                                            _onProcess = false;
                                          });
                                          return;
                                        } else {
                                          setState(() {
                                            _onProcess = true;
                                          });
                                          _editMember();
                                        }
                                      }
                                    },
                              child: Container(
                                height: 50,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20),
                                    color: themeProvider.getColor),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Spacer(),
                                    Text(
                                        EkubLocalization.of(context)!
                                            .translate("update"),
                                        style: buttonText),
                                    const Spacer(),
                                    Align(
                                      widthFactor: 2,
                                      alignment: Alignment.centerRight,
                                      child: _onProcess
                                          ? const Padding(
                                              padding: EdgeInsets.all(8.0),
                                              child: SizedBox(
                                                height: 20,
                                                width: 20,
                                                child:
                                                    CircularProgressIndicator(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            )
                                          : Container(),
                                    ),
                                  ],
                                ),
                              )),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  TextFormField _nameTextField() {
    return TextFormField(
      style: lableStyle,
      controller: userNameControl,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.person_2_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          hintText: EkubLocalization.of(context)!.translate("full_name"),
          hintStyle: hintStyle),
      validator: (value) => Sanitizer().isFullNameValid(value!, true, context),
      onSaved: (value) {
        _member['full_name'] = value;
      },
    );
  }

  TextFormField _phoneNumberTextField() {
    return TextFormField(
      style: lableStyle,
      controller: phoneControl,
      maxLength: 13,
      keyboardType: TextInputType.phone,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.phone_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          counterText: "",
          hintText: EkubLocalization.of(context)!.translate("phone_number"),
          hintStyle: hintStyle),
      validator: (value) => Sanitizer().isPhoneValid(value!, true, context),
    );
  }

  // _cityDropdown() {
  //   return Container(
  //     padding: const EdgeInsets.symmetric(horizontal: 10),
  //     decoration: BoxDecoration(
  //         borderRadius: BorderRadius.circular(10),
  //         color: Colors.white,
  //         border: Border.all(color: Colors.grey.shade400)),
  //     child: DropdownButton<String>(
  //       icon: Container(
  //         margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
  //         height: 38,
  //         width: 38,
  //         decoration: BoxDecoration(
  //             color: ColorProvider.backgroundColor, shape: BoxShape.circle),
  //         child: Icon(
  //           Icons.keyboard_arrow_down,
  //           size: 25,
  //           color: themeProvider.getColor,
  //         ),
  //       ),
  //       dropdownColor: Colors.grey[100],
  //       iconDisabledColor: themeProvider.getColor,
  //       elevation: 0,
  //       underline: Container(),
  //       borderRadius: BorderRadius.circular(10),
  //       style: hintStyle,
  //       isExpanded: true,
  //       value: _selectedCity,
  //       items: cities.map<DropdownMenuItem<String>>((String city) {
  //         return DropdownMenuItem<String>(
  //           value: city,
  //           onTap: () {
  //             // Prevent selecting disabled items
  //             if (city != 'Addis Ababa') {
  //               ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //                 content: Text('$city is disabled'),
  //               ));
  //             }
  //           },
  //           // Disable items other than 'New York'
  //           enabled: city == 'Addis Ababa',
  //           child: Text(
  //             city,
  //             style: city == 'Addis Ababa'
  //                 ? lableStyle
  //                 : TextStyle(color: Colors.grey.shade400),
  //           ),
  //         );
  //       }).toList(),
  //       onChanged: (String? newValue) {
  //         setState(() {
  //           _selectedCity = newValue!;
  //         });
  //       },
  //     ),
  //   );
  // }

  TextFormField _emailField() {
    return TextFormField(
      style: lableStyle,
      controller: emailControl,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.email_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          hintText: EkubLocalization.of(context)!.translate("email"),
          hintStyle: hintStyle),
      validator: (value) => Sanitizer().isValidEmail(value!, context),
      onSaved: (value) {
        _member["email"] = value;
      },
    );
  }

  TextFormField _locationField() {
    return TextFormField(
      // initialValue: widget.args.member.location ?? "",
      style: lableStyle,
      controller: locationControl,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.location_searching_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          fillColor: Colors.white,
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          filled: true,
          hintText:
              EkubLocalization.of(context)!.translate("specific_location"),
          hintStyle: hintStyle),
      validator: (value) => Sanitizer().isValidField(
          value!,
          EkubLocalization.of(context)!.translate("specific_location"),
          context),
      onSaved: (value) {
        _member["location"] = value;
      },
    );
  }

  TextFormField _woredaField() {
    return TextFormField(
      // initialValue: widget.args.member.woreda ?? "",

      style: lableStyle,
      controller: woredaControl,
      decoration: InputDecoration(
          border: const OutlineInputBorder(
              borderSide: BorderSide(style: BorderStyle.solid)),
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.location_city_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          hintText: EkubLocalization.of(context)!.translate("woreda"),
          hintStyle: hintStyle),
      validator: (value) => Sanitizer().checkLength(
          value!, EkubLocalization.of(context)!.translate("woreda"), context),
      onSaved: (value) {
        _member['woreda'] = value;
      },
    );
  }

  TextFormField _houseNumberField() {
    return TextFormField(
      style: lableStyle,
      controller: houseControl,
      decoration: InputDecoration(
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.house_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          hintText: EkubLocalization.of(context)!.translate("house_number"),
          hintStyle: hintStyle),
      validator: (value) => Sanitizer().checkLength(value!,
          EkubLocalization.of(context)!.translate("house_number"), context),
      onSaved: (value) {
        _member['housenumber'] = value;
      },
    );
  }

  _subCity() {
    return

        // CustomDropdown<String>.search(
        //   initialItem: widget.user.subCity,
        //   onChanged: (value) {
        //     setState(() {
        //       widget.user.subCity = value;
        //     });
        //   },
        //   hintText: EkubLocalization.of(context)!.translate("sub_city"),
        //   decoration: CustomDropdownDecoration(
        //     closedBorder: Border.all(color: Colors.grey.shade400),
        //     closedFillColor: Colors.white,
        //     hintStyle: hintStyle,
        //     closedSuffixIcon: Container(
        //       padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 0),
        //       decoration: BoxDecoration(
        //           color: ColorProvider.backgroundColor, shape: BoxShape.circle),
        //       child: Icon(
        //         Icons.keyboard_arrow_down,
        //         size: 25,
        //         color: themeProvider.getColor,
        //       ),
        //     ),
        //   ),

        //   items: subCities,
        //   // errorText:
        //   //     '${EkubLocalization.of(context)!.translate("valid_field")} ${EkubLocalization.of(context)!.translate("sub_city")}',
        // );

        TextFormField(
      initialValue: subCityControl.text,
      style: lableStyle,
      decoration: InputDecoration(
          border: const OutlineInputBorder(
              borderSide: BorderSide(style: BorderStyle.solid)),
          prefixIcon: Container(
            margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
            height: 10,
            width: 10,
            decoration: BoxDecoration(
                color: ColorProvider.backgroundColor, shape: BoxShape.circle),
            child: const Icon(
              Icons.signpost_outlined,
              size: 20,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: Colors.grey.shade400),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: BorderSide(color: themeProvider.getColor),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.red,
            ),
            borderRadius: BorderRadius.circular(18),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          fillColor: Colors.white,
          filled: true,
          hintText: EkubLocalization.of(context)!.translate("sub_city"),
          hintStyle: hintStyle),
      onSaved: (value) {
        subCityControl.text = value!;
      },
    );
  }

  _cityDropdown() {
    return CustomDropdown<String>.search(
      initialItem: _selectedCity,
      onChanged: (value) {
        setState(() {
          _selectedCity = value!;
        });
      },
      validator: (p0) {
        if (_selectedCity == "") {
          return EkubLocalization.of(context)!.translate("select_city_error");
        }
        return null;
      },
      hintText: EkubLocalization.of(context)!.translate("city"),
      decoration: CustomDropdownDecoration(
        closedBorder: Border.all(color: Colors.grey.shade400),
        closedFillColor: Colors.white,
        hintStyle: hintStyle,
        closedSuffixIcon: Container(
          padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 0),
          decoration: BoxDecoration(
              color: ColorProvider.backgroundColor, shape: BoxShape.circle),
          child: Icon(
            Icons.keyboard_arrow_down,
            size: 25,
            color: themeProvider.getColor,
          ),
        ),
      ),
      items: cities,
    );
  }

  _editMember() {
    var sender = MemberProvider(httpClient: http.Client());
    var res = sender.editMember(
        context,
        userNameControl.text,
        phoneControl.text,
        emailControl.text,
        _selectedGender,
        widget.args.member.id.toString(),
        woredaControl.text,
        _selectedCity,
        locationControl.text,
        houseControl.text,
        subCityControl.text);

    res
        .then((value) => {
              setState(() {
                _onProcess = false;
              }),
              if (value["code"].toString() == "200")
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("success"),
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                    imagePath: successDialogIcon,
                    panaraDialogType: PanaraDialogType.success,
                  ),
                  setState(() {
                    _onProcess = false;
                  })
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    title: EkubLocalization.of(context)!.translate("error"),
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                  })
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            // title: "Request Timeout",
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          _editMember();
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        _onProcess = false;
      });
      return {};
    });
  }
}
