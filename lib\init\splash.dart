// ignore_for_file: unused_element, use_build_context_synchronously

import 'dart:async';
import 'dart:io';

import 'package:ekub/init/language_select.dart';
import 'package:ekub/repository/language.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

import '../exports/screens.dart';
import '../exports/models.dart';
import '../repository/user_repos.dart';
import '../utils/tools.dart';

class SplashScreen extends StatefulWidget {
  static const routeName = "/change_password";

  const SplashScreen({super.key});
  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late User user;
  late ThemeProvider themeProvider;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  bool majorChangeDetected = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    // Set system UI overlay style based on theme
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: themeProvider.getColor,
      statusBarIconBrightness: Brightness.light,
    ));
  }

  @override
  void initState() {
    super.initState();
    // Initialize progress animation (reduced duration to 3 seconds)
    _progressController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _progressAnimation = Tween(begin: 0.0, end: 1.0)
        .animate(_progressController)
      ..addListener(() => setState(() {}));

    // Force English language at app start
    // WidgetsBinding.instance.addPostFrameCallback((_) async {
    //   final prefs = await SharedPreferences.getInstance();
    //   await prefs.setString('language', 'English');

    //   AppLanguage appLanguage = AppLanguage();
    //   appLanguage.changeLanguage(const Locale("en"));
    // });

    // Start version check and initialization flow
    _initializeApp();
    _progressController.forward();
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  /// Main initialization method that coordinates version checks and security checks
  void _initializeApp() async {
    try {
      // Check for app updates first
      await _checkForAppUpdates();

      // If no major update detected, proceed with security check and login
      if (!majorChangeDetected) {
        await _performSecurityCheck();
        // await _setDefaultLanguageAndNavigate();
        _proceedWithLogin();
      }
    } catch (e) {
      print('Initialization error: $e');
      // Fallback to login screen if any error occurs
      _openLoginScreen();
    }
  }

  /// Checks for required app updates from Firestore
  Future<void> _checkForAppUpdates() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final document = await FirebaseFirestore.instance
          .collection('virtual')
          .doc('version')
          .get()
          .timeout(const Duration(seconds: 5));

      if (document.exists) {
        final data = document.data() as Map<String, dynamic>;
        final firestoreVersion = data['version'] ?? '0.0.0+0';

        if (_isMajorChange(packageInfo.version, firestoreVersion)) {
          majorChangeDetected = true;
          _showUpdateDialog();
        }
      }
    } on TimeoutException {
      print('Version check timed out');
    } catch (e) {
      print('Version check error: $e');
    }
  }

  /// Performs device security check
  Future<void> _performSecurityCheck() async {
    try {
      final isSafeDevice = await isSecureDevice();
      if (!isSafeDevice) {
        _showSecurityWarningDialog();
      }
    } catch (e) {
      print('Security check error: $e');
    }
  }

  /// Shows mandatory update dialog
  void _showUpdateDialog() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Update Required'),
          content: const Text(
              'A major update is available. Please update the app to continue.'),
          actions: [
            TextButton(
              onPressed: () async {
                const url =
                    'https://play.google.com/store/apps/details?id=com.vintechplc.virtualequb&pcampaignid=web_share';
                if (await canLaunch(url)) {
                  await launch(url);
                }
                Navigator.of(context).pop();
              },
              child: const Text('Update Now'),
            ),
          ],
        ),
      );
    });
  }

  /// Shows security warning dialog for emulated devices
  void _showSecurityWarningDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Development Environment Detected"),
          content: const Text(
            "This app is running in a development environment (emulator or simulator). "
            "Some features may not work as expected. Please use a secure, non-rooted device "
            "for production use.",
          ),
          actions: <Widget>[
            TextButton(
              child: const Text("Continue Anyway"),
              onPressed: () {
                Navigator.of(context).pop();
                _proceedWithLogin();
              },
            ),
          ],
        );
      },
    );
  }

  /// Handles user authentication and navigation
  void _proceedWithLogin() async {
    try {
      final auth = AuthDataProvider(httpClient: http.Client());
      final userData =
          await auth.getUserData().timeout(const Duration(seconds: 5));

      if (userData.email != null &&
          userData.role != null &&
          userData.token != null) {
        _openHomeScreen(userData.role == "admin", userData.role ?? "none");
      } else {
        _openLoginScreen();
      }
    } on TimeoutException {
      print('User data fetch timed out');
      _openLoginScreen();
    } catch (e) {
      print('Login error: $e');
      _openLoginScreen();
    }
  }

  /// Determines if a version change is major
  bool _isMajorChange(String currentVersion, String firestoreVersion) {
    final currentParts = currentVersion.split('.');
    final firestoreParts = firestoreVersion.split('.');
    return int.parse(firestoreParts[0]) > int.parse(currentParts[0]);
  }

  /// Navigation methods
  Future<void> _openLoginScreen() async {
    final prefs = await SharedPreferences.getInstance();
    final hasLanguage = prefs.containsKey('language_code');
    final appLanguage = Provider.of<AppLanguage>(context, listen: false);
    await appLanguage.fetchLocale();
    debugPrint("Language: $hasLanguage");

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => hasLanguage
            ? kIsWeb
                ? WelcomeScreen(args: LoginScreenArgs(isOnline: true))
                : LoginScreen(args: LoginScreenArgs(isOnline: true))
            : const LanguageSelectionScreen(),
      ),
    );
  }

  Future<void> _setDefaultLanguageAndNavigate() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    AppLanguage appLanguage = Provider.of<AppLanguage>(context, listen: false);
    await appLanguage.changeLanguage(const Locale("en", "US"));
    await prefs.setString('language_code', 'en');
    await prefs.setString('country_code', 'US');

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => kIsWeb
            ? WelcomeScreen(args: LoginScreenArgs(isOnline: true))
            : LoginScreen(args: LoginScreenArgs(isOnline: true)),
      ),
    );
  }

  void _openHomeScreen(bool isAdmin, String role) {
    final argument = HomeScreenArgs(
      isOnline: true,
      isAdmin: isAdmin,
      role: role,
    );

    Navigator.pushNamedAndRemoveUntil(
      context,
      HomeScreen.routeName,
      (Route<dynamic> route) => false,
      arguments: argument,
    );
  }

  /// Builds the splash screen UI
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: themeProvider.getColor,
      body: Container(
        constraints: const BoxConstraints.expand(),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Image.asset(
              "assets/icons/logo_dark.png",
              width: 300,
            ),
            const SizedBox(height: 40),
            Text(
              'Let\'s make our dreams',
              style: TextStyle(
                color: themeProvider.getLightColor,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 5),
            Text(
              'come true together!',
              style: TextStyle(
                color: themeProvider.getLightColor,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 30),
            SizedBox(
              width: 200,
              child: LinearProgressIndicator(
                value: _progressAnimation.value,
                backgroundColor: themeProvider.getLightColor.withOpacity(0.3),
                valueColor:
                    AlwaysStoppedAnimation<Color>(themeProvider.getLightColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
