// ignore_for_file: must_be_immutable

import 'package:ekub/exports/screens.dart';
import 'package:ekub/routs/shared.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

import '../../../account/login_screen.dart';
import '../../../ui_kits/app_bar.dart';

class UserInfo extends StatefulWidget {
  static const routeName = "/user_info";
  UserInfoArgs args;
  UserInfo({super.key, required this.args});

  @override
  State<UserInfo> createState() => _UserInfoState();
}

class _UserInfoState extends State<UserInfo> {
  final _appBar = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: EkubAppBar(
          key: _appBar, title: "User Credentials", widgets: const []),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Center(child: Text(widget.args.message)),
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.6,
              child: ElevatedButton(
                onPressed: () {
                  if (widget.args.logout) {
                    widget.args.auth?.logOut();
                    LoginScreenArgs argument = LoginScreenArgs(isOnline: true);
                    Navigator.pushReplacement(context,
                        MaterialPageRoute(builder: (context) {
                      return kIsWeb
                          ? WelcomeScreen(args: argument)
                          : LoginScreen(args: argument);
                    }));
                  } else {
                    Navigator.pop(context);
                    Navigator.pop(context);
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(widget.args.button,
                        style: const TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
