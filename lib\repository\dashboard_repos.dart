// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ekub/models/dashboard.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/service/headers.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'ekub_localization.dart';

// Import your other necessary dependencies and models

class DashboardProvider with ChangeNotifier {
  final _baseUrl = RequestHeader.baseApp;
  final http.Client httpClient;
  final secureStorage = const FlutterSecureStorage();

  Dashboard _dashboardData = Dashboard(
      title: "Dashboard",
      daylyPaidAmount: 0,
      daylyExpected: "0",
      weeklyPaidAmount: "0",
      weeklyExpected: 0,
      monthlyPaidAmount: 0,
      monthlyExpected: 0,
      yearlyPaidAmount: 0,
      yearlyExpected: 0,
      tudayPaidMember: Members(totalMember: 0, members: []));
  List<Member> members = [];

  DashboardProvider({required this.httpClient});

  Dashboard get dashboard => _dashboardData;

  Future<void> fetchDashboard(BuildContext context) async {
    try {
      final http.Response response = await httpClient
          .get(
            Uri.parse('$_baseUrl/dashboard'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        var dashboard = Dashboard.fromJson(jsonDecode(response.body));
        _dashboardData = dashboard;
        members = dashboard.tudayPaidMember?.members ?? [];
      } else if (response.statusCode == 401) {
        gotoSignIn(context);
      }
      notifyListeners();
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw (EkubLocalization.of(context)!.translate("time_out"));
      } else {
        throw (e.toString());
      }
    }
  }
}
