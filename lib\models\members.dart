import 'dart:convert';

class Member {
  int? id;
  String? fullName;
  String? phone;
  String? email;
  String? gender;
  String? status;
  String? createdAt;
  String? updatedAt;
  String? profilePicture;
  String? city;
  String? subCity;
  String? ongoing;
  String? completed;
  String? woreda;
  String? houseNumber;
  String? location;
  String? rating;
  String? dateOfBirth;

  Member(
      {this.id,
      this.email,
      this.fullName,
      this.phone,
      this.gender,
      this.status,
      this.city,
      this.subCity,
      this.houseNumber,
      this.location,
      this.woreda,
      this.completed,
      this.ongoing,
      this.profilePicture,
      this.createdAt,
      this.rating,
      this.updatedAt,
      this.dateOfBirth});
  factory Member.fromJson(Map<String, dynamic> json) {
    return Member(
        id: json["id"],
        fullName: json["full_name"],
        phone: json["phone"],
        gender: json['gender'],
        status: json["status"],
        email: json["email"],
        rating: json["rating"] != null ? json["rating"].toString() : null,
        city: json["city"],
        woreda: json['woreda'],
        subCity: json['subcity'],
        completed: json['completed'] ?? "0",
        ongoing: json['ongoing'] ?? "0",
        houseNumber: json['house_number'],
        location: json["specific_location"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        profilePicture: json["profile_photo_path"],
        dateOfBirth: json["date_of_birth"]);
  }
}

class Members {
  List<Member>? members;
  int? totalMember;
  Members({required this.totalMember, required this.members});

  Members.fromJson(List<Member> parsedJson, this.totalMember) {
    members = parsedJson;
  }
  Members.fromStringObject(List<dynamic> parsedJson) {
    members = parsedJson.map((i) => Member.fromJson(i['member'])).toList();
  }
  String toJson() {
    String data = jsonEncode(members?.map((i) => i.toString()).toList());
    return data;
  }
}
