import 'dart:convert'; // For jsonEncode, base64Encode, utf8.encode
import 'package:ekub/exports/screens.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/utils/session.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'dart:js' as js;

// App imports
import '../service/headers.dart';
import 'js_interop.dart';

class JSAuthenticationExecutor {
  // Static flag to track if we're already handling a 405 error
  static bool isHandling405Error = false;

  // Global key for navigation
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  static Future<String> executeJavaScriptAutologin(BuildContext context) async {
    debugPrint("Proccessing autologin: Preparing JavaScript execution");

    if (kIsWeb) {
      debugPrint("Proccessing autologin: Preparing JavaScript execution");
      // For Autologin
      setupJavaScriptAutologinCallback(context);
    }

    try {
      final loginObject = jsonEncode(_createAutoLoginObject());
      // Log JavaScript execution for debugging
      debugPrint("⚠️ EXECUTING JAVASCRIPT WITH PAYMENT OBJECT:");
      debugPrint("⚠️ $loginObject");
      Session().logSession("js_execution",
          "Executing JavaScript on web with: ${jsonEncode(loginObject)}");

      // Set the payment object as a global JavaScript variable
      js.context.callMethod('eval', [
        """
        // Store payment data in a global variable for the payment processor to use
        window._authenticationObject = $loginObject;
        console.log('Payment data ready for processing');
      """
      ]);
    } catch (e) {
      // Log the error and return an error message
      debugPrint(
          "Authentication error: Failed to prepare authentication data: $e");
    }

    try {
      // Now execute the main JavaScript code that uses the global payment object
      final jsResult = await executeJavaScript("""
      (function() {
        // Initialize payment processing
        console.log('Initializing authentication processing');

        // Check if payment object is available
        if (!window._authenticationObject) {
          console.error('Authentication object not found');
          return 'Error: Authentication object not found';
        }

        let callBackCount = 0;
        // Define the callback function that will be called by the third-party payment system
        window.handleAuthDataCallback = function(response) {
          if (callBackCount != 0) {
            return;
          }
          // Execute the function only once
          callBackCount++;
          try {
            // Pass the response to Dart's handleAuthToken function
            if (typeof window.handleAuthToken === 'function') {
              window.handleAuthToken(response, "Payment message");
            } else {
              console.error('Dart handleAuthToken function is not defined');
            }
          } catch (error) {
            console.error('Processing error:', error);
            return;
          }
        };

        // Check if window.consumerapp exists
        if (window.consumerapp) {
          console.log('window.consumerapp exists');

          if (typeof window.consumerapp.evaluate === 'function') {
            console.log('window.consumerapp.evaluate is a function');
            try {
              // Use the payment object from the global variable
              const result = window.consumerapp.evaluate(JSON.stringify(window._authenticationObject));
              console.log('window.consumerapp.evaluate result:', result);
              return;
            } catch (error) {
              console.error('Error calling window.consumerapp.evaluate:', error);
              return;
            }
          } else {
            console.log('window.consumerapp.evaluate is NOT a function');
            return;
          }
        } else {
          console.log('window.consumerapp does NOT exist, creating simulation');
          alert("[Testing] consumer");

          // Create a payment service simulation for development/testing environments
          console.log('Creating authentication service simulation');

          // Define the payment service interface
          window.consumerapp = {
            evaluate: function(paymentDataStr) {
              console.log('Payment service simulation activated');

              try {
                // Parse the payment data
                const paymentData = JSON.parse(paymentDataStr);

                // Log payment details for debugging
                console.log('Processing payment with amount:',
                  paymentData.params && paymentData.params.amount ? paymentData.params.amount : 'unknown');

                // Check if we should simulate a failure (for testing error handling)
                const simulateFailure = false; // Set to true to test error handling

                // Simulate the payment processing with a delay
                // In production, this would be replaced by the actual payment gateway
                setTimeout(function() {
                  if (simulateFailure) {
                    // Simulate a failed payment
                    console.log('Simulating payment failure');
                    window.handleAuthDataCallback('TOKEN');
                  } else {
                    const testResponse = 'TOKEN';
                    // Simulate a successful payment
                    console.log('Simulating successful payment');
                    window.handleAuthDataCallback(testResponse);
                  }
                }, 2000); // 2 second delay to simulate processing time

                return true; // Indicate that the payment process has started
              } catch (error) {
                console.error('Payment simulation error:', error);

                // Report the error through the callback
                window.handleAuthDataCallback('TOKEN');

                return false; // Indicate that the payment process failed to start
              }
            }
          };

          try {
            // Start the payment process using the simulation
            console.log('Initiating simulated payment process');

            const result = window.consumerapp.evaluate(JSON.stringify(window._authenticationObject));

            if (result) {
              console.log('Payment process initiated successfully');
            } else {
              console.error('Failed to initiate authentication process');
              return;
            }
          } catch (error) {
            console.error('Error starting authentication process:', error);
            return;
          }
        }

        return 'JavaScript execution completed';
      })();
    """);
      debugPrint("⚠️ _executeJavaScript COMPLETED");

      // Log JavaScript execution result for debugging
      debugPrint("⚠️ JAVASCRIPT EXECUTION RESULT: $jsResult");
      Session().logSession("js_execution_result", jsResult);
      return jsResult;
    } catch (e) {
      // Log JavaScript execution result for debugging
      debugPrint("⚠️ JAVASCRIPT EXECUTION Error: $e");
      return "Error executing JavaScript: $e";
    }
  }

  static void setupJavaScriptAutologinCallback(
    BuildContext context,
  ) {
    js.context['handleAuthToken'] =
        (dynamic res, String message, [dynamic data]) async {
      debugPrint("Auth Response Token: $res");
      const String url =
          'https://virtualekubdash.com/api/telebirr-miniapp/callback-miniapp';
      final Map<String, dynamic> body = {'authToken': res};

      try {
        // final response = await _makeBackendCallbackRequest(
        //   url: url,
        //   body: body,
        // );

        // Success response for registered user
        final response = http.Response(
          jsonEncode({
            "user": {
              "id": 123,
              "phone_number": "1234567890",
              "name": "John Doe",
              "email": "<EMAIL>",
              "role": "member",
              "gender": "male",
              "token": "token",
              "member_id": 456,
            },
            "token": "valid_token",
          }),
          200,
        );

        // Success response for NOT registered user
        // final responseSimulation = http.Response(
        //   jsonEncode({
        //     "phone_number": "251953960596",
        //     "name": "Test",
        //     "token": "valid_token",
        //   }),
        //   200,
        // );

        // Parse the response body
        // TODO: [TEST] added simulation for testing
        // final Map<String, dynamic> responseData = jsonDecode(response.body);
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (response.statusCode == 200) {
          debugPrint('Auth Request Response successful: ${response.body}');

          // Check if the user is already registered and redirect to home screen page
          if (responseData.containsKey('user') &&
              responseData['user'] != null) {
            debugPrint("Extracted Auth User: $responseData['user']");

            final secureStorage = const FlutterSecureStorage();
            await secureStorage.write(
                key: 'id', value: responseData['user']['id'].toString());
            await secureStorage.write(
                key: 'phone_number',
                value: responseData['user']['phone_number']);
            await secureStorage.write(
                key: 'full_name', value: responseData['user']['name']);
            await secureStorage.write(
                key: 'token', value: responseData['token']);

            await secureStorage.write(
                key: "email", value: responseData['user']['email'] ?? "");
            await secureStorage.write(
                key: "role", value: responseData['user']['role'] ?? "none");

            await secureStorage.write(
                key: "gender", value: responseData["user"]['gender'] ?? "");
            await secureStorage.write(
                key: "member_id",
                value: responseData["user"]['member_id'].toString());

            // Redirect to home page
            _openHomeScreen(
                navigatorKey.currentContext!,
                responseData['user']['role'] == "admin",
                responseData['user']['role']);
          } else {
            // Handle missing authToken
            debugPrint("⚠️ user is not registered.");

            // Redirect to registration page
            if (responseData.containsKey('phone_number') &&
                responseData['phone_number'] != null) {
              final phone = responseData['phone_number'];
              final name = responseData['name'];
              Navigator.pushReplacementNamed(context, RegisterScreen.routeName,
                  arguments: AutoRegisterScreenArgs(
                      isOnline: true, phoneNumber: phone, name: name));
            }
            _showErrorDialog(
              navigatorKey.currentContext!,
              "Error",
              "Authentication failed. Missing authToken in the response.",
            );
          }
        } else {
          debugPrint('Auth Request Response failed: ${response.body}');
          _showErrorDialog(
            navigatorKey.currentContext!,
            "Error",
            "Authentication failed. Please try again later.",
          );
        }
      } catch (e) {
        debugPrint('Error making authentication response request: $e');
        _showErrorDialog(
          navigatorKey.currentContext!,
          "Error",
          "An unexpected error occurred. Please try again later.",
        );
      }
    };
  }

// ==================== HELPER METHODS ====================

  /// Shows a loading dialog to indicate payment processing
  static void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Processing payment...'),
            ],
          ),
        );
      },
    );
  }

  /// Shows an error dialog with the specified title and message
  static void _showErrorDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(message),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Shows an error dialog
  /// This method is called from the JavaScript callback
  static void showJavaScriptErrorDialog(String message) {
    debugPrint("⚠️ JAVASCRIPT CALLED showJavaScriptErrorDialog: $message");

    // Use the global navigator key to show the error dialog
    if (navigatorKey.currentContext != null) {
      _showErrorDialog(navigatorKey.currentContext!, 'Payment Failed', message);
      debugPrint("⚠️ SHOWED ERROR DIALOG");
    } else {
      debugPrint("⚠️ FAILED TO SHOW ERROR DIALOG: NO VALID CONTEXT");
    }
  }

  /// Creates the initial payment object with default values
  static Map<String, dynamic> _createAutoLoginObject() {
    return {
      'functionName': 'js_fun_h5GetAccessToken',
      'params': {
        'appid': '13509213619712010',
        'functionCallBackName': 'handleAuthDataCallback',
      },
    };
  }

  static void _openHomeScreen(BuildContext context, bool isAdmin, String role) {
    try {
      debugPrint("Opening home screen...");
      HomeScreenArgs argument = HomeScreenArgs(
        isOnline: true,
        isAdmin: isAdmin,
        role: role,
      );

      Navigator.pushNamedAndRemoveUntil(
        context,
        HomeScreen.routeName,
        (Route<dynamic> route) => false,
        arguments: argument,
      );
    } catch (e) {
      debugPrint("Error navigating to HomeScreen: $e");
    }
  }

  static Future<http.Response> _makeBackendCallbackRequest({
    required String url,
    required Map<String, dynamic> body,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    try {
      // Log the request details
      debugPrint('⬆️ Sending HTTP Request:');
      debugPrint('URL: $url');
      debugPrint('Body: ${jsonEncode(body)}');

      // Make the HTTP POST request
      final response = await http
          .post(
            Uri.parse(url),
            headers: await RequestHeader().authorisedHeader(),
            body: jsonEncode(body),
          )
          .timeout(timeout);

      // Log the response details
      debugPrint('⬇️ Received HTTP Response:');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Body: ${response.body}');

      return response;
    } catch (e) {
      // Log any errors that occur during the request
      debugPrint('❌ HTTP Request Error: $e');
      throw Exception('Failed to make backend request: $e');
    }
  }
}
