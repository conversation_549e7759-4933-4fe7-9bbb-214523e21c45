import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/admin/actions/manual_equb_list_page.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:flutter/material.dart';
import 'package:ekub/screens/dashboard/admin/actions/equb_frequency_selection.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';
import 'package:ekub/models/equb_type.dart';
import 'package:ekub/screens/dashboard/admin/member_equbs.dart';

class EqubSelectionPage extends StatelessWidget {
  final Function(String) onFrequencySelected;
  final EqubType equbType;
  final AddEqubArgs args;

  EqubSelectionPage({
    required this.onFrequencySelected,
    required this.equbType,
    required this.args,
  });

  @override
  Widget build(BuildContext context) {
    print("Equb Type: ${equbType.id}");
    final equbOptions = [
      {'label': 'Automatic Equb', 'icon': Icons.autorenew},
      {'label': 'Manual Equb', 'icon': Icons.handyman},
    ];
    final themeProvider = Provider.of<ThemeProvider>(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Equb Type'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: StaggeredGrid.count(
          crossAxisCount: 2,
          mainAxisSpacing: 10,
          crossAxisSpacing: 10,
          children: List.generate(equbOptions.length, (index) {
            final equbTypeLabel = equbOptions[index]['label'] as String;
            final icon = equbOptions[index]['icon'] as IconData;

            return StaggeredGridTile.count(
              crossAxisCellCount: 1,
              mainAxisCellCount: 1,
              child: GestureDetector(
                onTap: () {
                  if (equbTypeLabel == 'Automatic Equb') {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EqubFrequencySelection(
                          onFrequencySelected: onFrequencySelected,
                        ),
                      ),
                    );
                  } else {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ManualEqubList(
                          equbType: equbType,
                          args: args,
                        ),
                      ),
                    );
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: themeProvider.getLightColor,
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.5),
                        spreadRadius: 0.1,
                        blurRadius: 2,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        color: Colors.white,
                        size: 40,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        equbTypeLabel,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}
