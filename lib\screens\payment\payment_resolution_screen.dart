import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:ekub/service/headers.dart';

class PaymentResolutionScreen extends StatelessWidget {
  final String paymentId;
  final Function onPaymentCancelled;

  PaymentResolutionScreen(
      {required this.paymentId, required this.onPaymentCancelled});

  static Future<void> cancelPayment(BuildContext context, String paymentId,
      Function onPaymentCancelled) async {
    final url = Uri.parse(
        'https://test.virtualekubdash.com/api/cancel/cbegateway/$paymentId');
    try {
      final headers = await RequestHeader().authorisedHeader();
      final response = await http.delete(
        url,
        headers: headers,
      );

      if (response.statusCode == 200) {
        // Handle success
        print('Payment cancellation successful');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Deleted Successfully')),
          );
        }
        onPaymentCancelled();
      } else {
        // Handle failure
        print('Failed to cancel payment');
      }
    } catch (e) {
      // Handle error
      print('Error: $e');
    }
  }

  static Future<String?> retryPayment(String paymentId) async {
    print('Starting retryPayment method');
    final url = Uri.parse(
        'https://test.virtualekubdash.com/api/retry/cbegateway/$paymentId');
    try {
      final headers = await RequestHeader().authorisedHeader();
      final response = await http.post(
        url,
        headers: headers,
      );

      print('HTTP response status: ${response.statusCode}');
      if (response.statusCode == 200) {
        // Parse the JSON response
        final Map<String, dynamic> responseData = json.decode(response.body);
        final String retryUrl = responseData['url'];
        print('Retry URL: $retryUrl');
        return retryUrl;
      } else {
        // Handle failure
        print('Failed to retrieve retry URL');
        return null;
      }
    } catch (e) {
      // Handle error
      print('Error: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    throw UnimplementedError();
  }
}
