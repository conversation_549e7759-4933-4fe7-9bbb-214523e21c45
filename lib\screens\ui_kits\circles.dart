import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../themes/ThemeProvider.dart';

class Circles {
  BuildContext context;
  int theme;
  Circles(this.context, this.theme);

  double getSmallDiameter(BuildContext context) =>
      MediaQuery.of(context).size.width * 2 / 3;

  double getBigDiameter(BuildContext context) =>
      MediaQuery.of(context).size.width * 7 / 8;

  Positioned topLeft(String message) {
    var themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    return Positioned(
      left: -getBigDiameter(context) / 6,
      top: -getBigDiameter(context) / 6,
      child: Container(
        width: getBigDiameter(context),
        height: getBigDiameter(context),
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
                colors: [themeProvider.getColor, themeProvider.getLightColor],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter)),
        child: Center(
          child: Text(
            message,
            style: const TextStyle(
                fontFamily: "Pacifico", fontSize: 40, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Positioned topRight() {
    var themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    return Positioned(
      right: -getSmallDiameter(context) / 3,
      top: -getSmallDiameter(context) / 3,
      child: Container(
        width: getSmallDiameter(context),
        height: getSmallDiameter(context),
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
                colors: [themeProvider.getColor, themeProvider.getLightColor],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter)),
      ),
    );
  }

  Positioned bottomRight() {
    var themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    return Positioned(
      right: -getBigDiameter(context) / 2,
      bottom: -getBigDiameter(context) / 2,
      child: Container(
        width: getBigDiameter(context),
        height: getBigDiameter(context),
        decoration: BoxDecoration(
            shape: BoxShape.circle, color: themeProvider.getLightColor),
      ),
    );
  }

  Stack circleProvider(String text) {
    return Stack(
      children: <Widget>[
        Circles(context, theme).topRight(),
        Circles(context, theme).topLeft("Equb"),
        Circles(context, theme).bottomRight(),
      ],
    );
  }
}
