// ignore_for_file: must_be_immutable

import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../repository/ekub_localization.dart';
import '../../repository/user_repos.dart';
import 'otp_screen.dart';

class PhoneVerification extends StatefulWidget {
  static const routeName = "/phoneVerification";

  PhoneVerification({super.key, required this.status});
  String status;
  @override
  _PhoneVerificationState createState() => _PhoneVerificationState();
}

class _PhoneVerificationState extends State<PhoneVerification> {
  int userId = 0;
  int resendToken = 0;
  bool isOffline = false;
  bool isSubmitted = false;
  bool otpVisibility = false;
  late bool _onProcess;
  String verificationID = "";
  // User? user;
  late ThemeProvider themeProvider;
  final _loginFormKey = GlobalKey<FormState>();
  TextEditingController phoneController = TextEditingController();
  TextEditingController otpController = TextEditingController();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _onProcess = false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var language = EkubLocalization.of(context)!;
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.black),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _loginFormKey,
          autovalidateMode: isSubmitted
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.disabled,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
            ),
            height: MediaQuery.of(context).size.height * 0.85,
            margin: const EdgeInsets.fromLTRB(20, 0, 20, 0),
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      // height: 140,
                      // color: Colors.red,
                      margin: const EdgeInsets.only(
                        top: 20,
                      ),
                      child: const Image(
                        image: AssetImage(
                          'assets/icons/icon.png',
                        ),
                        height: 100,
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    Text(
                      language.translate("verify_phone_number"),
                      style: const TextStyle(
                        fontSize: 25,
                        fontWeight: boldFont,
                      ),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    Text(
                      language.translate("verify_phone_message"),
                      style: TextStyle(
                        fontSize: fontSmall,
                        fontWeight: normalFontWeight,
                        color: bodyTextColor,
                      ),
                      textAlign: TextAlign.start,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 0.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              language.translate("phone_number"),
                              style: TextStyle(
                                  color: themeProvider.getColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: fontMedium),
                            ),
                          ),
                          const SizedBox(
                            height: 3,
                          ),
                          TextFormField(
                            maxLength: 9,
                            controller: phoneController,
                            keyboardType: TextInputType.phone,
                            style: lableStyle,
                            decoration: InputDecoration(
                                border: const OutlineInputBorder(
                                    borderSide:
                                        BorderSide(style: BorderStyle.solid)),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(18),
                                  borderSide: BorderSide(
                                      color: themeProvider.getColor
                                          .withOpacity(0.3)),
                                ),
                                errorBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.red,
                                  ),
                                  borderRadius: BorderRadius.circular(18),
                                ),
                                focusedErrorBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(18),
                                  borderSide: const BorderSide(
                                      color: Colors.red, width: 2),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(18),
                                  borderSide:
                                      BorderSide(color: themeProvider.getColor),
                                ),
                                fillColor: Colors.white,
                                filled: true,
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.never,
                                prefixIcon: Container(
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 5),
                                  height: 25,
                                  width: 30,
                                  decoration: BoxDecoration(
                                      color: themeProvider.getColor
                                          .withOpacity(0.1),
                                      shape: BoxShape.circle),
                                  child: Icon(
                                    Icons.phone_outlined,
                                    size: 20,
                                    color: themeProvider.getColor,
                                  ),
                                ),
                                prefix: const Text(
                                  "+251 ",
                                ),
                                labelText: '+2519-99-89-89-21',
                                counterText: "",
                                labelStyle: hintStyle),
                            validator: (value) {
                              if (value!.isEmpty) {
                                return language.translate("phone_empty_member");
                              } else if (value.length < 9) {
                                return language.translate("phone_length");
                              }
                              return null;
                            },
                            onSaved: (value) {},
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Column(
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        setState(() {
                          isSubmitted = true;
                        });

                        final form = _loginFormKey.currentState;
                        if (form!.validate()) {
                          setState(() {
                            _onProcess = true;
                          });
                          form.save();
                          if (!await InternetConnectivity()
                              .checkInternetConnectivty(context, true)) {
                            setState(() {
                              _onProcess = false;
                            });
                            return;
                          } else {
                            _checkPhoneNumber(int.parse(phoneController.text));
                          }
                        }
                      },
                      child: SizedBox(
                        height: 50,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Spacer(),
                            Text(language.translate("send_code"),
                                style: buttonText),
                            const Spacer(),
                            Align(
                              widthFactor: 2,
                              alignment: Alignment.centerRight,
                              child: _onProcess
                                  ? const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                        ),
                                      ),
                                    )
                                  : Container(),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          "${language.translate("have_account")}? ",
                          style: TextStyle(
                              fontSize: fontMedium,
                              color: bodyTextColor,
                              fontWeight: normalFontWeight),
                        ),
                        InkWell(
                          borderRadius: BorderRadius.circular(20),
                          splashColor: Colors.amber,
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Center(
                            child: Text(
                              language.translate("sign_in"),
                              style: TextStyle(
                                  color: themeProvider.getLightColor,
                                  fontWeight: boldFont),
                            ),
                          ),
                        ),
                      ],
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void sendCode(BuildContext context, String phone) {
    var sender = AuthDataProvider(httpClient: http.Client());

    var res = sender.sendOtp(context, phone);
    res
        .then((value) => {
              setState(() {
                _onProcess = false;
              }),
              if (value["acknowledge"] == "success")
                {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => OtpScreen(
                              phoneNumber: phoneController.text,
                              status: widget.status,
                              userId: userId)))
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value["message"][0],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                  }),
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          sendCode(context, phone);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        _onProcess = false;
      });
      return {};
    });
  }

  void _checkPhoneNumber(int phoneNumber) {
    var sender = AuthDataProvider(httpClient: http.Client());

    var res = sender.checkPhoneNumber("+251$phoneNumber", context);
    res
        .then((value) => {
              print("Sent number($phoneNumber): $value"),
              if (value["code"] == 200)
                {
                  if (value["data"] == false)
                    {
                      if (widget.status == "forgotPassword")
                        {
                          PanaraInfoDialog.show(
                            context,
                            message: EkubLocalization.of(context)!
                                .translate("unknown_phone"),
                            buttonText:
                                EkubLocalization.of(context)!.translate("okay"),
                            onTapDismiss: () {
                              Navigator.pop(context);
                            },
                            imagePath: errorDialogIcon,
                            panaraDialogType: PanaraDialogType.error,
                          ),
                          setState(() {
                            _onProcess = false;
                          }),
                        }
                      else
                        {
                          sendCode(context, phoneNumber.toString()),
                        }
                    }
                  else if (value["data"] == true)
                    {
                      setState(() {
                        // _onProcess = false;
                        userId = value["userId"];
                      }),
                      if (widget.status == "forgotPassword")
                        {
                          sendCode(context, phoneNumber.toString()),
                        }
                      else
                        {
                          PanaraInfoDialog.show(
                            context,
                            message: EkubLocalization.of(context)!
                                .translate("registered_phone"),
                            buttonText:
                                EkubLocalization.of(context)!.translate("okay"),
                            onTapDismiss: () {
                              Navigator.pop(context);
                            },
                            imagePath: errorDialogIcon,
                            panaraDialogType: PanaraDialogType.error,
                          ),
                          setState(() {
                            _onProcess = false;
                          })
                        }
                    }
                }
              else
                {
                  setState(() {
                    _onProcess = false;
                  }),
                  PanaraInfoDialog.show(
                    context,
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    _onProcess = false;
                  }),
                }
            })
        .onError((error, stackTrace) {
      if (mounted) {
        PanaraConfirmDialog.show(context,
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          _checkPhoneNumber(phoneNumber);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
        setState(() {
          _onProcess = false;
        });
      }
      return {};
    });
  }
}
