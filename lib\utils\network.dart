// ignore_for_file: unused_field, unused_element

import 'dart:io'; //InternetAddress utility
import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart'; //For StreamController/Stream
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:http/http.dart' as http;

class NetworkManager {
  //This creates the single instance by calling the `_internal` constructor specified below
  static final NetworkManager _singleton = NetworkManager._internal();
  NetworkManager._internal();

  //This is what's used to retrieve the instance through the app
  static NetworkManager getInstance() => _singleton;

  //This tracks the current connection status
  bool hasConnection = false;

  //This is how we'll allow subscribing to connection changes
  StreamController<bool> connectionChangeController =
      StreamController.broadcast();

  //flutter_connectivity
  final Connectivity _connectivity = Connectivity();

  //Hook into flutter_connectivity's Stream to listen for changes
  //And check the connection status out of the gate
  void initialize() {
    // _connectivity.onConnectivityChanged.listen(_connectionChange);
    checkConnection();
  }

  Stream<bool> get connectionChange => connectionChangeController.stream;

  //A clean up method to close our StreamController
  //   Because this is meant to exist through the entire application life cycle this isn't
  //   really an issue
  void dispose() {
    connectionChangeController.close();
  }

  //flutter_connectivity's listener
  void _connectionChange(ConnectivityResult result) {
    checkConnection();
  }

  //The test to actually see if there is a connection
  Future<bool> checkConnection() async {
    bool previousConnection = hasConnection;

    if (kIsWeb) {
      // Web-compatible check (using HTTP request)
      print("[WEB] Checking internet connection...");
      try {
        final stopwatch = Stopwatch()..start();
        final response = await http
            .get(
              Uri.parse('https://httpbin.org/get'),
            )
            .timeout(const Duration(seconds: 5)); // Timeout after 5 seconds
        print("[WEB] Response received in ${stopwatch.elapsedMilliseconds}ms");
        print("[WEB] Status code: ${response.statusCode}");
        hasConnection = response.statusCode == 200;
      } catch (e) {
        print('[WEB] Connection check failed: $e');
        hasConnection = false;
      }
    } else {
      // Mobile/Desktop check (using dart:io)
      print("[NATIVE] Checking internet connection...");
      try {
        final stopwatch = Stopwatch()..start();
        final result = await InternetAddress.lookup('google.com');
        hasConnection = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
        print(
            "[NATIVE] Response received in ${stopwatch.elapsedMilliseconds}ms");
        print("[NATIVE] DNS lookup completed in ${hasConnection}ms");
      } on SocketException catch (e) {
        print('[NATIVE] Connection check failed: $e');
        hasConnection = false;
      }
    }

    print(
        "Internet connection: ${hasConnection ? 'AVAILABLE' : 'NOT AVAILABLE'}");
    //The connection status changed send out an update to all listeners
    if (previousConnection != hasConnection) {
      print(
          "Connection state changed to: ${hasConnection ? 'ONLINE' : 'OFFLINE'}");
      connectionChangeController.add(hasConnection);
    }

    return hasConnection;
  }
}
