name: ekub
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+17

environment:
  sdk: ">=2.17.0 <3.0.0"
  # sdk: ">=3.0.5 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  animated_custom_dropdown: ^3.0.0
  animated_snack_bar: ^0.4.0
  cached_network_image: ^3.2.0
  connectivity_plus: ^6.0.5
  cupertino_icons: ^1.0.2
  dio: ^5.3.3
  email_validator: null
  equatable: ^2.0.3
  fl_chart: ^0.68.0
  floor: ^1.2.0
  flutter_bloc: ^8.0.1
  flutter_countdown_timer: ^4.1.0
  flutter_fortune_wheel: ^1.3.0
  flutter_image_compress: ^2.2.0
  flutter_inappwebview: ^6.0.0
  flutter_keyboard_visibility: ^6.0.0
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^4.0.0
  flutter_localizations:
    sdk: flutter
  camera: ^0.10.0+1
  flutter_rating_bar: ^4.0.1
  flutter_secure_storage: ^8.1.0
  flutter_slidable: ^3.0.0
  flutter_svg: ^2.0.0
  flutter_telebirr: ^0.0.4
  flutter_widget_from_html: ^0.15.1
  font_awesome_flutter: ^10.1.0
  google_fonts: ^4.0.4
  http: ^1.0.0
  image_picker: ^1.0.4
  in_app_update: ^4.2.2
  internet_connection_checker: ^1.0.0+1
  intl: any
  jailbreak_root_detection: ^1.0.0
  loading_indicator: ^3.1.1
  localization: null
  ntp: ^2.0.0
  # package_info_plus: ^8.0.0
  panara_dialogs: ^0.1.4
  path_provider: ^2.1.3
  percent_indicator: ^4.0.1
  provider: null

  roulette: ^0.1.4
  shared_preferences: null
  shimmer: ^3.0.0
  sleek_circular_slider: ^2.0.1
  # upgrader: ^6.5.0
  upgrader: ^10.3.0
  url_launcher: ^6.3.1
  # wakelock_windows: ^0.2.1
  webview_flutter: ^4.9.0
  win32: any
  firebase_core: ^3.7.0
  firebase_messaging: ^15.1.4
  firebase_in_app_messaging: ^0.8.0+9
  flutter_staggered_grid_view: ^0.7.0
  cloud_firestore: ^5.4.5
  web_socket_channel: ^3.0.2
dev_dependencies:
  build_runner: ^2.1.2
  floor_generator: ^1.2.0
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following home: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/icons/icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  # web:
  #   generate: true
  #   image_path: "assets/icons/logo.png"
  #   background_color: "#hexcode"
  #   theme_color: "#hexcode"
  # windows:
  #   generate: true
  #   image_path: "assets/icons/logo.png"
  #   icon_size: 48 # min:48, max:256, default: 48

flutter:
  generate: true # Add this line

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/translations/
    - assets/icons/
    - assets/icons/paymentIcons/awash-icon.png
    - assets/icons/paymentIcons/cbebirr-icon.png
    - assets/icons/paymentIcons/telebirr-icon.png
    - assets/icons/Mask_group.png
    - assets/icons/Money_hand.png
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
