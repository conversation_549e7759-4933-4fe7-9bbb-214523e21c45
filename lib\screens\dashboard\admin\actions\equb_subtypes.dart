// ignore_for_file: empty_catches, use_build_context_synchronously

import 'dart:async';
import 'dart:typed_data';

import 'package:ekub/screens/dashboard/admin/actions/equb_list_page.dart';
import 'package:ekub/screens/ui_kits/app_bar.dart';
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../../../models/equb_type.dart';
import '../../../../repository/ekub_localization.dart';
import '../../../../repository/equb_repos.dart';
import '../../../../routs/shared.dart';
import '../../../../utils/tools.dart';
import '../../../themes/ThemeProvider.dart';
import 'add_ekub.dart';
import 'equb_frequency_selection.dart';

class EqubSubtypeScreen extends StatefulWidget {
  final AddEqubArgs args;
  const EqubSubtypeScreen({super.key, required this.args});

  @override
  State<EqubSubtypeScreen> createState() => _EqubSubtypeScreenState();
}

class _EqubSubtypeScreenState extends State<EqubSubtypeScreen> {
  List<EqubType> _items = [];
  bool loading = true;
  EqubType? slectedEqubType;
  int selectedIndex = 0;
  List<String> equbTypes = [];
  late ThemeProvider themeProvider;
  Uint8List? image;
  List<Uint8List?> images = [];
  final _appBar = GlobalKey<FormState>();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    loadEqubType();
    super.initState();
  }

  void loadEqubType() async {
    int index = 0;
    setState(() {
      loading = true;
    });
    try {
      equbTypes = [];
      _items = await Provider.of<EqubDataProvider>(context, listen: false)
          .loadEqubTypes(context, 0, 1);
      setState(() {
        images.length = _items.length;
        slectedEqubType = _items.isEmpty ? null : _items[0];
        for (var element in _items) {
          if (element.status == "Active") {
            equbTypes.add(element.name.toString());
            getImage(element.id.toString(), index);
          }
          index += 1;
        }
        loading = false;
      });
    } catch (e) {
      if (e is TimeoutException) {
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("time_out"),
            message: EkubLocalization.of(context)!.translate("timeout_message"),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);

          loadEqubType();
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
      } else {
        PanaraConfirmDialog.show(context,
            message: e.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          if (mounted) {
            loadEqubType();

            Navigator.pop(context);
          }
        }, onTapCancel: () {
          if (mounted) {
            Navigator.pop(context);
          }
        }, panaraDialogType: PanaraDialogType.error);
      }
      setState(() {
        loading = false;
      });
    }
  }

  getImage(String id, index) async {
    try {
      image = await Provider.of<EqubDataProvider>(context, listen: false)
          .getEqubtypePicture(context, id);
      setState(() {
        images[index] = image;
      });
      // print(images);s
      // return image;
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: EkubAppBar(
        key: _appBar,
        title: EkubLocalization.of(context)!.translate("select_ekub_type"),
        widgets: const [],
      ),
      body: loading
          ? searchLoading()
          : Container(
              margin: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 10),
                  Expanded(
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 10,
                        mainAxisSpacing: 10,
                        //childAspectRatio: 0.8,
                      ),
                      itemCount: _items.length,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedIndex = index;
                              slectedEqubType = _items[index];
                            });

                            // Print the equbType frequency and args
                            print('selected equb typex: ${slectedEqubType!}');
                            print('Args: ${widget.args}');

                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => EqubFrequencySelection(
                                  onFrequencySelected: (frequency) {
                                    // Navigate to the next page with the selected frequency
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => EqubListPage(
                                          equbType: slectedEqubType!,
                                          frequency: frequency,
                                          args: widget.args,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          },
                          child: roleContainer(
                            context,
                            _items[index],
                            selectedIndex == index,
                            themeProvider,
                            images[index] != null && images[index]!.isNotEmpty
                                ? images[index]
                                : null,
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
    );
  }

  Future<dynamic> showEqubInfo(BuildContext context) {
    return PanaraInfoDialog.show(
      context,
      title: slectedEqubType!.name!,
      message: slectedEqubType!.type == "Automatic"
          ? "${EkubLocalization.of(context)!.translate("automatic_equb_msg1")}. ${EkubLocalization.of(context)!.translate("starts")} ${formatDate(slectedEqubType!.startDate!)} ${EkubLocalization.of(context)!.translate("and")} ${EkubLocalization.of(context)!.translate("ends")} ${formatDate(slectedEqubType!.endDate!)}. ${EkubLocalization.of(context)!.translate("automatic_equb_msg2")}."
          : EkubLocalization.of(context)!.translate("manual_equb_msg"),
      buttonText: EkubLocalization.of(context)!.translate("okay"),
      onTapDismiss: () {
        Navigator.pop(context);
      },
      panaraDialogType: PanaraDialogType.normal,
    );
  }

  roleContainer(
      BuildContext context, EqubType type, bool isSelected, theme, equbImage) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: .1,
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
        color: Colors.white,
        borderRadius: const BorderRadius.all(Radius.circular(15)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.center,
            width: 55,
            height: 55,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: themeProvider.getLightColor,
            ),
            child: Text(
              type.name!.substring(0, 1),
              style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.w900,
                fontSize: fontBig,
              ),
            ),
          ),
          const SizedBox(height: 10),
          Text(
            type.name!,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: fontMedium,
            ),
          ),
          const SizedBox(height: 5),
          // Text(
          //   '${EkubLocalization.of(context)!.translate("round")}: ${type.round}',
          //   style: TextStyle(
          //     color: Colors.grey[600],
          //     fontSize: fontSmall,
          //   ),
          // ),
          // Text(
          //   '${EkubLocalization.of(context)!.translate("type")}: ${type.type}',
          //   style: TextStyle(
          //     color: Colors.grey[600],
          //     fontSize: fontSmall,
          //   ),
          // ),
        ],
      ),
    );
  }
}
