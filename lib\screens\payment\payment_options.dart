import 'package:ekub/models/members.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/account/privacy_screen.dart';
import 'package:ekub/screens/dashboard/admin/member_equbs.dart';
import 'package:ekub/screens/dashboard/root/root_screen.dart';
import 'package:ekub/screens/payment/payment_success.dart';

import 'package:flutter/material.dart';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'dart:core';
import 'package:ekub/repository/payment_repos.dart';

import 'package:http/http.dart' as http;

import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:flutter/services.dart';

class PaymentOptionsScreen extends StatelessWidget {
  final Member member;
  final String equbId;
  final String amount;

  const PaymentOptionsScreen({
    Key? key,
    required this.member,
    required this.equbId,
    required this.amount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    print('Member ID: \\${member.id}');
    print('Equb ID: \\${equbId}');
    print('Amount: \\${amount}');
    return Scaffold(
      appBar: AppBar(
        title: Text(EkubLocalization.of(context)!.translate("payment_options")),
      ),
      body: PaymentMethods(
        amount: amount,
        equbId: equbId,
        member: member,
      ),
    );
  }
}

class PaymentMethods extends StatefulWidget {
  final String amount;
  final String equbId;
  final Member member;

  PaymentMethods({
    required this.amount,
    required this.equbId,
    required this.member,
  });

  @override
  _PaymentMethodsState createState() => _PaymentMethodsState();
}

class _PaymentMethodsState extends State<PaymentMethods> {
  final PaymentRepository _paymentRepository = PaymentRepository();
  bool _isLoading = false;
  bool _onProcess = false;

  static const MethodChannel _platform = MethodChannel('telebirr');

  @override
  void initState() {
    super.initState();
    initialize(context);
  }

  /// Initializes the MethodChannel and listens for navigation commands.
  void initialize(BuildContext context) {
    _platform.setMethodCallHandler((call) async {
      if (call.method == 'navigateTo') {
        String page = call.arguments; // Get the target page
        _navigateToPage(context, page);
      }
    });
  }

  /// Handles navigation to the specified page.
  void _navigateToPage(BuildContext context, String page) {
    if (page == "success") {
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => PaymentSuccessPage(member: widget.member)));
    } else {
      _showSnackBar(context, 'Payment failed. Please try again.');
    }
  }

  void _onPaymentSuccess() {
    print('Payment successful!');
  }

  Future<void> _payWithCBEBirr(BuildContext context) async {
    setState(() {
      _isLoading = true;
    });

    final responseData = await _paymentRepository.payWithCBEBirr(
      amount: widget.amount,
      equbId: widget.equbId,
      memberId: widget.member.id.toString(),
    );

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }

    if (responseData != null && responseData['url'] != null) {
      final paymentUrl = responseData['url'];

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PaymentWebView(url: paymentUrl),
          ),
        );
      }
    } else {
      _showSnackBar(context, 'Failed to process payment. Please try again.');
    }
  }

  void _payWithTelebirr(
      String memberId, String equbId, String amount, String credit) {
    setState(() {
      _onProcess = true;
    });

    // Log payment attempt for debugging
    debugPrint(
        "Attempting telebirr payment: memberId=$memberId, equbId=$equbId, amount=$amount");

    var sender = MemberProvider(httpClient: http.Client());
    var res = sender.payWithTelebirr(context, memberId, amount, equbId);
    res.then((value) async {
      setState(() {
        _onProcess = false;
      });

      // Check if value is a Map and handle the response properly
      if (mounted && value is Map<String, dynamic>) {
        debugPrint("Telebirr payment response: $value");

        if (value['success'] == true) {
          // Payment initiated successfully
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content:
                    Text(value['message'] ?? 'Payment initiated successfully')),
          );
        } else {
          // Payment failed - show a more user-friendly message
          String errorMessage = value['message'] ?? 'Something went wrong...';

          // Make the error message more user-friendly
          if (errorMessage.contains("CURL error 28")) {
            errorMessage =
                "Connection timeout. Please check your internet connection and try again.";
          } else if (errorMessage.contains("500")) {
            errorMessage = "Server error. Please try again later.";
          }

          PanaraConfirmDialog.show(context,
              message: errorMessage,
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            Navigator.pop(context);
            _payWithTelebirr(memberId, equbId, amount, credit);
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }).onError((error, stackTrace) {
      debugPrint("Telebirr payment error: $error");

      if (mounted) {
        // Create a more user-friendly error message
        String errorMessage =
            "Payment service is currently unavailable. Please try again later.";

        PanaraConfirmDialog.show(context,
            message: errorMessage,
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          _payWithTelebirr(memberId, equbId, amount, credit);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);

        setState(() {
          _onProcess = false;
        });
      }
    });
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ListView(
          padding: const EdgeInsets.all(16),
          children: [
            paymentMethod(
              title: 'Pay with telebirr',
              imageUrl: 'assets/icons/paymentIcons/telebirr-icon.png',
              onPressed: () {
                _payWithTelebirr(widget.member.id.toString(), widget.equbId,
                    widget.amount, '');
              },
              iconSize: const Size(120, 70),
            ),
            paymentMethod(
              title: 'Pay with CBE Birr',
              imageUrl: 'assets/icons/paymentIcons/cbebirr-icon.png',
              onPressed: () {
                // Using logger instead of print
                debugPrint('Amount to pay: ${widget.amount}');
                _payWithCBEBirr(context);
              },
              iconSize: const Size(90, 90),
            ),
          ],
        ),
        if (_isLoading || _onProcess)
          const Center(
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget paymentMethod({
    required String title,
    required String imageUrl,
    required VoidCallback onPressed,
    required Size iconSize,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue, width: 1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: onPressed,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Image.asset(
                  imageUrl,
                  width: iconSize.width,
                  height: iconSize.height,
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class PaymentWebView extends StatelessWidget {
  final String url;

  const PaymentWebView({super.key, required this.url});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop) {
          // Navigate back to HomeScreen with required arguments
          Navigator.of(context).pushReplacementNamed(
            HomeScreen.routeName,
            arguments: HomeScreenArgs(
              isAdmin: false, // or false, depending on your logic
              role: 'member',
              isOnline: true, // replace with actual role
            ),
          );
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('CBE Birr Payment'),
        ),
        body: Transform.translate(
          offset: const Offset(0, -56.0),
          child: InAppWebView(
            initialUrlRequest: URLRequest(url: WebUri(url)),
            onWebViewCreated: (controller) {
              // WebView created
            },
            onLoadStop: (controller, url) async {
              // Page load completed
            },
          ),
        ),
      ),
    );
  }
}
