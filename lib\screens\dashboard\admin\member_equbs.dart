// ignore_for_file: no_logic_in_create_state, must_be_immutable, must_call_super, empty_catches, use_build_context_synchronously

import 'dart:async';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:ekub/exports/screens.dart';
import 'package:ekub/models/members.dart';
import 'package:ekub/models/user.dart';
import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/repository/language.dart';
import 'package:ekub/repository/main_equbs_list.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/comming_soon.dart';
import 'package:ekub/screens/dashboard/admin/actions/add_ekub.dart';
import 'package:ekub/screens/dashboard/admin/actions/completed_ekubs.dart';
import 'package:ekub/screens/dashboard/admin/actions/equb_frequency_selection.dart';
import 'package:ekub/screens/dashboard/admin/actions/equb_list_page.dart';
import 'package:ekub/screens/dashboard/admin/actions/equb_subtypes.dart';
import 'package:ekub/screens/dashboard/admin/actions/list_of_equb.dart';
import 'package:ekub/screens/dashboard/admin/actions/manual_equb_list_page.dart';
import 'package:ekub/screens/dashboard/admin/actions/passed_equbs.dart';
import 'package:ekub/screens/dashboard/admin/equb_selection_page.dart';
import 'package:ekub/screens/dashboard/admin/members.dart';
import 'package:ekub/screens/dashboard/ekubs/equb_provider.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/screens/ui_kits/bottom_sheet.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/device.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../models/ekub.dart';
import '../../../models/equb_type.dart';
import '../../../repository/equb_repos.dart';
import '../../../utils/tools.dart';
import '../../settings/profile.dart';
import '../../themes/ThemeProvider.dart';
import '../../ui_kits/app_bar.dart';
import '../../ui_kits/list_view.dart';
import '../../ui_kits/loading_indicator.dart';
import '../../ui_kits/no_internet_connection_found.dart';
import 'tabs/member_tabs.dart';

class MemberEqubs extends StatefulWidget {
  static const routeName = '/member_equbs';
  MemberEqubsArgs args;
  MemberEqubs({super.key, required this.args});

  @override
  State<MemberEqubs> createState() => _MemberEqubsState(args);
}

class _MemberEqubsState extends State<MemberEqubs>
    with AutomaticKeepAliveClientMixin<MemberEqubs> {
  @override
  bool get wantKeepAlive => true;
  MemberEqubsArgs args;
  _MemberEqubsState(this.args);
  bool isConnected = true;
  bool proLoaded = false;
  bool isActive = true;
  bool isLoading = true;
  bool loading = true;
  int? totalEqubs = 0;
  int selectedTab = 0;
  Uint8List? image;
  Member? member;
  List<EqubType> _items = [];
  EqubType? selctedType;
  int selectedIndex = 0;
  List<String> equbTypes = [];
  Uint8List? equbImage;
  List<Uint8List?> images = [];
  List<String> languages = ["En", "አማ", "Or", "ትግ", "So"];
  String selectedLanguage = "Language";
  String language = "";
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      gender: "loading",
      email: "loading",
      role: "loading",
      memberId: "loading");
  List<Equb>? items = [];
  List<Equb>? completedEqubs = [];

  var myMenuItems = <String>[];
  final _appBar = GlobalKey<FormState>();
  late ThemeProvider themeProvider;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  late AppLanguage appLanguage;
  final dropdownControl = TextEditingController();

  EqubType? selectedEqubType;

  @override
  void initState() {
    super.initState();

    _initializeData();
//    _executeListOfEqubLogic();
  }

  void _executeListOfEqubLogic() {
    // Create an instance of ListOfEqub
    ListOfEqub listOfEqub = ListOfEqub(
      title: 'Equb Details',
      description: 'Initial Equb Details',
      equbType: EqubType(), // Provide a default or initial EqubType
      frequency: 'Daily',
      args: AddEqubArgs(
        isOnline: true,
        member: args.member,
        isAdmin: true,
        user: user,
      ),
    );

    // Access the state of ListOfEqub and call the method
    final listOfEqubState = listOfEqub.createState();
    listOfEqubState.initState();
  }

  Future<void> _initializeData() async {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    appLanguage = Provider.of<AppLanguage>(context, listen: false);
    _fetchLocale();
    await _loadProfile();
    if (_items.isNotEmpty) {
      final defaultItem =
          _items.firstWhere((item) => item.id == 1, orElse: () => _items[0]);
      selectedIndex = _items.indexOf(defaultItem);
      selctedType = defaultItem;
      selectedEqubType = defaultItem;
    }
  }

  Future<void> _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());
    try {
      user = await auth.getUserData();
      if (user.memberId != null) {
        try {
          image = await Provider.of<MemberProvider>(context, listen: false)
              .getProfilePicture(context, user.memberId!);
        } catch (e) {
          // If image loading fails, load default image
          final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
          image = bytes.buffer.asUint8List();
        }
      } else {
        // If no member ID, load default image
        final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
        image = bytes.buffer.asUint8List();
      }
      setState(() {
        proLoaded = true;
      });
      // print("Loaded member information: ${user.toString()}");
      // print("Image loaded: ${image != null}");
      await loadEqubs();
    } catch (e) {
      // Handle error
      //    print("Error loading profile: $e");
      // Load default image on error
      final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
      image = bytes.buffer.asUint8List();
    }
  }

  Future<void> loadEqubs() async {
    if (!await InternetConnectivity()
        .checkInternetConnectivty(context, false)) {
      setState(() {
        isConnected = false;
        isLoading = false;
      });
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      member = await Provider.of<MemberProvider>(context, listen: false)
          .getMember(context, args.member.id.toString());
      if (member != null) {
        await loadEqubType();
        await Provider.of<EqubDataProvider>(context, listen: false)
            .loadMemberEqub(context, 0, 1, args.member.id!);
        await Provider.of<MemberProvider>(context, listen: false)
            .loadCompletedMemberEqub(context, args.member.id!.toString());
      }
      setState(() {
        isLoading = false;
        isConnected = true;
      });
    } catch (e) {
      // Handle error
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> loadEqubType() async {
    setState(() {
      loading = true;
    });
    try {
      _items = await Provider.of<EqubDataProvider>(context, listen: false)
          .loadMainEqubTypes(context, 0, 1);
      equbTypes = _items
          .where((item) => item.status == "Active")
          .map((item) => item.name.toString())
          .toList();
      setState(() {
        loading = false;
      });
    } catch (e) {
      // Handle error
      setState(() {
        loading = false;
      });
    }
  }

  _fetchLocale() async {
    await appLanguage.fetchLocale();
    language = appLanguage.appLocal.languageCode.toLowerCase();

    if (language == "fr") {
      setState(() {
        selectedLanguage = "Or";
      });
    }
    if (language == "es") {
      setState(() {
        selectedLanguage = "ትግ";
      });
    }
    if (language == "am") {
      setState(() {
        selectedLanguage = "አማ";
      });
    } else if (language == "tl") {
      setState(() {
        selectedLanguage = "So";
      });
    } else if (language == "en") {
      setState(() {
        selectedLanguage = "En";
      });
    }
  }

  _deleteMember(String id) {
    setState(() {
      isLoading = true;
    });
    var sender = MemberProvider(httpClient: http.Client());
    var res = sender.deleteMember(context, id);
    res
        .then((value) => {
              if (value["code"] == 200)
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                    imagePath: successDialogIcon,
                    panaraDialogType: PanaraDialogType.success,
                  ),
                  setState(() {
                    isLoading = false;
                  })
                }
              else
                {
                  PanaraInfoDialog.show(
                    context,
                    message: value["message"],
                    buttonText: EkubLocalization.of(context)!.translate("okay"),
                    onTapDismiss: () {
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                    imagePath: errorDialogIcon,
                    panaraDialogType: PanaraDialogType.error,
                  ),
                  setState(() {
                    isLoading = false;
                  })
                }
            })
        .onError((error, stackTrace) {
      PanaraConfirmDialog.show(context,
          // title: "Request Timeout",
          message: error.toString(),
          confirmButtonText:
              EkubLocalization.of(context)!.translate("try_again"),
          cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
          onTapConfirm: () {
        Navigator.pop(context);
        _deleteMember(id);
      }, onTapCancel: () {
        Navigator.pop(context);
      }, panaraDialogType: PanaraDialogType.error);
      return {};
    });
  }

  _addEqubPage() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => EqubSubtypeScreen(
                  args: AddEqubArgs(
                      isOnline: true,
                      member: args.member,
                      isAdmin: true,
                      user: user),
                )));
    // Navigator.pushNamed(context, AddEqub.routeName,
    //     arguments: AddEqubArgs(
    //         isOnline: true, member: args.member, isAdmin: true, user: user));
  }

  _onSelected(item) {
    switch (item) {
      case 'Add Equb':
        member!.status == "Active"
            ? _addEqubPage()
            : PanaraInfoDialog.show(context,
                message:
                    EkubLocalization.of(context)!.translate("inactive_member"),
                buttonText: EkubLocalization.of(context)!.translate("okay"),
                onTapDismiss: () {
                Navigator.pop(context);
              }, panaraDialogType: PanaraDialogType.normal);

        break;

      case 'Delete':
        PanaraConfirmDialog.show(context,
            title: EkubLocalization.of(context)!.translate("delete"),
            message:
                "${EkubLocalization.of(context)!.translate("confirm_delete")} ${args.member.fullName}?",
            confirmButtonText:
                EkubLocalization.of(context)!.translate("delete"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            imagePath: warningDialogIcon, onTapConfirm: () {
          Navigator.pop(context);
          _deleteMember(args.member.id.toString());
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.warning);

        break;
    }
  }

  _openProfile() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => ProfilePage(
                  image: image,
                  member: member,
                )));
  }

  Widget _buildProfileImage(String imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      placeholder: (context, url) => CircularProgressIndicator(),
      errorWidget: (context, url, error) => Icon(Icons.error),
      imageBuilder: (context, imageProvider) => CircleAvatar(
        radius: 30,
        backgroundImage: imageProvider,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    appLanguage = Provider.of<AppLanguage>(context, listen: true);

    var language = EkubLocalization.of(context)!;
    image = Provider.of<MemberProvider>(context, listen: true).image;
    member = Provider.of<MemberProvider>(context, listen: true).memberDetail;
    items = Provider.of<EqubDataProvider>(context, listen: true).equbs.equbs;
    totalEqubs =
        Provider.of<EqubDataProvider>(context, listen: true).equbs.totalMember;
    completedEqubs =
        Provider.of<MemberProvider>(context, listen: true).completedEqubs.equbs;

    if (isLoading) {
      return searchLoading();
    }

    if (!isConnected) {
      return NoConnectionWidget(
        fun: loadEqubs,
        isLoading: isLoading,
      );
    }

    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: EkubAppBar(
          key: _appBar,
          title:
              toCamelCase(args.member.fullName ?? language.translate("equbs")),
          widgets: [
            // user.role == "admin"
            //     ? Container()
            //     : SizedBox(
            //         width: 90,
            //         child: CustomDropdown<String>(
            //           onChanged: (value) {
            //             setState(() {
            //               selectedLanguage = value!;
            //               if (selectedLanguage == "En") {
            //                 appLanguage.changeLanguage(const Locale("en"));
            //               } else if (selectedLanguage == "አማ") {
            //                 appLanguage.changeLanguage(const Locale("am_ET"));
            //               } else if (selectedLanguage == "Or") {
            //                 appLanguage.changeLanguage(const Locale("fr"));
            //               } else if (selectedLanguage == "So") {
            //                 appLanguage.changeLanguage(const Locale("tl"));
            //               } else if (selectedLanguage == "ትግ") {
            //                 appLanguage.changeLanguage(const Locale("es"));
            //               }
            //             });
            //           },
            //           decoration: CustomDropdownDecoration(
            //             closedFillColor: Colors.white,
            //             closedSuffixIcon: Icon(
            //               Icons.language,
            //               color: bodyTextColor,
            //               size: 25,
            //             ),
            //             hintStyle: TextStyle(
            //                 color: themeProvider.getColor,
            //                 fontWeight: boldFont),
            //           ),
            //           items: languages,
            //           hintText: selectedLanguage.substring(0, 2),
            //           excludeSelected: true,
            //         ),
            //       ),
            IconButton(
                onPressed: () {
                  _loadProfile();
                },
                icon: const Icon(Icons.refresh)),
            user.role == "admin"
                ? PopupMenuButton<String>(
                    onSelected: _onSelected,
                    itemBuilder: (BuildContext context) {
                      return myMenuItems.map((String choice) {
                        return PopupMenuItem<String>(
                          value: choice,
                          child: Text(
                            choice,
                            style: const TextStyle(color: Colors.black),
                          ),
                        );
                      }).toList();
                    })
                : !isLoading &&
                        user.role == "equb_collector" &&
                        member!.status == "Active"
                    ? IconButton(
                        onPressed: () {
                          _addEqubPage();
                        },
                        icon: const Icon(Icons.add))
                    : Container(),
          ]),
      body: _buildContent(),
      drawer: user.role != "member" ? null : _drawer(context, language),
    );
  }

  Widget _buildContent() {
    return DefaultTabController(
      length: 2,
      child: RefreshIndicator(
        onRefresh: _loadProfile,
        key: _refreshIndicatorKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            user.role == "member"
                ? Container(
                    // alignment: Alignment.center,
                    width: MediaQuery.of(context).size.width,
                    padding: const EdgeInsets.only(bottom: 10, left: 20),
                    decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(20),
                            bottomRight: Radius.circular(20))),
                    child: Text(
                      items!.isEmpty
                          ? EkubLocalization.of(context)!
                              .translate("select_ekub_type")
                          : '${EkubLocalization.of(context)!.translate("ongoing")} ${EkubLocalization.of(context)!.translate("equbs")}',
                      style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 25),
                    ),
                  )
                : Container(
                    width: MediaQuery.of(context).size.width,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(20),
                            bottomRight: Radius.circular(20))),
                    child: TabBar(
                        labelStyle: const TextStyle(
                            color: Colors.white, fontWeight: FontWeight.bold),
                        unselectedLabelColor: Colors.black,
                        indicatorWeight: 3.0,
                        dividerHeight: 0,
                        indicatorSize: TabBarIndicatorSize.tab,
                        indicator: BoxDecoration(
                          color: themeProvider.getColor,
                          borderRadius: BorderRadius.circular(5),
                        ),
                        tabs: [
                          Tab(
                              child: Text(
                            items!.isEmpty
                                ? EkubLocalization.of(context)!
                                    .translate("select_ekub_type")
                                : '${EkubLocalization.of(context)!.translate("ongoing")} ${EkubLocalization.of(context)!.translate("equbs")}',
                          )),
                          Tab(
                            child: Text(EkubLocalization.of(context)!
                                .translate("completed_ekub")),
                          ),
                        ]),
                  ),
            user.role == "member"
                ? Expanded(
                    child: SizedBox(
                        height: Device.body(context),
                        child: listHolder(
                            items, themeProvider.getColor, member, false)),
                  )
                : Expanded(
                    child: TabBarView(children: [
                      SizedBox(
                          height: Device.body(context),
                          child: listHolder(completedEqubs,
                              themeProvider.getColor, member, true)),
                    ]),
                  ),
          ],
        ),
      ),
    );
  }

  Drawer _drawer(BuildContext context, EkubLocalization language) {
    return Drawer(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.only(top: 30),
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                      top: 30.0, left: 20, bottom: 25, right: 20),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => UpdateAccount(
                                  args: UpdateAccountArgs(
                                      isOnline: true, user: member!),
                                  user: member!),
                            ),
                          );
                        },
                        child: CircleAvatar(
                          radius: 30,
                          backgroundColor: themeProvider.getLightColor,
                          backgroundImage: image != null
                              ? MemoryImage(image!)
                              : AssetImage('assets/icons/user.jpg')
                                  as ImageProvider<Object>,
                        ),
                      ),
                      const SizedBox(
                        width: 15,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              toCamelCase(member!.fullName ?? ""),
                              style: TextStyle(
                                  color: themeProvider.getColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: fontMedium),
                            ),
                            Text(
                              member!.phone ?? "",
                              style: TextStyle(
                                  color: Colors.grey.shade500,
                                  fontWeight: FontWeight.bold,
                                  fontSize: fontMedium),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(
                  color: Colors.grey.shade400,
                ),
                const SizedBox(
                  height: 15,
                ),

                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.home_outlined,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    language.translate("home"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    loadEqubs();
                    Navigator.pop(context);
                  },
                ),
                // ListTile(
                //   leading: IconButton(
                //       onPressed: () {
                //         appLanguage.changeLanguage(Locale("en"));
                //       },
                //       icon: const Icon(Icons.language)),
                // ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.add_circle_outline,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("join_ekub"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => MainEqubsList(
                                  args: AddEqubArgs(
                                    isOnline: true,
                                    member: member!,
                                    isAdmin: false,
                                    user: user,
                                    // Indicate the source
                                  ),
                                )));
                  },
                ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.list_alt_outlined,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("completed_ekub"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => CompletedEqubs(
                                  memberId: member!.id.toString(),
                                )));
                  },
                ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.list_alt_outlined,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("passed_equb"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            PassedEqubs(memberId: args.member.id.toString()),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.person_outline,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("my_profile"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _openProfile();
                  },
                ),
                ListTile(
                  leading: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        color: ColorProvider.primary.withOpacity(.1),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.lock_outline,
                      color: ColorProvider.primary,
                      size: 25,
                    ),
                  ),
                  title: Text(
                    EkubLocalization.of(context)!.translate("change_password"),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: fontMedium),
                  ),
                  onTap: () {
                    Navigator.pushNamed(context, ChangePassword.routeName,
                        arguments: ChangePasswordArgs(
                            isOnline: true, role: user.role, fromDrawer: true));
                  },
                ),
                ListTile(
                    leading: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 5, vertical: 2),
                      height: 35,
                      width: 35,
                      decoration: BoxDecoration(
                          color: ColorProvider.primary.withOpacity(.1),
                          shape: BoxShape.circle),
                      child: Icon(
                        Icons.contact_emergency_outlined,
                        color: ColorProvider.primary,
                        size: 25,
                      ),
                    ),
                    onTap: () {
                      contactUsWidget(context);
                    },
                    title: Text(
                      EkubLocalization.of(context)!.translate("contact_us"),
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: fontMedium),
                    )),
              ],
            ),
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              height: 35,
              width: 35,
              decoration: BoxDecoration(
                  color: ColorProvider.primary.withOpacity(.1),
                  shape: BoxShape.circle),
              child: Icon(
                Icons.logout_outlined,
                color: ColorProvider.primary,
                size: 25,
              ),
            ),
            title: Text(
              EkubLocalization.of(context)!.translate("logout"),
              style: const TextStyle(
                  fontWeight: FontWeight.bold, fontSize: fontMedium),
            ),
            onTap: () {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: Text(language.translate("warning")),
                    content: Text(language.translate("confirm_logout")),
                    actions: [
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Colors.grey, // Set a color for cancel
                        ),
                        child: Text(language.translate("cancel")),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          gotoSignIn(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: themeProvider
                              .getLightColor, // Set a color for confirm
                        ),
                        child: Text(language.translate("confirm")),
                      ),
                    ],
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget listHolder(items, theme, Member? member, bool completed) {
    return items.isEmpty && completed
        ? Center(
            child: Text(
            EkubLocalization.of(context)!.translate("ekub_empty"),
            style:
                TextStyle(color: bodyTextColor, fontWeight: normalFontWeight),
          ))
        : items.isNotEmpty && !isLoading
            ? Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                        itemCount: items.length,
                        padding: const EdgeInsets.all(0.0),
                        itemBuilder: (context, index) {
                          return _buildListItems(context, items[index], index,
                              theme, completed, null);
                        }),
                  ),
                ],
              )
            : isLoading
                ? searchLoading()
                : SingleChildScrollView(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // const SizedBox(height: 30),
                          // Ad section with text and images
                          Container(
                            margin: const EdgeInsets.only(bottom: 20),
                            padding: const EdgeInsets.all(0),
                            // decoration: BoxDecoration(
                            //   color: Colors.blueAccent,
                            //   borderRadius: BorderRadius.circular(10),
                            // ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Text(
                                //   "Modern and Stable",
                                //   style: TextStyle(
                                //     color: Colors.white,
                                //     fontWeight: FontWeight.bold,
                                //     fontSize: 18,
                                //   ),
                                // ),
                                const SizedBox(height: 10),
                                Image.asset(
                                  'assets/icons/Mask_group.png', // Replace with your image path
                                  fit: BoxFit.cover,
                                ),
                              ],
                            ),
                          ),
                          if (_items.isEmpty)
                            const Center(
                              child: Text("No equbs found to subscribe"),
                            ),
                          // _items.isEmpty
                          //     ? const Center(
                          //         child: Text("No equbs found to subscribe"),
                          //       )
                          //     : Container(
                          //         padding: const EdgeInsets.all(5),
                          //         decoration: BoxDecoration(
                          //           color: Colors.grey,
                          //           borderRadius: BorderRadius.circular(10),
                          //         ),
                          //         child: buildEqubButtons(),
                          //       ),
                          if (selectedEqubType != null)
                            ListOfEqub(
                              title: 'Equb Details',
                              description:
                                  'Equb Type: ${selectedEqubType?.name ?? "N/A"}\n'
                                  'Frequency: Daily\n'
                                  'Member: ${widget.args.member.fullName}\n'
                                  'User Role: ${user.role}',
                              equbType: selectedEqubType!,
                              frequency: "Daily",
                              args: AddEqubArgs(
                                isOnline: widget.args.isOnline,
                                member: widget.args.member,
                                isAdmin: widget.args.isAdmin ?? false,
                                user: user,
                              ),
                            ),

                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  );
  }

  Widget _buildListItems(BuildContext context, Equb equb, int item, theme,
      bool completed, String? equbImage) {
    // Calculate total paid amount for this specific equb
    double totalPaidAmount = double.tryParse(equb.totalPayment ?? "0") ?? 0.0;

    // Get total expected amount
    double totalExpectedAmount =
        double.tryParse(equb.totalAmount ?? "0") ?? 0.0;

    // Calculate remaining amount
    double remainingAmount = totalExpectedAmount - totalPaidAmount;

    return GestureDetector(
      onTap: () {
        // Print the amounts when equb is clicked
        print('Total Paid Amount: ETB ${totalPaidAmount.toStringAsFixed(2)}');
        print(
            'Total Expected Amount: ETB ${totalExpectedAmount.toStringAsFixed(2)}');
        print('Remaining Amount: ETB ${remainingAmount.toStringAsFixed(2)}');

        // Navigation logic with remaining amount
        MemberETabsArgs arg = MemberETabsArgs(
            completed: completed,
            equb: equb,
            isOnline: true,
            role: user.role,
            index: item,
            memberId: user.id,
            member: member!,
            remainingAmount: remainingAmount // Add the remaining amount here
            );
        Navigator.pushNamed(context, MemberTabs.routeName, arguments: arg);
      },
      child: _listUi(theme, equb),
    );
  }

  _listUi(Color theme, Equb equb) {
    return Padding(
      padding: const EdgeInsets.only(left: 15, right: 15.0, top: 10),
      child: EqubCard(
        status: equb.status ?? "loading...",
        round: equb.equbType!.round ?? "loading...",
        amount: equb.amount ?? "loading...",
        icon: Icons.person,
        equbType: equb.equbType?.name ?? "loading...",
        totalAmount: equb.totalAmount ?? "loading...",
        startDate: equb.startDate ?? "loading...",
        endDate: equb.endDate ?? "loading...",
        lotteryDate: equb.lotteryDate ?? "loading...",
        totalPayment: equb.totalPayment ?? "loading...",
        remainingPayment: equb.remainingPayment ?? "loading...",
        remainingLotteryDate: equb.remainingLotteryDate ?? "loading...",
        theme: themeProvider,
      ),
    );
  }

  Widget buildEqubButtons() {
    return Row(
      children: [
        // Automatic Equb Button
        Expanded(
          child: GestureDetector(
            onTap: () {
              // Handle Automatic Equb button tap
              setState(() {
                selectedIndex = 0; // Example index for Automatic Equb
                // Additional logic for Automatic Equb
              });
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 5),
              padding: const EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                color: selectedIndex == 0 ? Colors.green : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Center(
                child: Text(
                  "Automatic Equb",
                  style: TextStyle(
                    color: selectedIndex == 0 ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
        // Manual Equb Button
        Expanded(
          child: GestureDetector(
            onTap: () {
              // Ensure you have a valid EqubType instance to pass
              EqubType equbType = EqubType(); // Replace with actual instance

              // Navigate to ManualEqubList when Manual Equb button is tapped
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ManualEqubList(
                    equbType: equbType, // Pass the required equbType argument
                    args: AddEqubArgs(
                      isOnline: true,
                      member: member!,
                      isAdmin: false,
                      user: user,
                    ),
                  ),
                ),
              );
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 5),
              padding: const EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                color: selectedIndex == 1 ? Colors.green : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Center(
                child: Text(
                  "Manual Equb",
                  style: TextStyle(
                    color: selectedIndex == 1 ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Scaffold inactiveUser(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            onPressed: () {
              loadEqubs();
            },
            icon: const Icon(Icons.refresh)),
        backgroundColor: Colors.black,
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Row(
              children: [
                Text(
                  EkubLocalization.of(context)!.translate("logout"),
                  style: const TextStyle(
                      color: Colors.red,
                      fontSize: fontMedium,
                      fontWeight: normalFontWeight),
                ),
                IconButton(
                    onPressed: () {
                      PanaraConfirmDialog.show(
                        context,
                        title:
                            EkubLocalization.of(context)!.translate("warning"),
                        message: EkubLocalization.of(context)!
                            .translate("confirm_logout"),
                        confirmButtonText:
                            EkubLocalization.of(context)!.translate("confirm"),
                        cancelButtonText:
                            EkubLocalization.of(context)!.translate("cancel"),
                        onTapCancel: () {
                          Navigator.pop(context);
                        },
                        onTapConfirm: () {
                          gotoSignIn(context);
                        },
                        imagePath: warningDialogIcon,
                        panaraDialogType: PanaraDialogType.warning,
                      );
                    },
                    icon: const Icon(
                      Icons.logout,
                      color: Colors.red,
                      size: 20,
                    )),
              ],
            ),
          )
        ],
      ),
      body: SafeArea(
        child: isLoading
            ? searchLoading()
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Image(
                    height: 150,
                    image: AssetImage(
                      "assets/icons/inactive_user.png",
                    ),
                    fit: BoxFit.cover,
                  ),
                  Center(
                    child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 30),
                        child: Text(
                          EkubLocalization.of(context)!
                              .translate("inactive_user"),
                          style: TextStyle(
                              color: bodyTextColor,
                              fontWeight: normalFontWeight),
                        )),
                  ),
                ],
              ),
      ),
    );
  }

  Widget roleContainer(
      BuildContext context, EqubType type, bool isSelected, theme, equbImage) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: .1,
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
        color: Colors.white,
        borderRadius: const BorderRadius.all(Radius.circular(15)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Container(
          // alignment: Alignment.center,
          // width: 65,
          // height: 65,
          // decoration: BoxDecoration(
          //   borderRadius: BorderRadius.circular(10),
          //   color: themeProvider.getLightColor,
          // ),
          // child: equbImage != null
          //     ? ClipRRect(
          //         borderRadius: BorderRadius.circular(10),
          //         child: CachedNetworkImage(
          //           imageUrl: equbImage,
          //           fit: BoxFit.cover,
          //           width: 55,
          //           height: 55,
          //           placeholder: (context, url) =>
          //               CircularProgressIndicator(),
          //           errorWidget: (context, url, error) => Icon(
          //             Icons.image_not_supported,
          //             color: theme.getColor,
          //           ),
          //         ),
          //       )
          //     : Icon(
          //         Icons.image_not_supported,
          //         color: themeProvider.getLightColor,
          //       ),
          //),
          const SizedBox(height: 10),
          Text(
            type.name!,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium,
                overflow: TextOverflow.ellipsis),
          ),
          const SizedBox(height: 5),
        ],
      ),
    );
  }
}

class ListOfEqub extends StatefulWidget {
  final String title;
  final String description;
  final EqubType equbType;
  final String frequency;
  final AddEqubArgs args;

  ListOfEqub(
      {required this.title,
      required this.description,
      required this.equbType,
      required this.frequency,
      required this.args});

  @override
  _ListOfEqubState createState() => _ListOfEqubState();
}

class _ListOfEqubState extends State<ListOfEqub> {
  bool _onProcess = false;
  Member? member;
  late ThemeProvider themeProvider;
  late Future<List<EqubType>> _equbFuture;
  bool _agreedToTerms = false;

  @override
  void initState() {
    super.initState();
    member = widget.args.member;
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    loadProfile();
    _equbFuture = _loadEqubs();
  }

  Future<List<EqubType>> _loadEqubs() {
    print("Loading equbs...");
    if (member == null || member?.id == null) {
      throw Exception('Member ID is required');
    }
    return Provider.of<EqubDataProvider>(context, listen: false)
        .loadEqubsByFrequency(context, widget.frequency, member!.id!);
  }

  void loadProfile() {
    // Implement the logic you want to execute
    print("Loading profile...");
    // Add your logic here
  }

  void refreshEqubList() {
    setState(() {
      print("Refreshing equb list...");
      _equbFuture = _loadEqubs(); // Reload the list
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<EqubType>>(
      future: _equbFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return searchLoading();
        } else if (snapshot.hasError) {
          print('Error: ${snapshot.error}');
          return Center(child: Text('Error: ${snapshot.error}'));
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          print('No Equbs found.');

          // // Retry logic
          // Future.delayed(Duration(seconds: 2), () {

          //   _retryLoadEqubs();
          // });

          return Center(child: Text('No Equbs found!'));
        } else {
          print('Equbs loaded: ${snapshot.data!.length}');
          return SingleChildScrollView(
            child: Column(
              children: snapshot.data!.map((equb) {
                return _buildEqubCard(equb);
              }).toList(),
            ),
          );
        }
      },
    );
  }

  void _retryLoadEqubs() {
    setState(() {
      _equbFuture = _loadEqubs(); // Reload the list
    });
  }

  Widget _buildEqubCard(EqubType equb) {
    // // Check if equb is null or any critical property is null
    // if (equb == null || equb.name == null || equb.type == null) {
    //   return Text('Invalid Equb Data');
    // }

    return equb.type == "Automatic" || equb.type == "Seasonal"
        ? Card(
            margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 1.0),
            elevation: 4.0,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4.0),
              child: Padding(
                padding: EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Image.asset(
                                'assets/icons/Money_hand.png',
                                color: themeProvider.getLightColor,
                                width: 20,
                                height: 20,
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      equb.name!,
                                      style: TextStyle(
                                          color: themeProvider.getLightColor,
                                          fontWeight: boldFont),
                                    ),
                                    SizedBox(height: 10),
                                    _buildInfoRow(
                                      'Start Date',
                                      _formatDate(equb.startDate),
                                    ),
                                    _buildInfoRow(
                                        'End Date', _formatDate(equb.endDate)),
                                    _buildInfoRow('Amount', equb.amount,
                                        fontSizes: {'Amount': 14.0}),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    ElevatedButton(
                      onPressed: _onProcess
                          ? null
                          : () async {
                              setState(() {
                                _onProcess = true;
                              });

                              // Show loading indicator immediately
                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return Center(
                                    child: CircularProgressIndicator(),
                                  );
                                },
                              );

                              // Check internet connectivity asynchronously
                              bool isConnected = await InternetConnectivity()
                                  .checkInternetConnectivty(context, true);
                              if (!isConnected) {
                                Navigator.pop(context);
                                setState(() {
                                  _onProcess = false;
                                });
                                return;
                              }

                              // Fetch member data asynchronously
                              var member = await Provider.of<MemberProvider>(
                                      context,
                                      listen: false)
                                  .getMember(context,
                                      widget.args.member?.id.toString() ?? '0');

                              if (member.status != "Active" &&
                                  widget.args.user?.role == "member") {
                                Navigator.pop(context);
                                showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return AlertDialog(
                                      title: const Row(
                                        children: [
                                          Icon(Icons.warning,
                                              color: Colors.red),
                                          SizedBox(width: 8),
                                          Text('Account Inactive'),
                                        ],
                                      ),
                                      content: const Text(
                                        "Your account is inactive",
                                        style: TextStyle(fontSize: 16),
                                      ),
                                      actions: [
                                        ElevatedButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                themeProvider.getLightColor,
                                          ),
                                          child: Text("Okay"),
                                        ),
                                      ],
                                    );
                                  },
                                );
                                setState(() {
                                  _onProcess = false;
                                });
                                return;
                              }

                              Navigator.pop(context);

                              // Print the data that will be sent
                              print("Data to be sent:");
                              print("Member ID: ${member.id}");
                              print("Equb Type ID: ${equb.id}");
                              print("Amount: ${equb.amount}");
                              print(
                                  "Total Amount: ${calculateExpectedAmount(equb.amount?.toDouble() ?? 0.0, equb.startDate ?? '', equb.endDate ?? '')}");
                              print("Start Date: ${equb.startDate}");
                              print("End Date: ${equb.endDate}");
                              print(
                                  "Lottery Date: ''"); // Assuming lottery date is not set here
                              print(
                                  "Timeline t: '0'"); // Assuming timeline is not set here
                              print("Type: ${equb.type}");
                              // Show modal bottom sheet
                              showModalBottomSheet(
                                context: context,
                                isDismissible: true,
                                isScrollControlled: true,
                                builder: (builder) {
                                  return StatefulBuilder(
                                    builder: (BuildContext context,
                                        StateSetter setModalState) {
                                      return Container(
                                        height:
                                            MediaQuery.of(context).size.height *
                                                0.9,
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                        ),
                                        child: Column(
                                          children: [
                                            Flexible(
                                              child: SingleChildScrollView(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          vertical: 10,
                                                          horizontal: 20),
                                                      child: Text(
                                                        "${EkubLocalization.of(context)!.translate("terms")} ${equb.name}",
                                                        style: TextStyle(
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          fontSize: fontBig,
                                                        ),
                                                      ),
                                                    ),
                                                    Divider(
                                                      color:
                                                          Colors.grey.shade400,
                                                    ),
                                                    equb.term == null
                                                        ? Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        15),
                                                            child: Text(
                                                              "${EkubLocalization.of(context)!.translate("no_terms")}.",
                                                              style:
                                                                  const TextStyle(
                                                                color: Colors
                                                                    .black38,
                                                                fontWeight:
                                                                    normalFontWeight,
                                                                fontSize:
                                                                    fontMedium,
                                                              ),
                                                            ),
                                                          )
                                                        : Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        15),
                                                            child: HtmlWidget(
                                                              "${equb.term}",
                                                            ),
                                                          ),
                                                    SizedBox(height: 20),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Checkbox(
                                                  value: _agreedToTerms,
                                                  onChanged: (bool? value) {
                                                    setModalState(() {
                                                      _agreedToTerms =
                                                          value ?? false;
                                                    });
                                                  },
                                                ),
                                                TextButton(
                                                  onPressed: () {
                                                    setModalState(() {
                                                      _agreedToTerms =
                                                          !_agreedToTerms;
                                                    });
                                                  },
                                                  child: Text(
                                                    'I agree to the terms and conditions',
                                                    style: TextStyle(
                                                      color: themeProvider
                                                          .getLightColor,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Container(
                                              width: double.infinity,
                                              padding:
                                                  const EdgeInsets.all(16.0),
                                              child: ElevatedButton(
                                                onPressed: _agreedToTerms
                                                    ? () {
                                                        final equbId =
                                                            equb.id ?? 0;
                                                        final typeId =
                                                            equb.id ?? 0;
                                                        final amount = equb
                                                                .amount
                                                                ?.toString() ??
                                                            '0';
                                                        final totalAmount =
                                                            calculateExpectedAmount(
                                                                equb.amount
                                                                        ?.toDouble() ??
                                                                    0.0,
                                                                equb.startDate ??
                                                                    '',
                                                                equb.endDate ??
                                                                    '');
                                                        final startDate = equb
                                                                .startDate
                                                                ?.split(
                                                                    ' ')[0] ??
                                                            '';
                                                        final endDate =
                                                            equb.endDate?.split(
                                                                    ' ')[0] ??
                                                                '';
                                                        final lotteryDate = '';
                                                        final timeline = '0';

                                                        _addEqub(
                                                          member!.id ?? 0,
                                                          typeId,
                                                          amount,
                                                          totalAmount,
                                                          startDate,
                                                          endDate,
                                                          lotteryDate,
                                                          timeline,
                                                          equb.type ??
                                                              'Automatic',
                                                        );

                                                        Navigator.pop(context);
                                                      }
                                                    : null,
                                                style: ButtonStyle(
                                                  foregroundColor:
                                                      MaterialStateProperty
                                                          .resolveWith<
                                                              Color>((Set<
                                                                  MaterialState>
                                                              states) {
                                                    if (states.contains(
                                                        MaterialState
                                                            .disabled)) {
                                                      return Colors
                                                          .white; // Text color when disabled
                                                    }
                                                    return Colors
                                                        .white; // Text color when enabled
                                                  }),
                                                  backgroundColor:
                                                      MaterialStateProperty
                                                          .resolveWith<
                                                              Color>((Set<
                                                                  MaterialState>
                                                              states) {
                                                    if (states.contains(
                                                        MaterialState
                                                            .disabled)) {
                                                      return Colors
                                                          .grey; // Background color when disabled
                                                    }
                                                    return themeProvider
                                                        .getLightColor; // Background color when enabled
                                                  }),
                                                  elevation:
                                                      MaterialStateProperty.all(
                                                          0),
                                                  shadowColor:
                                                      MaterialStateProperty.all(
                                                          Colors.transparent),
                                                  surfaceTintColor:
                                                      MaterialStateProperty.all(
                                                          Colors.transparent),
                                                ),
                                                child: Text('Add Equb'),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                              ).whenComplete(() {
                                setState(() {
                                  _onProcess = false;
                                });
                              });
                            },
                      style: TextButton.styleFrom(
                        foregroundColor:
                            const Color.fromARGB(255, 255, 255, 255),
                        backgroundColor: themeProvider.getLightColor,
                        padding:
                            EdgeInsets.symmetric(vertical: 0, horizontal: 24.0),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(38.0),
                        ),
                        //elevation: 5.0,
                      ),
                      child: Text(
                        'Join',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ))
        : Card(
            margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            elevation: 4.0,
            child: ListTile(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AddEqub(
                      slectedEqubType: equb,
                      args: AddEqubArgs(
                        isOnline: true,
                        member: widget.args.member,
                        isAdmin: false,
                        user: widget.args.user,
                      ),
                    ),
                  ),
                );
              },
            ),
          );
  }

  String calculateExpectedAmount(
      double amount, String startDate, String endDate) {
    int days =
        DateTime.parse(endDate).difference(DateTime.parse(startDate)).inDays;
    return (amount * days).toString();
  }

  _addEqub(
      int memberId,
      int typeId,
      String amount,
      String totalAmount,
      String startDate,
      String endDate,
      String lotteryDate,
      String timeline,
      String type) async {
    var sender = EqubDataProvider(httpClient: http.Client());
    var response = await sender.addEqubToMember(context, memberId, typeId,
        amount, totalAmount, startDate, endDate, lotteryDate, timeline, type);

    if (response["code"] == 200) {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Center(
            child: CircularProgressIndicator(),
          );
        },
      );

      // Show a snackbar after a short delay
      Future.delayed(Duration(seconds: 1), () {
        Navigator.pop(context); // Close the loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("You have successfully joined the equb."),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );

        // Perform navigation automatically after showing the snackbar
        Future.delayed(Duration(seconds: 2), () {
          if (widget.args.user!.role == "equb_collector") {
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                builder: (context) => MembersScreen(
                  args: MemberEqubsArgs(
                    member: Member(
                      id: int.parse(widget.args.user!.id ?? "0"),
                      fullName: widget.args.user!.fullName,
                      phone: widget.args.user!.phoneNumber,
                      gender: widget.args.user!.gender,
                    ),
                    isOnline: true,
                  ),
                ),
              ),
              (route) => false,
            );
          } else {
            _openHomeScreen(
              widget.args.user!.role!.toLowerCase() == "admin",
              widget.args.user!.role!,
            );
          }
        });
      });
    } else {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                SizedBox(width: 8),
                Text("Error"),
              ],
            ),
            content: Text(
              response["message"],
              style: TextStyle(fontSize: 16),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text("Retry",
                    style: TextStyle(color: themeProvider.getLightColor)),
              ),
            ],
          );
        },
      );
    }
    setState(() {
      _onProcess = false;
    });
  }

  _openHomeScreen(bool isAdmin, String role) async {
    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);
    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }

  Widget _buildInfoRow(String label, dynamic value,
      {Map<String, double>? fontSizes}) {
    double fontSize =
        fontSizes?[label] ?? 12.0; // Default to 14.0 if not specified
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
              fontSize: fontSize, // Use the specified font size
            ),
          ),
          Expanded(
            child: Text(
              value?.toString() ?? 'N/A',
              style: TextStyle(
                color: Colors.black54,
                fontSize: fontSize,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? date) {
    if (date == null || date.isEmpty) return 'N/A';
    try {
      final parsedDate = DateTime.parse(date);
      return DateFormat('MMMM dd, yyyy').format(parsedDate);
    } catch (e) {
      return 'Invalid date';
    }
  }
}
