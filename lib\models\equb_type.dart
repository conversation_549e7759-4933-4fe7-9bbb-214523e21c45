import 'dart:convert';

import 'package:ekub/utils/tools.dart';
import 'package:flutter/foundation.dart';

class EqubType {
  int? id;
  String? name;
  String? round;
  String? type;
  String? term;
  String? lotteryDate;
  String? rote;
  String? remark;
  String? status;
  String? startDate;
  String? endDate;
  String? createdAt;
  String? updatedAt;
  String? active;
  int? mainEqubId;
  String? deletedAt;
  String? quota;
  int? expectedMembers;
  String? image;
  String? imageUrl;
  MainEqub? mainEqub;
  int? amount;
  String? description;
  String? totalAmount;
  String? timeline;

  EqubType(
      {this.id,
      this.name,
      this.round,
      this.type,
      this.term,
      this.status,
      this.rote,
      this.remark,
      this.startDate,
      this.endDate,
      this.createdAt,
      this.lotteryDate,
      this.updatedAt,
      this.active,
      this.mainEqubId,
      this.deletedAt,
      this.quota,
      this.expectedMembers,
      this.image,
      this.imageUrl,
      this.mainEqub,
      this.amount,
      this.description,
      this.totalAmount,
      this.timeline});

  factory EqubType.fromJson(Map<String, dynamic> json) {
    try {
      return EqubType(
        id: json["id"] is String ? int.tryParse(json["id"]) : json["id"],
        name: json["name"] ?? "Unnamed Equb",
        round: json["round"]?.toString() ?? "",
        description: json["description"],
        amount: json["amount"] is String
            ? int.tryParse(json["amount"] ?? "0")
            : (json["amount"] ?? 0),
        totalAmount: json["total_amount"]?.toString() ?? "0",
        startDate: json["start_date"] ?? DateTime.now().toString(),
        endDate: json["end_date"] ??
            DateTime.now().add(const Duration(days: 30)).toString(),
        lotteryDate: json["lottery_date"],
        status: json["status"] ?? "Active",
        createdAt: json["created_at"] ?? DateTime.now().toString(),
        updatedAt: json["updated_at"] ?? DateTime.now().toString(),
        rote: json["rote"] ?? "Daily",
        remark: json["remark"]?.toString(),
        term: json["term"]?.toString(),
        active: json["active"]?.toString() ?? "1",
        mainEqubId: json["main_equb_id"] is String
            ? int.tryParse(json["main_equb_id"] ?? "0")
            : json["main_equb_id"],
        deletedAt: json["deleted_at"]?.toString(),
        quota: json["quota"]?.toString(),
        expectedMembers: json["expected_members"] is String
            ? int.tryParse(json["expected_members"] ?? "0")
            : json["expected_members"],
        timeline: json["timeline"]?.toString() ?? "30",
        type: json["type"] ?? "Automatic",
        image: json["image"],
        imageUrl: json["image_url"],
        mainEqub: json["main_equb"] != null
            ? MainEqub.fromJson(json["main_equb"])
            : null,
      );
    } catch (e) {
      debugPrint("Error parsing EqubType: $e");
      debugPrint("JSON data: $json");

      // Return a default EqubType with minimal required fields
      return EqubType(
        id: 0,
        name: "Error Equb",
        status: "Error",
        type: "Unknown",
        rote: "Daily",
        amount: 0,
      );
    }
  }

  // This method is incorrectly implemented - it should be a factory method
  // that returns a list of EqubType objects
  static List<EqubType> fromStringObject(List<dynamic> parsedJson) {
    return parsedJson.map((i) => EqubType.fromJson(i)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'round': round,
      'type': type,
      'term': term,
      'status': status,
      'rote': rote,
      'remark': remark,
      'startDate': startDate,
      'endDate': endDate,
      'createdAt': createdAt,
      'lotteryDate': lotteryDate,
      'updatedAt': updatedAt,
      'active': active,
      'mainEqubId': mainEqubId,
      'deletedAt': deletedAt,
      'quota': quota,
      'expectedMembers': expectedMembers,
      'image': image,
      'imageUrl': imageUrl,
      'mainEqub': mainEqub?.toJson(),
      'amount': amount,
    };
  }
}

class EqubTypes {
  List<EqubType>? equbTypes;
  int totalMember;

  EqubTypes({required this.totalMember, required this.equbTypes});

  EqubTypes.fromJson(List<dynamic> parsedJson, this.totalMember) {
    equbTypes = parsedJson.map((i) => EqubType.fromJson(i)).toList();
  }

  String toJson() {
    String data = jsonEncode(equbTypes?.map((i) => i.toJson()).toList());
    return data;
  }
}

class ActiveEqubs {
  List<EqubType>? equbs;
  int totalMember;

  ActiveEqubs({required this.totalMember, required this.equbs});

  ActiveEqubs.fromJson(List<dynamic> parsedJson, this.totalMember) {
    equbs = parsedJson.map((i) => EqubType.fromJson(i)).toList();
  }

  String toJson() {
    String data = jsonEncode(equbs?.map((i) => i.toJson()).toList());
    return data;
  }
}

class InActiveEqubs {
  List<EqubType>? equbs;
  int totalMember;

  InActiveEqubs({required this.totalMember, required this.equbs});

  InActiveEqubs.fromJson(List<dynamic> parsedJson, this.totalMember) {
    equbs = parsedJson.map((i) => EqubType.fromJson(i)).toList();
  }

  String toJson() {
    String data = jsonEncode(equbs?.map((i) => i.toJson()).toList());
    return data;
  }
}

class EqubStore {
  bool isLoaded;
  EqubTypes equbs;
  ActiveEqubs activeEqubs;
  InActiveEqubs inActiveEqubs;

  EqubStore(
      {required this.isLoaded,
      required this.equbs,
      required this.activeEqubs,
      required this.inActiveEqubs});

  factory EqubStore.fromJson(Map<String, dynamic> json) {
    return EqubStore(
        isLoaded: false,
        equbs: EqubTypes.fromJson(json["equbTypes"], 0),
        activeEqubs: ActiveEqubs.fromJson(json["activeEqubType"], 0),
        inActiveEqubs: InActiveEqubs.fromJson(json["deactiveEqubType"], 0));
  }
}

class MainEqub {
  int? id;
  String? name;
  int? createdBy;
  String? remark;
  String? status;
  String? active;
  String? createdAt;
  String? updatedAt;
  String? imageUrl;

  MainEqub({
    this.id,
    this.name,
    this.createdBy,
    this.remark,
    this.status,
    this.active,
    this.createdAt,
    this.updatedAt,
    this.imageUrl,
  });

  factory MainEqub.fromJson(Map<String, dynamic> json) {
    return MainEqub(
      id: json["id"],
      name: json["name"],
      createdBy: json["created_by"],
      remark: json["remark"],
      status: json["status"],
      active: json["active"],
      createdAt: json["created_at"],
      updatedAt: json["updated_at"],
      imageUrl: json["image_url"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'createdBy': createdBy,
      'remark': remark,
      'status': status,
      'active': active,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'imageUrl': imageUrl,
    };
  }
}
