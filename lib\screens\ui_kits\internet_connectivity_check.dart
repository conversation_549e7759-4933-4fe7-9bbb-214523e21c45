import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:internet_connection_checker/internet_connection_checker.dart';

import '../../repository/ekub_localization.dart';

/// Enum representing different types of connectivity errors
enum ConnectivityErrorType {
  /// No internet connection detected
  noConnection,

  /// Connection is too slow or unstable
  unstableConnection,

  /// Server is unreachable
  serverUnreachable,

  /// Request timed out
  timeout,

  /// Unknown error
  unknown
}

/// Result of connectivity check
class ConnectionCheckResult {
  final bool isConnected;
  final ConnectivityErrorType? errorType;
  final String? errorDetails;

  ConnectionCheckResult({
    required this.isConnected,
    this.errorType,
    this.errorDetails,
  });

  /// Create a successful connection result
  factory ConnectionCheckResult.success() {
    return ConnectionCheckResult(isConnected: true);
  }

  /// Create a failed connection result with error type
  factory ConnectionCheckResult.failure(ConnectivityErrorType type,
      [String? details]) {
    return ConnectionCheckResult(
      isConnected: false,
      errorType: type,
      errorDetails: details,
    );
  }
}

class InternetConnectivity {
  /// Check internet connectivity with detailed error information
  /// Returns a Future<bool> for backward compatibility
  Future<bool> checkInternetConnectivty(
      BuildContext ctx, bool showMessage) async {
    try {
      // Print debug information
      print("[Connectivity] Starting connectivity check");

      final result = await checkDetailedConnectivity();

      // Print result
      print(
          "[Connectivity] Check result: isConnected=${result.isConnected}, errorType=${result.errorType}, details=${result.errorDetails}");

      if (!result.isConnected && showMessage && ctx.mounted) {
        _showErrorMessage(ctx, result);
      }

      return result.isConnected;
    } catch (e) {
      // Print exception
      print("[Connectivity] Exception during connectivity check: $e");

      if (showMessage && ctx.mounted) {
        _showErrorMessage(
            ctx,
            ConnectionCheckResult.failure(
                ConnectivityErrorType.unknown, e.toString()));
      }
      return false;
    }
  }

  /// Check internet connectivity with detailed error information
  Future<ConnectionCheckResult> checkDetailedConnectivity() async {
    try {
      if (kIsWeb) {
        print("[Connectivity] Running web-specific check");
        // Web-specific check - for web, we'll assume connection is available
        // and rely on API calls to detect actual connectivity
        try {
          print("[Connectivity] Using navigator.onLine check for web");

          // This is a simple check that works in web browsers
          // It only tells if the browser has network connectivity, not internet access
          bool isOnline =
              true; // Default to true and let API calls determine actual connectivity

          print("[Connectivity] Web connectivity check result: $isOnline");
          if (isOnline) {
            return ConnectionCheckResult.success();
          } else {
            return ConnectionCheckResult.failure(
                ConnectivityErrorType.noConnection,
                'Browser reports no network connection');
          }
        } catch (e) {
          print("[Connectivity] Web connectivity check error: $e");
          // Even if check fails, return success and let API calls determine actual connectivity
          return ConnectionCheckResult.success();
        }
      } else {
        print("[Connectivity] Running mobile-specific check");
        // Mobile-specific check
        try {
          print("[Connectivity] Checking InternetConnectionChecker");
          bool hasInternet = await InternetConnectionChecker().hasConnection;
          print(
              "[Connectivity] InternetConnectionChecker result: $hasInternet");

          print("[Connectivity] Checking Connectivity package");
          List<ConnectivityResult> connectivityResult =
              await Connectivity().checkConnectivity();
          print("[Connectivity] Connectivity results: $connectivityResult");

          bool hasNetworkInterface =
              connectivityResult.contains(ConnectivityResult.mobile) ||
                  connectivityResult.contains(ConnectivityResult.wifi);
          print("[Connectivity] Has network interface: $hasNetworkInterface");

          if (!hasNetworkInterface) {
            print("[Connectivity] No network interface available");
            return ConnectionCheckResult.failure(
                ConnectivityErrorType.noConnection);
          }

          if (!hasInternet) {
            print("[Connectivity] Network interface available but no internet");
            // We have WiFi/mobile data but no internet
            return ConnectionCheckResult.failure(
                ConnectivityErrorType.unstableConnection);
          }

          // Additional check to verify server connectivity
          try {
            print("[Connectivity] Making HTTP request to Google.com");
            final response = await http
                .get(Uri.parse('https://www.google.com'))
                .timeout(const Duration(seconds: 5));

            print(
                "[Connectivity] HTTP response status: ${response.statusCode}");
            if (response.statusCode == 200) {
              return ConnectionCheckResult.success();
            } else {
              return ConnectionCheckResult.failure(
                  ConnectivityErrorType.serverUnreachable,
                  'Server returned status code: ${response.statusCode}');
            }
          } on TimeoutException {
            print("[Connectivity] HTTP request timed out");
            return ConnectionCheckResult.failure(ConnectivityErrorType.timeout);
          } catch (e) {
            print("[Connectivity] HTTP request error: $e");
            // We have internet but can't reach the server
            return ConnectionCheckResult.failure(
                ConnectivityErrorType.serverUnreachable, e.toString());
          }
        } catch (e) {
          print("[Connectivity] Error during mobile connectivity check: $e");
          return ConnectionCheckResult.failure(
              ConnectivityErrorType.unknown, e.toString());
        }
      }
    } catch (e) {
      print("[Connectivity] Unexpected error: $e");
      return ConnectionCheckResult.failure(
          ConnectivityErrorType.unknown, e.toString());
    }
  }

  /// Show appropriate error message based on the connectivity result
  void _showErrorMessage(BuildContext ctx, ConnectionCheckResult result) {
    if (result.isConnected) return;

    String message;

    // For web, we'll show more specific messages about API connectivity
    if (kIsWeb) {
      // On web, most connectivity issues are related to API access
      if (result.errorDetails != null &&
          result.errorDetails!.contains("XMLHttpRequest")) {
        // This is a CORS issue - likely trying to access an API that doesn't allow it
        message = "Unable to connect to the server. Please try again later.";
      } else {
        // Generic API connectivity issue
        message = EkubLocalization.of(ctx)!.translate("error_message");
      }
    } else {
      // For mobile, we'll use the original error types
      switch (result.errorType) {
        case ConnectivityErrorType.noConnection:
          message = "${EkubLocalization.of(ctx)!.translate("no_connection")}!";
          break;
        case ConnectivityErrorType.unstableConnection:
          message = "${EkubLocalization.of(ctx)!.translate("connection_msg")}!";
          break;
        case ConnectivityErrorType.serverUnreachable:
          message =
              "${EkubLocalization.of(ctx)!.translate("internet_connection")}!";
          break;
        case ConnectivityErrorType.timeout:
          message = EkubLocalization.of(ctx)!.translate("timeout_message");
          break;
        case ConnectivityErrorType.unknown:
        default:
          message = EkubLocalization.of(ctx)!.translate("error_message");
          break;
      }
    }

    ScaffoldMessenger.of(ctx).showSnackBar(SnackBar(
      backgroundColor: Colors.red,
      content: Text(
        message,
        style: TextStyle(
          fontSize: fontMedium,
          fontWeight: normalFontWeight,
          color: whiteText,
        ),
      ),
      duration: const Duration(seconds: 3),
    ));
  }
}
