import 'package:intl/intl.dart';
import 'package:flutter/material.dart';

class LanguageConstants {
  // Date Format
  static const String defaultLocale = 'en_US';

  // Common Strings
  static const String loading = 'Loading...';
  static const String error = 'Error';
  static const String retry = 'Retry';
  static const String cancel = 'Cancel';
  static const String confirm = 'Confirm';
  static const String okay = 'Okay';
  static const String warning = 'Warning';

  // Equb Related
  static const String selectEqubType = 'Select Equb Type';
  static const String ongoingEqubs = 'Ongoing Equbs';
  static const String completedEqubs = 'Completed Equbs';
  static const String noEqubsFound = 'No Equbs Found';
  static const String joinEqub = 'Join Equb';
  static const String addEqub = 'Add Equb';
  static const String deleteEqub = 'Delete Equb';
  static const String confirmDelete =
      'Are you sure you want to delete this equb?';

  // Member Related
  static const String inactiveMember = 'Inactive Member';
  static const String confirmLogout = 'Are you sure you want to logout?';
  static const String logout = 'Logout';

  // Format date with consistent locale
  static String formatDate(String? date, {BuildContext? context}) {
    if (date == null || date.isEmpty) return 'N/A';
    try {
      final parsedDate = DateTime.parse(date);

      // Use the current app locale if context is provided
      if (context != null) {
        final locale = Localizations.localeOf(context).toString();
        return DateFormat('MMMM dd, yyyy', locale).format(parsedDate);
      }

      // Fallback to default format
      return DateFormat('MMMM dd, yyyy', defaultLocale).format(parsedDate);
    } catch (e) {
      return 'Invalid date';
    }
  }
}
