// Web implementation of JavaScript execution

// ignore: avoid_web_libraries_in_flutter
import 'dart:js' as js;
import 'dart:async';

// Execute JavaScript code and return the result
Future<String> executeJavaScript(String code) async {
  try {
    print('Executing JavaScript code:');
    print(code.substring(0, code.length > 100 ? 100 : code.length) + '...');

    // Execute the JavaScript code
    final result = js.context.callMethod('eval', [code]);

    // Convert the result to a string
    return result?.toString() ?? 'null';
  } catch (e, stackTrace) {
    print('Error executing JavaScript in web: $e');
    print('Stack trace: $stackTrace');

    // Try to get more detailed error information
    try {
      // Check if there's a JavaScript error object
      final jsError = js.context['console']
          .callMethod('error', ['Last JavaScript error:', e]);
      print('JavaScript error details: $jsError');
    } catch (innerError) {
      print('Error getting JavaScript error details: $innerError');
    }

    return 'Error: $e';
  }
}
