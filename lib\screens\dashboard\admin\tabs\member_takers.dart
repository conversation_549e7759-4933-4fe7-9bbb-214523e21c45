// ignore_for_file: must_be_immutable, no_logic_in_create_state, must_call_super, use_build_context_synchronously

import 'dart:async';

import 'package:ekub/models/members.dart';
import 'package:ekub/screens/ui_kits/app_bar.dart';
import 'package:ekub/screens/ui_kits/no_internet_connection_found.dart';
import 'package:ekub/utils/colors.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/device.dart';
import 'package:flutter/material.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../../../models/ekub.dart';
import '../../../../repository/ekub_localization.dart';
import '../../../../repository/equb_repos.dart';
import '../../../../repository/member_repos.dart';
import '../../../ui_kits/internet_connectivity_check.dart';
import '../../../themes/ThemeProvider.dart';
import '../../../ui_kits/list_view.dart';
import '../../../ui_kits/loading_indicator.dart';

class EqubTakersScreen extends StatefulWidget {
  int index;
  bool isFromAutomatic;
  final Member? member;
  bool completed;
  EqubTakersScreen(
      {super.key,
      required this.member,
      required this.completed,
      required this.isFromAutomatic,
      required this.index});
  @override
  State<EqubTakersScreen> createState() => _EqubTakersState();
}

class _EqubTakersState extends State<EqubTakersScreen>
    with AutomaticKeepAliveClientMixin<EqubTakersScreen> {
  @override
  bool get wantKeepAlive => true;
  bool isLoading = true;
  bool isConnected = true;
  Equb? equb;
  List<Equb>? _items = [];
  late ThemeProvider themeProvider;
  final _appBar = GlobalKey<FormState>();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    loadmemberEqubs();
    super.initState();
  }

  void loadmemberEqubs() async {
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        setState(() {
          isConnected = false;
          isLoading = false;
        });
        return;
      }
      widget.completed
          ? await Provider.of<MemberProvider>(context, listen: false)
              .loadCompletedMemberEqub(context, widget.member!.id!.toString())
          : await Provider.of<EqubDataProvider>(context, listen: false)
              .loadMemberEqub(context, 0, 1, widget.member!.id!);
    } catch (e) {
      if (e is TimeoutException) {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("time_out"),
              message:
                  EkubLocalization.of(context)!.translate("timeout_message"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            Navigator.pop(context);
            loadmemberEqubs();
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              message: EkubLocalization.of(context)!
                  .translate("error_load_equbtype"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            loadmemberEqubs();
            Navigator.pop(context);
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
    setState(() {
      isLoading = false;
      isConnected = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    _items = widget.completed
        ? Provider.of<MemberProvider>(context, listen: false)
            .completedEqubs
            .equbs
        : Provider.of<EqubDataProvider>(context, listen: true).equbs.equbs;
    equb = _items![widget.index];
    return Scaffold(
      backgroundColor: ColorProvider.backgroundColor,
      appBar: widget.isFromAutomatic
          ? EkubAppBar(
              key: _appBar,
              title: EkubLocalization.of(context)!.translate("details"),
              widgets: const [],
            )
          : null,
      body: SizedBox(
        height: Device.body(context),
        child: isConnected
            ? isLoading
                ? searchLoading()
                : RefreshIndicator(
                    onRefresh: () async {
                      await Future.delayed(const Duration(seconds: 2));
                      loadmemberEqubs();
                    },
                    key: _refreshIndicatorKey,
                    child: listHolder(equb!.equbTakers!.equbTakers ?? [],
                        themeProvider.getColor),
                  )
            : NoConnectionWidget(
                fun: loadmemberEqubs,
                isLoading: isLoading,
              ),
      ),
    );
  }

  Widget listHolder(items, theme) {
    return items.length > 1
        ? Column(
            children: [
              Expanded(
                child: ListView.builder(
                    //controller: _controller,
                    itemCount: items.length,
                    padding: const EdgeInsets.all(0.0),
                    itemBuilder: (context, index) {
                      return _buildListItems(
                          context, items[index], items, index, theme);
                    }),
              ),
              // when the _loadMore function is running
            ],
          )
        : Container(
            margin: const EdgeInsets.symmetric(vertical: 150),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  alignment: Alignment.center,
                  child: const Image(
                    height: 200,
                    image: AssetImage(
                      "assets/icons/pay.png",
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
                Center(
                    child: Text(
                  EkubLocalization.of(context)!.translate("not_paid"),
                  style: TextStyle(
                      color: bodyTextColor, fontWeight: normalFontWeight),
                )),
              ],
            ),
          );
  }

  Widget _buildListItems(
      BuildContext context, EqubTaker equbTaker, item, int index, theme) {
    return GestureDetector(
      onTap: () {},
      child: _listUi(theme, equbTaker, item, index),
    );
  }

  _listUi(Color theme, EqubTaker equbTaker, item, index) {
    return Padding(
        padding: const EdgeInsets.only(left: 8.0, right: 8.0),
        child: equbTaker.status == "unpaid" || equbTaker.status == "void"
            ? Container()
            : equbTaker.status == "paid"
                ? TakerCard(
                    takerName: equbTaker.memberId ?? "Unavailable",
                    status: equbTaker.status ?? "Unavailable",
                    lotteryAmount: item![0].lotteryAmount ?? "0",
                    icon: Icons.payment,
                    paymentType: equbTaker.paymentType ?? "Unavailable",
                    updatedAt: equbTaker.updatedAt ?? "Unavailable",
                    createdAt: equbTaker.createdAt ?? "Unavailable",
                    theme: themeProvider,
                    payedBy: equbTaker.paidBy ?? "Unavailable",
                    chequeAmount: equbTaker.chequeAmount ?? "0",
                    chequeBankName: equbTaker.chequeBankName,
                    remainingAmount: equbTaker.remainingAmount ?? "0",
                    chequeDescription:
                        equbTaker.chequeDescription ?? "Unavailable",
                  )
                : TakerCard(
                    takerName: equbTaker.memberId ?? "Unavailable",
                    status: equbTaker.status ?? "Unavailable",
                    lotteryAmount: equbTaker.lotteryAmount ?? "0",
                    icon: Icons.payment,
                    paymentType: equbTaker.paymentType ?? "Unavailable",
                    updatedAt: equbTaker.updatedAt ?? "Unavailable",
                    createdAt: equbTaker.createdAt ?? "Unavailable",
                    theme: themeProvider,
                    payedBy: equbTaker.paidBy ?? "Unavailable",
                    chequeAmount: equbTaker.chequeAmount ?? "0",
                    chequeBankName: equbTaker.chequeBankName,
                    remainingAmount: equbTaker.remainingAmount ?? "0",
                    chequeDescription:
                        equbTaker.chequeDescription ?? "Unavailable",
                  ));
  }
}
