package com.vintechplc.virtualequb

import android.util.Log
import android.widget.Toast
import androidx.fragment.app.FragmentActivity
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.huawei.ethiopia.pay.sdk.api.core.listener.PayCallback
import com.huawei.ethiopia.pay.sdk.api.core.data.PayInfo
import com.huawei.ethiopia.pay.sdk.api.core.utils.PaymentManager

class MainActivity : FlutterFragmentActivity() { // Changed to FlutterFragmentActivity
    private val CHANNEL = "telebirr"  // Channel name

    // Declare method channel
    private lateinit var methodChannel: MethodChannel

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Initialize method channel
        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        
        // Set the method call handler
        methodChannel.setMethodCallHandler { call, result ->
            if (call.method == "nativeFunction") {
                val appid = call.argument<String>("appid")
                val merchCode = call.argument<String>("merch_code")
                val prepayId = call.argument<String>("prepay_id")
                val timestamp = call.argument<String>("timestamp")
                val amount = call.argument<String>("amount")
                Log.d("merchCode", merchCode ?: "merchCode is null")

                // Call the function to initiate the Telebirr payment
                initiateTelebirrPayment(result, appid, merchCode, prepayId, timestamp, amount)
            } else {
                result.notImplemented()
            }
        }
    }

    // Method to initiate Telebirr payment
    private fun initiateTelebirrPayment(
        result: MethodChannel.Result, 
        appid: String?, 
        merchCode: String?, 
        prepayId: String?, 
        timestamp: String?, 
        amount: String?
    ) {
        val BUYGOODS = "Virtual Equb"
        val shortCode = merchCode // Replace with actual short code
        val paymentAmount = amount // Replace with the amount
        val prepayId = prepayId // Replace with the actual prepay_id
        val timestamp = timestamp // Replace with actual timestamp

        // Concatenate to form the receiveCode
        val receiveCode = "TELEBIRR$$BUYGOODS$$shortCode$$paymentAmount$$prepayId$$timestamp"
        Log.d("Telebirr", receiveCode)

        // Prepare PayInfo for the payment
        val payInfo = PayInfo.Builder()
            .setAppId(appid)
            .setShortCode(shortCode)
            .setReceiveCode(receiveCode)
            .build()

        // Initiate payment using PaymentManager
        PaymentManager.getInstance().pay(this, payInfo)

        // Set the callback for payment result
        PaymentManager.getInstance().setPayCallback(object : PayCallback {
            override fun onPayCallback(code: Int, errMsg: String?) {
                Log.d("PAYMENT", "onPayCallback: code $code errMsg $errMsg")
                runOnUiThread {
    // Show payment result as a toast message
    Toast.makeText(this@MainActivity, "Payment Result: $code, $errMsg", Toast.LENGTH_SHORT).show()

    // If payment is successful (code == 0), log a success message
    if (code == 0) {
        Log.d("PAYMENT", "Payment successful!")
    }

    // Send navigation message to Flutter
    val page = if (code == 0) "success" else "home_screen"  // Navigate to "success" page on success, else "home_screen"
    methodChannel.invokeMethod("navigateTo", page)  // Use methodChannel to send the navigation command
}
            }
        })

        // Show message to user that the Telebirr app is opening
        Toast.makeText(this, "Opening Telebirr App", Toast.LENGTH_LONG).show()

        // Return the result to Flutter
        result.success(receiveCode)
    }
}