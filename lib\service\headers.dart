import 'package:http/http.dart' as http;
import '../models/result.dart';
import '../repository/user_repos.dart';

class RequestHeader {
  // static const String baseApp = "https://www.virtualequb.com/api";
  //deployment api
  //static const String baseApp = "https://www.virtualekubdash.com/api";
  //testing api
  //static const String baseApp = "https://www.staging.tamiratekub.com/api";
  //testing api2
  static const String baseApp = "https://virtualekubdash.com/api";
  // static const String baseApp = "https://virtualekubdash.com/api";
  //static const String baseApp = "https://virtualekubdash.com/api";
  //static const String baseApp = "https://test2.virtualekubdash.com/api";
  //local api
  //static const String baseApp = "127.0.0.1:8000/api";
  //static const String baseApp = "http://192.168.1.15:8000/api";
  AuthDataProvider authDataProvider =
      AuthDataProvider(httpClient: http.Client());
  Future<Map<String, String>>? authorisedHeader() async => <String, String>{
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await authDataProvider.getToken()}'
      };

  Future<Map<String, String>>? defaultHeader() async => <String, String>{
        'Content-Type': 'application/json',
        'app-key': 'OGV8V1FCa1FXaDVGZw'
      };
}

class RequestResult {
  Result requestResult(String code, String body) {
    if (code == "200") {
      return Result(code, true, body);
    } else {
      return Result(code, false, body);
    }
  }

  // String _prepareResult(code) {
  //   switch (code) {
  //     case Constants.anAuthorizedC:
  //       return Constants.anAuthorizedM;
  //     case Constants.accessForbiddenC:
  //       return Constants.accessForbiddenM;
  //     case Constants.notFoundC:
  //       return Constants.notFoundM;
  //     case Constants.serverErrorC:
  //       return Constants.serverErrorM;
  //     case Constants.requestTimeoutC:
  //       return Constants.requestTimeoutM;
  //     default:
  //       return Constants.unknownErrorM;
  //   }
  // }
}
