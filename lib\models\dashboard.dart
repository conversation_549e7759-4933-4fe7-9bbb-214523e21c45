import 'package:ekub/models/members.dart';

class Dashboard {
  String? title;
  String? lables;
  double? fullPaidAmount;
  double? expected;

  double? daylyPaidAmount;
  double? daylyUnpaidAmount;
  String? daylyExpected;

  String? weeklyPaidAmount;
  double? weeklyUnpaidAmount;
  double? weeklyExpected;

  double? monthlyPaidAmount;
  double? monthlyUnpaidAmount;
  double? monthlyExpected;

  double? yearlyPaidAmount;
  double? yearlyUnpaidAmount;
  double? yearlyExpected;

  int? totalMember;
  Members? tudayPaidMember;
  int? activeMember;
  int? totalUser;
  String? totalEqubPayment;

  Dashboard(
      {this.title,
      this.lables,
      this.fullPaidAmount,
      this.expected,
      this.daylyPaidAmount,
      this.daylyUnpaidAmount,
      this.daylyExpected,
      this.weeklyPaidAmount,
      this.weeklyUnpaidAmount,
      this.weeklyExpected,
      this.monthlyPaidAmount,
      this.monthlyUnpaidAmount,
      this.monthlyExpected,
      this.yearlyPaidAmount,
      this.yearlyUnpaidAmount,
      this.yearlyExpected,
      this.totalMember,
      this.tudayPaidMember,
      this.activeMember,
      this.totalUser,
      this.totalEqubPayment});

  factory Dashboard.fromJson(Map<String, dynamic> json) {
    return Dashboard(
      title: json["title"],
      lables: json["lables"],
      fullPaidAmount: 1000,
      expected: double.parse(json["daylyPaidAmount"].toString()),
      daylyPaidAmount: double.parse(json["daylyPaidAmount"].toString()),
      daylyExpected: json["daylyExpected"].toString(),
      weeklyPaidAmount: json["weeklyPaidAmount"].toString(),
      weeklyExpected: double.parse(json["weeklyExpected"].toString()),
      monthlyPaidAmount: double.parse(json["monthlyPaidAmount"].toString()),
      monthlyExpected: double.parse(json["monthlyExpected"].toString()),
      yearlyPaidAmount: double.parse(json["yearlyPaidAmount"].toString()),
      yearlyExpected: double.parse(json["yearlyExpected"].toString()),
      tudayPaidMember: Members.fromStringObject(json["tudayPaidMember"]),
      activeMember: json['activeMember'],
      totalUser: json["totalUser"],
      totalEqubPayment: json["totalEqubPayment"].toString(),
    );
  }

  @override
  String toString() => 'Dashboard $lables';
}

class DashboardData {
  bool isLoaded;
  Dashboard? dashboard;
  String message;
  DashboardData({required this.isLoaded, this.dashboard, this.message = ""});
}
