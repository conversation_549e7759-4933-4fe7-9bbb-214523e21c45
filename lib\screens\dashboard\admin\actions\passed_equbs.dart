// ignore_for_file: empty_catches, must_be_immutable, use_build_context_synchronously

import 'dart:async';

import 'package:ekub/repository/equb_repos.dart';
import 'package:ekub/repository/member_repos.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/dashboard/admin/tabs/member_tabs.dart';
import 'package:ekub/screens/themes/ThemeProvider.dart';
import 'package:ekub/screens/ui_kits/app_bar.dart';
import 'package:ekub/screens/ui_kits/list_view.dart' hide formatDate;
import 'package:ekub/screens/ui_kits/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../../../models/ekub.dart';
import '../../../../models/members.dart';
import '../../../../models/user.dart';
import '../../../../repository/ekub_localization.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/constants.dart';
import '../../../../utils/device.dart';
import '../../../../utils/tools.dart';
import '../../../ui_kits/internet_connectivity_check.dart';
import '../../../ui_kits/no_internet_connection_found.dart';

class PassedEqubs extends StatefulWidget {
  String memberId;
  PassedEqubs({super.key, required this.memberId});

  @override
  State<PassedEqubs> createState() => _PassedEqubsState();
}

class _PassedEqubsState extends State<PassedEqubs> {
  User user = User(
      phoneNumber: "loading",
      fullName: "loading",
      gender: "loading",
      email: "loading",
      role: "loading",
      memberId: "loading");
  bool isConnected = true;

  bool proLoaded = false;

  bool isLoading = true;
  Member? member;

  List<Equb>? equbs = [];
  final _appBar = GlobalKey<FormState>();

  late ThemeProvider themeProvider;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    loadEqubs();
    _loadProfile();

    super.initState();
  }

  _loadProfile() async {
    var auth = AuthDataProvider(httpClient: http.Client());

    try {
      await auth.getUserData().then((value) => {
            setState(() {
              user = value;
              proLoaded = true;
            })
          });
    } catch (e) {}
  }

  void loadEqubs() async {
    try {
      if (!await InternetConnectivity()
          .checkInternetConnectivty(context, false)) {
        setState(() {
          isConnected = false;
          isLoading = false;
        });
        return;
      }
      setState(() {
        isLoading = true;
      });
      await Provider.of<EqubDataProvider>(context, listen: false)
          .loadPassedMemberEqub(context, 0, 1, int.parse(widget.memberId));
      member = await Provider.of<MemberProvider>(context, listen: false)
          .getMember(context, widget.memberId);

      // Use passedEqubs instead of equbs
      equbs = Provider.of<EqubDataProvider>(context, listen: false)
          .passedEqubs
          .equbs;

      // Print all passed equbs
      equbs?.forEach((equb) {
        print('Passed Equb ID: ${equb.id}, End Date: ${equb.endDate}');
      });

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      if (e is TimeoutException) {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              title: EkubLocalization.of(context)!.translate("time_out"),
              message:
                  EkubLocalization.of(context)!.translate("timeout_message"),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            Navigator.pop(context);
            loadEqubs();
          }, onTapCancel: () {
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      } else {
        if (mounted) {
          PanaraConfirmDialog.show(context,
              message: e.toString(),
              confirmButtonText:
                  EkubLocalization.of(context)!.translate("try_again"),
              cancelButtonText: EkubLocalization.of(context)!
                  .translate("cancel"), onTapConfirm: () {
            if (mounted) {
              loadEqubs();
              Navigator.pop(context);
            }
          }, onTapCancel: () {
            setState(() {
              isLoading = false;
            });
            Navigator.pop(context);
          }, panaraDialogType: PanaraDialogType.error);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorProvider.backgroundColor,
        appBar: EkubAppBar(
            key: _appBar,
            title: EkubLocalization.of(context)!.translate("passed_equb"),
            widgets: [
              IconButton(
                  onPressed: () {
                    loadEqubs();
                  },
                  icon: const Icon(Icons.refresh))
            ]),
        body: isConnected
            ? isLoading
                ? searchLoading()
                : RefreshIndicator(
                    onRefresh: () async {
                      await Future.delayed(const Duration(seconds: 2));
                      loadEqubs();
                    },
                    key: _refreshIndicatorKey,
                    child: Column(
                      children: [
                        Expanded(
                          child: SizedBox(
                              height: Device.body(context),
                              child:
                                  listHolder(equbs!, themeProvider.getColor)),
                        ),
                      ],
                    ),
                  )
            : NoConnectionWidget(
                fun: loadEqubs,
                isLoading: isLoading,
              ));
  }

  Widget listHolder(List<Equb> items, Color theme) {
    return items.isNotEmpty && !isLoading
        ? ListView.builder(
            itemCount: items.length,
            padding:
                const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  print("Equb ID: ${items[index].id}");
                  print(
                      "Equb Type Name: ${items[index].equbType?.name ?? 'Unknown'}");
                  print("Is Online: true");
                  print("User Role: ${user.role}");
                  print("Index: $index");
                  print("Member ID: ${user.id}");
                  print("Member: ${member.toString()}");

                  MemberETabsArgs arg = MemberETabsArgs(
                      equb: items[index],
                      isOnline: true,
                      role: user.role,
                      index: index,
                      memberId: user.id,
                      member: member!,
                      completed: true);
                  Navigator.pushNamed(context, MemberTabs.routeName,
                      arguments: arg);
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: EqubCard(
                    status: items[index].status ?? "loading...",
                    round: items[index].equbType!.round ?? "loading...",
                    amount: items[index].amount ?? "loading...",
                    icon: Icons.person,
                    equbType: items[index].equbType?.name ?? "loading...",
                    totalAmount: items[index].totalAmount ?? "loading...",
                    startDate: items[index].startDate ?? "loading...",
                    endDate: items[index].endDate ?? "loading...",
                    lotteryDate: items[index].lotteryDate ?? "loading...",
                    totalPayment: items[index].totalPayment ?? "loading...",
                    remainingPayment:
                        items[index].remainingPayment ?? "loading...",
                    remainingLotteryDate:
                        items[index].remainingLotteryDate ?? "loading...",
                    theme: themeProvider,
                  ),
                ),
              );
            })
        : Center(
            child: Text(
            EkubLocalization.of(context)!.translate("no_passed_equb"),
            style:
                TextStyle(color: bodyTextColor, fontWeight: normalFontWeight),
          ));
  }

  Widget _buildListItems(
      BuildContext context, Equb equb, int index, Color theme) {
    return GestureDetector(
      onTap: () {
        MemberETabsArgs arg = MemberETabsArgs(
            equb: equb,
            isOnline: true,
            role: user.role,
            index: index,
            memberId: user.id,
            member: member!,
            completed: true);
        Navigator.pushNamed(context, MemberTabs.routeName, arguments: arg);
      },
      child: _listUi(theme, equb),
    );
  }

  Widget _listUi(Color theme, Equb equb) {
    var language = EkubLocalization.of(context)!;

    return Container(
      margin: const EdgeInsets.only(top: 10, left: 15, right: 15),
      padding: const EdgeInsets.only(top: 0, bottom: 15, right: 10, left: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5), //color of shadow
            spreadRadius: .1, //spread radius
            blurRadius: 2, // blur radius
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    Text(
                      equb.equbType!.name ?? "",
                      style: const TextStyle(
                          overflow: TextOverflow.ellipsis,
                          color: Colors.black,
                          fontSize: fontMedium,
                          fontWeight: boldFont),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      language.translate("round"),
                      style: TextStyle(
                          overflow: TextOverflow.ellipsis,
                          color: bodyTextColor,
                          fontSize: fontSmall,
                          fontWeight: normalFontWeight),
                    ),
                    Container(
                      alignment: Alignment.center,
                      margin: const EdgeInsets.symmetric(
                          horizontal: 5, vertical: 1),
                      height: 20,
                      width: 25,
                      decoration: BoxDecoration(
                          color: ColorProvider.backgroundColor,
                          shape: BoxShape.circle),
                      child: Text(
                        equb.equbType!.round ?? "",
                        style: TextStyle(
                            overflow: TextOverflow.ellipsis,
                            color: themeProvider.getColor,
                            fontSize: fontSmall,
                            fontWeight: normalFontWeight),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Container(
                      alignment: Alignment.center,
                      height: 20,
                      width: 60,
                      decoration: BoxDecoration(
                          color: themeProvider.getLightColor.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(20)),
                      child: Text(
                        equb.equbType!.status ?? "",
                        style: TextStyle(
                            color: themeProvider.getColor,
                            fontSize: fontSmall,
                            fontWeight: boldFont),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const Divider(
              height: 3,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(language.translate("amount"), style: lableStyle),
                      const SizedBox(
                        height: 5,
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 5, vertical: 3),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: ColorProvider.backgroundColor),
                        child: Text(
                          equb.amount!,
                          style: TextStyle(
                            color: themeProvider.getColor,
                            fontWeight: boldFont,
                            fontSize: fontSmall,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: .0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(language.translate("payment"),
                                    style: lableStyle),
                                const SizedBox(
                                  height: 5,
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 5, vertical: 3),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: ColorProvider.backgroundColor),
                                  child: Text(
                                    equb.totalAmount!,
                                    style: TextStyle(
                                      overflow: TextOverflow.ellipsis,
                                      color: themeProvider.getColor,
                                      fontWeight: boldFont,
                                      fontSize: fontSmall,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(" ${language.translate("started")}",
                          style: lableStyle),
                      const SizedBox(
                        height: 5,
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 5, vertical: 3),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: ColorProvider.backgroundColor),
                        child: Text(
                          formatDate(equb.startDate!),
                          style: TextStyle(
                            color: themeProvider.getColor,
                            fontWeight: boldFont,
                            fontSize: fontSmall,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: .0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(language.translate("to"),
                                    style: lableStyle),
                                const SizedBox(
                                  height: 5,
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 5, vertical: 3),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: ColorProvider.backgroundColor),
                                  child: Text(
                                    formatDate(equb.endDate!),
                                    style: TextStyle(
                                      overflow: TextOverflow.ellipsis,
                                      color: themeProvider.getColor,
                                      fontWeight: boldFont,
                                      fontSize: fontSmall,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
