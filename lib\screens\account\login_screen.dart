// ignore_for_file: prefer_final_fields, use_build_context_synchronously

import 'dart:async';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:ekub/routs/shared.dart';
import 'package:ekub/screens/settings/constant.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
// // import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:provider/provider.dart';

import '../../exports/screens.dart';
import '../../repository/ekub_localization.dart';
import '../../repository/language.dart';
import '../../repository/user_repos.dart';
import '../../utils/network.dart';
import '../themes/ThemeProvider.dart';

class LoginScreen extends StatefulWidget {
  static const routeName = "/login";

  const LoginScreen({super.key, required LoginScreenArgs args});
  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool isOffline = false;
  bool _isLocked = false;
  var invisible = true;
  bool isSubmitted = false;
  int loginAttempts = 0;
  late Timer _lockTimer;
  late bool _onProcess;
  List<String> languages = ["English", "አማርኛ", "Oromic", "ትግሪኛ", "Somaali"];
  String selectedLanguage = "Language";
  String language = "";
  late ThemeProvider themeProvider;
  final Map<String, dynamic> _doctor = {};
  final _loginFormKey = GlobalKey<FormState>();
  final passwordControl = TextEditingController();
  final emailControl = TextEditingController();
  late StreamSubscription _connectionChangeStream;
  final dropdownControl = TextEditingController();
  // AppLanguage appLanguage = AppLanguage(); // Removed
  // FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  // Future<String> getToken() async {
  //   final token = await _firebaseMessaging.getToken();
  //   return token.toString();
  // }

  void changeSate() {
    if (invisible) {
      setState(() {
        invisible = false;
      });
    } else {
      setState(() {
        invisible = true;
      });
    }
  }

  @override
  void initState() {
    _onProcess = false;
    NetworkManager connectionStatus = NetworkManager.getInstance();
    _connectionChangeStream =
        connectionStatus.connectionChange.listen(connectionChanged);
    themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    getAttempt();
    super.initState();
  }

  void connectionChanged(dynamic hasConnection) {
    setState(() {
      isOffline = !hasConnection;
    });
  }

  @override
  dispose() {
    _connectionChangeStream.cancel();
    _onProcess = false;
    super.dispose();
  }

  String? attempts = "0";
  getAttempt() async {
    var sender = AuthDataProvider(httpClient: http.Client());
    attempts = await sender.getloginAttempts();
    setState(() {
      loginAttempts = int.parse(attempts ?? "0");
    });

    // Removed AppLanguage fetch and language setting logic
    // await appLanguage.fetchLocale();
    // language = appLanguage.appLocal.languageCode.toLowerCase();

    // Removed language setting based on appLanguage
    // if (language == "fr") {
    //   setState(() {
    //     selectedLanguage = "Oromic";
    //   });
    // }
    // if (language == "es") {
    //   setState(() {
    //     selectedLanguage = "ትግሪኛ";
    //   });
    // }
    // if (language == "am") {
    //   setState(() {
    //     selectedLanguage = "አማርኛ";
    //   });
    // } else if (language == "tl") {
    //   setState(() {
    //     selectedLanguage = "Somaali";
    //   });
    // } else if (language == "en") {
    //   setState(() {
    //     selectedLanguage = "English";
    //   });
    // }
  }

  @override
  Widget build(BuildContext context) {
    // Removed AppLanguage provider access
    var appLanguage = Provider.of<AppLanguage>(context);
    var language = EkubLocalization.of(context)!;

    return Scaffold(
        appBar: AppBar(backgroundColor: Colors.white, elevation: 0, actions: [
          // Language Settings Button
          PopupMenuButton<String>(
            icon: Icon(
              Icons.language,
              color: themeProvider.getColor,
              size: 25,
            ),
            tooltip: 'Language Settings',
            onSelected: (value) {
              // if (value == 'test') {
              //   Navigator.pushNamed(context, '/language_test');
              // } else if (value == 'select') {
              //   Navigator.pushNamed(context, '/language_selection');
              // }
              setState(() {
                selectedLanguage = value;
                if (selectedLanguage == "English") {
                  appLanguage.changeLanguage(const Locale("en"));
                } else if (selectedLanguage == "አማርኛ") {
                  appLanguage.changeLanguage(const Locale("am"));
                } else if (selectedLanguage == "Oromic") {
                  appLanguage.changeLanguage(const Locale("fr"));
                } else if (selectedLanguage == "Somaali") {
                  appLanguage.changeLanguage(const Locale("tl"));
                } else if (selectedLanguage == "ትግሪኛ") {
                  appLanguage.changeLanguage(const Locale("es"));
                }
              });
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'English',
                child: Text('English'),
              ),
              const PopupMenuItem<String>(
                value: 'አማርኛ',
                child: Text('አማርኛ'),
              ),
              const PopupMenuItem<String>(
                value: 'ትግሪኛ',
                child: Text('ትግሪኛ'),
              ),
              const PopupMenuItem<String>(
                value: 'Oromic',
                child: Text('Oromic'),
              ),
              const PopupMenuItem<String>(
                value: 'Somaali',
                child: Text('Somaali'),
              ),
            ],
          ),
        ]),
        body: SingleChildScrollView(
          child: SafeArea(
            child: Form(
              key: _loginFormKey,
              autovalidateMode: isSubmitted
                  ? AutovalidateMode.onUserInteraction
                  : AutovalidateMode.disabled,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    alignment: Alignment.center,
                    // height: 140,
                    // color: Colors.red,
                    margin: const EdgeInsets.only(
                      top: 20,
                    ),
                    child: const Image(
                      image: AssetImage(
                        'assets/icons/icon.png',
                      ),
                      height: 100,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Company Name
                  Container(
                    margin: const EdgeInsets.fromLTRB(20, 5, 20, 0),
                    padding: const EdgeInsets.fromLTRB(20, 5, 20, 0),
                    child: Center(
                      child: Text(
                        "Virtual Equb",
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: themeProvider.getColor,
                        ),
                      ),
                    ),
                  ),

                  // Tagline
                  Container(
                    margin: const EdgeInsets.fromLTRB(20, 5, 20, 0),
                    padding: const EdgeInsets.fromLTRB(20, 5, 20, 0),
                    child: Center(
                      child: Text(
                        "Make Your Dreams a Reality with Virtual Equb",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: bodyTextColor,
                          height: 1.5,
                        ),
                      ),
                    ),
                  ),

                  Container(
                    margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                    padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                    child: Text(
                      language.translate("login"),
                      style: const TextStyle(
                        fontSize: 30,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                    padding: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                    child: Text(
                      language.translate("login_subtitle"),
                      style: TextStyle(
                        fontSize: fontMedium,
                        color: Colors.grey.shade400,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ),
                  // const SizedBox(
                  //   height: 10,
                  // ),
                  _align(language)
                ],
              ),
            ),
          ),
        ));
  }

  Align _align(EkubLocalization language) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
              margin: const EdgeInsets.fromLTRB(20, 20, 20, 10),
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
              child: _emailAndPassword(language)),
          Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10)),
            margin: const EdgeInsets.fromLTRB(20, 30, 20, 10),
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 10,
                ),
                _startLoginProcess(language),
                const SizedBox(
                  height: 10,
                ),
                _newUser(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Column _emailAndPassword(EkubLocalization language) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            language.translate("phone_number"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        TextFormField(
          maxLength: 9,
          controller: emailControl,
          keyboardType: TextInputType.phone,
          style: lableStyle,
          decoration: InputDecoration(
              border: const OutlineInputBorder(
                  borderSide: BorderSide(style: BorderStyle.solid)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide:
                    BorderSide(color: themeProvider.getColor.withOpacity(0.3)),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              fillColor: Colors.white,
              filled: true,
              counterText: "",
              floatingLabelBehavior: FloatingLabelBehavior.never,
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.phone_outlined,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              prefix: const Text(
                "+251 ",
              ),
              labelText: '+2519-09-89-89-21',
              labelStyle: hintStyle),
          validator: (value) {
            if (value!.isEmpty) {
              return language.translate("phone_empty_member");
            } else if (value.length < 9) {
              return language.translate("phone_length");
            }
            return null;
          },
          onSaved: (value) {
            _doctor["phone_number"] = "+251${value!}";
          },
        ),
        Padding(
          padding: const EdgeInsets.only(left: 8.0, top: 20),
          child: Text(
            language.translate("password"),
            style: TextStyle(
                color: themeProvider.getColor,
                fontWeight: FontWeight.bold,
                fontSize: fontMedium),
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 0),
          child: TextFormField(
            style: lableStyle,
            controller: passwordControl,
            obscureText: invisible,
            decoration: InputDecoration(
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.red,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              border: const OutlineInputBorder(
                  borderSide: BorderSide(style: BorderStyle.solid)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide:
                    BorderSide(color: themeProvider.getColor.withOpacity(0.3)),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
                borderSide: BorderSide(color: themeProvider.getColor),
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 25,
                width: 30,
                decoration: BoxDecoration(
                    color: themeProvider.getColor.withOpacity(0.1),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.lock_outline,
                  size: 20,
                  color: themeProvider.getColor,
                ),
              ),
              labelText: "********",
              labelStyle: hintStyle,
              fillColor: Colors.white,
              filled: true,
              floatingLabelBehavior: FloatingLabelBehavior.never,
              suffix: GestureDetector(
                onTap: _onProcess ? null : changeSate,
                child: Icon(
                  invisible ? Icons.visibility : Icons.visibility_off,
                  color: themeProvider.getColor,
                  size: 15,
                ),
              ),
            ),
            validator: (value) {
              if (value!.isEmpty) {
                return EkubLocalization.of(context)!
                    .translate("password_empty");
              } else if (value.length < 6) {
                return EkubLocalization.of(context)!
                    .translate("password_length");
              } else if (value.length > 25) {
                return EkubLocalization.of(context)!
                    .translate("password_limit");
              } else {
                return null;
              }
            },
            onSaved: (value) {
              _doctor["password"] = value;
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            InkWell(
              borderRadius: BorderRadius.circular(20),
              splashColor: Colors.amber,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PhoneVerification(
                      status: "forgotPassword",
                    ),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 5),
                child: Text(
                  "${language.translate("forget_password")} ?",
                  style: TextStyle(
                      color: themeProvider.getColor,
                      fontWeight: boldFont,
                      fontSize: fontMedium),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  _startLoginProcess(EkubLocalization language) {
    return ElevatedButton(
      onPressed: _onProcess
          ? null
          : () async {
              setState(() {
                isSubmitted = true;
              });
              final form = _loginFormKey.currentState;
              if (form!.validate()) {
                form.save();
                if (!await InternetConnectivity()
                    .checkInternetConnectivty(context, true)) {
                  setState(() {
                    _onProcess = false;
                  });
                  return;
                } else {
                  _loginUser(context);
                }
              }
            },
      child: SizedBox(
        height: 50,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            Text(EkubLocalization.of(context)!.translate("sign_in"),
                style: buttonText),
            const Spacer(),
            Align(
              widthFactor: 2,
              alignment: Alignment.centerRight,
              child: _onProcess
                  ? const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                    )
                  : Container(),
            ),
          ],
        ),
      ),
    );
  }

  _newUser() {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: SizedBox(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                "${EkubLocalization.of(context)!.translate("don't_have_account")}? ",
                style: TextStyle(
                    fontSize: fontMedium,
                    color: bodyTextColor,
                    fontWeight: normalFontWeight),
              ),
              InkWell(
                borderRadius: BorderRadius.circular(20),
                splashColor: Colors.amber,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          PhoneVerification(status: "newUser"),
                    ),
                  );
                },
                child: Text(
                  EkubLocalization.of(context)!.translate("sign_up"),
                  style: TextStyle(
                      color: themeProvider.getLightColor,
                      fontWeight: boldFont,
                      fontSize: fontMedium),
                ),
              ),
            ],
          ),
        ));
  }

  _loginUser(BuildContext context) {
    if (!isOffline) {
      prepareRequest(context, loginAttempts);
    } else {}
  }

  void prepareRequest(BuildContext context, int loginAttempts) async {
    if (_isLocked) {
      PanaraInfoDialog.show(
        context,
        title: EkubLocalization.of(context)!.translate("blocked"),
        message: EkubLocalization.of(context)!.translate("blocked_msg"),
        buttonText: EkubLocalization.of(context)!.translate("okay"),
        onTapDismiss: () {
          Navigator.pop(context);
        },
        imagePath: warningDialogIcon,
        panaraDialogType: PanaraDialogType.warning,
      );
    } else {
      var sender = AuthDataProvider(httpClient: http.Client());

      setState(() {
        _onProcess = true;
      });
      // _doctor["fcm_id"] = await getToken();
      var res = sender.loginUser(_doctor, loginAttempts, context);
      res
          .then((value) => {
                setState(() {
                  _onProcess = false;
                }),
                if (value.code == "200")
                  {
                    setState(() {
                      sender.setloginAttempts("0");
                    }),
                    if (value.message == "admin")
                      {_openHomeScreen(value.message == "admin", value.message)}
                    else
                      {_openHomeScreen(value.message == "admin", value.message)}
                  }
                else if (value.code == "401")
                  {_checkLogin(value.message)}
                else
                  {
                    PanaraInfoDialog.show(
                      context,
                      title: EkubLocalization.of(context)!.translate("error"),
                      message: value.message,
                      buttonText:
                          EkubLocalization.of(context)!.translate("okay"),
                      onTapDismiss: () {
                        Navigator.pop(context);
                      },
                      imagePath: errorDialogIcon,
                      panaraDialogType: PanaraDialogType.error,
                    ),
                    setState(() {
                      _onProcess = false;
                    })
                  }
              })
          .onError((error, stackTrace) {
        PanaraConfirmDialog.show(context,
            message: error.toString(),
            confirmButtonText:
                EkubLocalization.of(context)!.translate("try_again"),
            cancelButtonText: EkubLocalization.of(context)!.translate("cancel"),
            onTapConfirm: () {
          Navigator.pop(context);
          prepareRequest(context, loginAttempts);
        }, onTapCancel: () {
          Navigator.pop(context);
        }, panaraDialogType: PanaraDialogType.error);
        setState(() {
          _onProcess = false;
        });
        return {};
      });
    }
  }

  _openHomeScreen(bool isAdmin, String role) async {
    HomeScreenArgs argument =
        HomeScreenArgs(isOnline: true, isAdmin: isAdmin, role: role);

    Navigator.pushNamedAndRemoveUntil(
        context, HomeScreen.routeName, (Route<dynamic> route) => false,
        arguments: argument);
  }

  _checkLogin(String message) {
    var sender = AuthDataProvider(httpClient: http.Client());

    setState(() {
      loginAttempts++;

      sender.setloginAttempts(loginAttempts.toString());
    });
    if (loginAttempts > 5) {
      _lockAccount();
    } else {
      PanaraInfoDialog.show(
        context,
        message: message,
        buttonText: EkubLocalization.of(context)!.translate("okay"),
        onTapDismiss: () {
          Navigator.pop(context);
        },
        imagePath: errorDialogIcon,
        panaraDialogType: PanaraDialogType.error,
      );
    }
  }

  void _lockAccount() {
    _isLocked = true;
    PanaraInfoDialog.show(
      context,
      title: "${EkubLocalization.of(context)!.translate("blocked")} !",
      message: EkubLocalization.of(context)!.translate("blocked_msg"),
      buttonText: EkubLocalization.of(context)!.translate("okay"),
      onTapDismiss: () {
        Navigator.pop(context);
      },
      imagePath: warningDialogIcon,
      panaraDialogType: PanaraDialogType.warning,
    );
    _lockTimer = Timer(const Duration(minutes: 5), () {
      _unlockAccount();
    });
  }

  void _unlockAccount() {
    _isLocked = false;
    _lockTimer.cancel();
    var sender = AuthDataProvider(httpClient: http.Client());
    setState(() {
      loginAttempts = 0;
      sender.setloginAttempts("0");
    });
    PanaraInfoDialog.show(
      context,
      title: EkubLocalization.of(context)!.translate("unlocked"),
      message: EkubLocalization.of(context)!.translate("unlocked_msg"),
      buttonText: EkubLocalization.of(context)!.translate("okay"),
      onTapDismiss: () {
        Navigator.pop(context);
      },
      panaraDialogType: PanaraDialogType.normal,
    );
  }
}
