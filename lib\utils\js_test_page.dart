import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'js_interop.dart';

class JSTestPage extends StatefulWidget {
  const JSTestPage({Key? key}) : super(key: key);

  @override
  State<JSTestPage> createState() => _JSTestPageState();
}

class _JSTestPageState extends State<JSTestPage> {
  final TextEditingController _codeController = TextEditingController();
  String _result = '';
  bool _isExecuting = false;

  @override
  void initState() {
    super.initState();
    // Set default JavaScript code
    _codeController.text = '''
// Check if window.consumerapp exists
if (window.consumerapp) {
  console.log('window.consumerapp exists:', window.consumerapp);

  // Check if window.consumerapp.evaluate is a function
  if (typeof window.consumerapp.evaluate === 'function') {
    console.log('window.consumerapp.evaluate is a function');
  } else {
    console.log('window.consumerapp.evaluate is NOT a function, it is:', typeof window.consumerapp.evaluate);
  }
} else {
  console.log('window.consumerapp does NOT exist');

  // Define window.consumerapp for testing
  console.log('Defining window.consumerapp for testing...');
  window.consumerapp = {
    evaluate: function(obj) {
      console.log('consumerapp.evaluate called with:', obj);
      return true;
    }
  };

  console.log('window.consumerapp defined:', window.consumerapp);
}

// Return a summary of the check
return {
  consumerappExists: window.consumerapp !== undefined,
  evaluateExists: window.consumerapp !== undefined && typeof window.consumerapp.evaluate === 'function'
};
''';
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  Future<void> _executeJavaScript() async {
    setState(() {
      _isExecuting = true;
      _result = 'Executing JavaScript...';
    });

    try {
      if (!kIsWeb) {
        // Show a warning if not running on web
        setState(() {
          _result =
              'JavaScript execution is only supported on web platforms.\n\n'
              'Your code would be executed on web:\n\n${_codeController.text}';
          _isExecuting = false;
        });
        return;
      }

      final result = await executeJavaScript(_codeController.text);
      setState(() {
        _result = result;
        _isExecuting = false;
      });
    } catch (e) {
      setState(() {
        _result = 'Error: $e';
        _isExecuting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('JavaScript Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Enter JavaScript code to execute:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: TextField(
                controller: _codeController,
                maxLines: null,
                expands: true,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'Enter JavaScript code here',
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isExecuting ? null : _executeJavaScript,
              child: _isExecuting
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('Executing...'),
                      ],
                    )
                  : const Text('Execute JavaScript'),
            ),
            const SizedBox(height: 16),
            const Text(
              'Result:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: SingleChildScrollView(
                  child: Text(_result),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
