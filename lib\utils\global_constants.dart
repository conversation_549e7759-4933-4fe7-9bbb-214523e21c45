final List<String> cities = [
  "Aboms<PERSON>",
  "Adama",
  "Addis Ababa",
  "Addis Zemen",
  "Adet",
  "Adigrat",
  "Agaro",
  "Ä€reka",
  "Arba Minch",
  "Asaita",
  "Assbe Tefera",
  "Assosa",
  "Assosa",
  "Axum",
  "Bahir Dar",
  "Bako",
  "Bata",
  "Bedele",
  "Bedesa",
  "Bichena",
  "Bishoftu",
  "Boditi",
  "Bonga",
  "Bure",
  "Butajira",
  "Debark",
  "Debre Birhan",
  "Debre Markos",
  "Debre Tabor",
  "Dessie",
  "Dilla",
  "Dire Dawa",
  "Dodola",
  "Dubti",
  "Felege Neway",
  "Fiche",
  "Finote Selam",
  "Gambela",
  "Gebre Guracha",
  "Gelemso",
  "Genet",
  "Gimbi",
  "Ginir",
  "Goba",
  "Gondar",
  "Golwayn",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>am",
  "Hara<PERSON>",
  "<PERSON>saain<PERSON>",
  "Inda Silase",
  "Jijiga",
  "Jimma",
  "Jin<PERSON>",
  "Kahandhale",
  "Kemise",
  "Kibre Mengist",
  "Korem",
  "Lasoano",
  "Maychew",
  "Mek'el<PERSON>",
  "Metah<PERSON>",
  "<PERSON><PERSON>",
  "Mojo",
  "Nazret",
  "N<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "N<PERSON><PERSON>",
  "Qoro<PERSON>",
  "Raq<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "Se<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>ji",
  "<PERSON>rot<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>marug<PERSON>",
  "Yirga Alem",
  "Ziway",
  "Waal",
  "Fadhigaradle",
  "Gedo",
  "Digih Habar Es"
];

final List<String> subCities = [
  'Addis Ketema',
  'Akaky Kaliti',
  'Arada',
  'Bole',
  'Gullele',
  'Kirkos',
  'Kolfe Keranio',
  'Lideta',
  'Lemi Kura',
  'Nifas Silk-Lafto',
  'Yeka',
];
