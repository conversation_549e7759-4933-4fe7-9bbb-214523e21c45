// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:ekub/models/payment_transaction.dart';
import 'package:ekub/models/telebirr_response.dart';
import 'package:ekub/repository/equb_repos.dart';
import 'package:ekub/repository/user_repos.dart';
import 'package:ekub/service/headers.dart';
import 'package:ekub/utils/constants.dart';
import 'package:ekub/utils/session.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

import '../exports/models.dart';
import '../screens/dashboard/root/root_screen.dart';
import 'ekub_localization.dart';
import 'package:flutter/services.dart';

class MemberProvider with ChangeNotifier {
  final _baseUrl = RequestHeader.baseApp;
  final http.Client httpClient;
  final secureStorage = const FlutterSecureStorage();
  AuthDataProvider authDataProvider =
      AuthDataProvider(httpClient: http.Client());
  MemberProvider({required this.httpClient});
  List<Member> _member = [];
  List<Member> searchResult = [];
  Map get member => {
        "members": _member,
      };

  Future<void> loadMembers(BuildContext context, int page, int offset) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/member'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        final List maps = jsonDecode(response.body)['members'];
        _member = List<Member>.from(maps.map((data) => Member.fromJson(data)));
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
      notifyListeners();
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<void> searchMember(
      BuildContext context, int page, int offset, String searchInput) async {
    try {
      final response = await http
          .get(
            Uri.parse(
                '$_baseUrl/member/search-member/$searchInput/$offset/$page'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        final List maps = jsonDecode(response.body)['members'];
        searchResult =
            List<Member>.from(maps.map((data) => Member.fromJson(data)));
        notifyListeners();
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }

      notifyListeners();
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Uint8List? imageData;
  Uint8List? get image => imageData;
  Future getProfilePicture(BuildContext context, String memberId) async {
    try {
      if (memberId.isEmpty) {
        debugPrint("Warning: Empty member ID provided to getProfilePicture");
        // Return a default image from assets instead of throwing an error
        final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
        return bytes.buffer.asUint8List();
      }

      final response = await http
          .get(
            Uri.parse('$_baseUrl/member/$memberId/profile-picture'),
            headers: await RequestHeader().defaultHeader(),
          )
          .timeout(const Duration(minutes: 2));

      if (response.statusCode == 200) {
        imageData = response.bodyBytes;
        notifyListeners();
        return imageData;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        // Return a default image for other error codes
        final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
        return bytes.buffer.asUint8List();
      }
    } on SocketException {
      // Handle connection error
      debugPrint(
          'Connection error: Verify your internet connection and try again.');
      final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
      return bytes.buffer.asUint8List();
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      debugPrint('HTTP error: $e');
      final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
      return bytes.buffer.asUint8List();
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      debugPrint('Format error: $e');
      final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
      return bytes.buffer.asUint8List();
    } catch (e) {
      debugPrint("Error loading profile picture: $e");
      // Return a default image instead of throwing an error
      final ByteData bytes = await rootBundle.load('assets/icons/user.jpg');
      return bytes.buffer.asUint8List();
    }
  }

  Future loadMoreMembers(
      BuildContext context, int page, int offset, bool loadmore) async {
    try {
      print("$_baseUrl/member/loadMoreMember/$offset/$page");
      final response = await http
          .get(
            Uri.parse('$_baseUrl/member/loadMoreMember/$offset/$page'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        final List maps = jsonDecode(response.body)['members'];
        List<Member> item =
            List<Member>.from(maps.map((data) => Member.fromJson(data)));
        // if (!loadmore) {
        //   _member = [];
        // }

        if (item.isNotEmpty) {
          loadmore ? _member.addAll(item) : _member = item;
        }
        notifyListeners();
        // return _member;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }

      notifyListeners();
      return _member;
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<Result> signupMember(
    BuildContext context,
    String name,
    String phoneNumber,
    String email,
    String gender,
    Map<String, dynamic> user,
    String password,
  ) async {
    print('SignupMember Data:');
    print('Name: $name');
    print('Phone Number: $phoneNumber');
    print('Email: $email');
    print('Gender: $gender');
    print('User Data: $user');
    print('Password: $password');
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/member/registerMember'),
            headers: await RequestHeader().defaultHeader(),
            body: json.encode({
              "full_name": name,
              "phone": phoneNumber,
              "gender": gender,
              "city": "unknown",
              "woreda": "unknown",
              "housenumber": "unknown",
              "location": "unknown",
              "password": password,
              "email": email,
              "date_of_birth": user['date_of_birth'],
            }),
          )
          .timeout(timeout);
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');
      if (response.statusCode == 200) {
        return Result(response.statusCode.toString(), true,
            jsonDecode(response.body)['message']);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return Result(response.statusCode.toString(), false,
            jsonDecode(response.body)['message']);
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<Result> autoSignupMember(
    BuildContext context,
    String name,
    String phoneNumber,
    String email,
    String gender,
    String dateOfBirth,
    String? city,
    String woreda,
    String housenumber,
    String location,
    Map<String, dynamic> user,
  ) async {
    print('AutoSignupMember Data:');
    print('Name: $name');
    print('Phone Number: $phoneNumber');
    print('Email: $email');
    print('Gender: $gender');
    print('User Data: $user');
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/telebirr-miniapp/register-member'),
            headers: await RequestHeader().defaultHeader(),
            body: json.encode({
              "full_name": name,
              "phone": phoneNumber,
              "gender": gender,
              "city": city,
              "woreda": woreda,
              "housenumber": housenumber,
              "location": location,
              "email": email,
              "date_of_birth": user['date_of_birth'],
            }),
          )
          .timeout(timeout);
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');
      Map<String, dynamic> output = jsonDecode(response.body);
      if (response.statusCode == 200 &&
          output['code'] == 200 &&
          output.containsKey('user')) {
        await secureStorage.write(
            key: 'id', value: output['user']['id'].toString());
        await secureStorage.write(
            key: 'phone_number', value: output['user']['phone_number']);
        await secureStorage.write(
            key: 'full_name', value: output['user']['name']);
        await secureStorage.write(key: 'token', value: output['token']);

        await secureStorage.write(
            key: "email", value: output['user']['email'] ?? "");
        await secureStorage.write(
            key: "role", value: output['user']['role'] ?? "none");

        await secureStorage.write(
            key: "gender", value: output["user"]['gender'] ?? "");
        await secureStorage.write(
            key: "member_id", value: output["user"]['member_id'].toString());
        return Result(
            response.statusCode.toString(), true, output['user']['role']);
      } else {
        var responseStatusCode = response.statusCode.toString();
        if (responseStatusCode == "200") {
          responseStatusCode = "400";
        }
        return Result(
            responseStatusCode, true, jsonDecode(response.body)['message']);
        // return Result(response.statusCode.toString(), false,
        //     jsonDecode(response.body)['message']);
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future updateMember(
      BuildContext context,
      String name,
      String phoneNumber,
      String email,
      String gender,
      String id,
      String woreda,
      String city,
      String location,
      String houseNumber,
      String subcity,
      File? profileImage) async {
    Dio dio = Dio();
    dio.options.headers = {
      "Authorization": "Bearer ${await authDataProvider.getToken()}",
      "Content-Type": "application/json",
    };

    FormData formData = FormData.fromMap({
      "full_name": name,
      "phone": phoneNumber,
      "gender": gender,
      "city": city,
      "subcity": subcity,
      "woreda": woreda,
      "housenumber": houseNumber,
      "location": location,
      "email": email,
      "profile_picture": profileImage == null
          ? null
          : await MultipartFile.fromFile(profileImage.path,
              contentType: MediaType("image", "*")),
    });

    try {
      Response response = await dio
          .post(
            '$_baseUrl/member/updateProfile/$id',
            data: formData,
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        Provider.of<MemberProvider>(context, listen: false)
            .getMember(context, id);

        return response.data;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return response.data;
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future editMember(
    BuildContext context,
    String name,
    String phoneNumber,
    String email,
    String gender,
    String id,
    String woreda,
    String city,
    String location,
    String houseNumber,
    String subcity,
  ) async {
    try {
      final response = await http
          .put(
            Uri.parse('$_baseUrl/member/update/$id'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              "full_name": name,
              "phone": phoneNumber,
              "gender": gender,
              "update_city": city,
              "update_subcity": subcity,
              "update_woreda": woreda,
              "update_housenumber": houseNumber,
              "update_location": location,
              "email": email,
            }),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        Provider.of<MemberProvider>(context, listen: false)
            .loadMembers(context, 0, 1);

        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return jsonDecode(response.body);
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future addMember(
    BuildContext context,
    String fullName,
    String phone,
    var email,
    String gender,
    String city,
    String location,
    var subCity,
    var woreda,
    var houseNumber,
  ) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/member/register'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              "full_name": fullName,
              "phone": '+251$phone',
              "gender": gender,
              "city": city,
              "location": location,
              "email": email,
              "woreda": woreda,
              "subcity": subCity,
              "housenumber": houseNumber,
            }),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        Provider.of<MemberProvider>(context, listen: false)
            .loadMembers(context, 0, 1);

        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return jsonDecode(response.body);
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future savePaymentAdmin(BuildContext context, String type, String amount,
      String credit, String equbId, String memberId, File? proof) async {
    Dio dio = Dio();
    dio.options.headers = {
      "Authorization": "Bearer ${await authDataProvider.getToken()}",
      "Content-Type": "application/json",
    };
    FormData formData = FormData.fromMap({
      'payment_type': type,
      'amount': amount,
      'creadit': credit,
      'equb_id': equbId,
      'member_id': memberId,
      "profile_picture": proof == null
          ? null
          : await MultipartFile.fromFile(proof.path,
              contentType: MediaType("image", "*")),
    });
    try {
      //   final response = await http
      Response response = await dio
          .post(
            '$_baseUrl/payment/registerForAdmin',
            data: formData,
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadMemberEqub(context, 0, 1, int.parse(memberId));
        Provider.of<MemberProvider>(context, listen: false)
            .getPayments(context, memberId, equbId, 1, 0, false, false);

        return response.data;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
          return response.data;
        }
        return response.data;
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future savePayment(BuildContext context, String type, String amount,
      String credit, String equbId, String memberId, File? proof) async {
    Dio dio = Dio();
    dio.options.headers = {
      "Authorization": "Bearer ${await authDataProvider.getToken()}",
      "Content-Type": "application/json",
    };
    FormData formData = FormData.fromMap({
      'payment_type': type,
      'amount': amount,
      'creadit': credit,
      'equb_id': equbId,
      'member_id': memberId,
      "profile_picture": proof == null
          ? null
          : await MultipartFile.fromFile(proof.path,
              contentType: MediaType("image", "*")),
    });
    try {
      //   final response = await http
      //       .post(
      //         Uri.parse('$_baseUrl/payment/register'),
      //         headers: await RequestHeader().authorisedHeader(),
      //         body: json.encode({
      //           'payment_type': type,
      //           'amount': amount,
      //           'creadit': credit,
      //           'equb_id': equbId,
      //           'member_id': memberId,
      //           'payment_proof':
      //         }),
      //       )

      Response response = await dio
          .post(
            '$_baseUrl/payment/register',
            data: formData,
          )
          .timeout(const Duration(minutes: 3));

      if (response.statusCode == 200) {
        // Refresh the equb data
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadMemberEqub(context, 0, 1, int.parse(memberId));

        // Don't call getPayments here - it's causing errors
        // The payment status will be updated when the user returns to the app

        return response.data;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
          return response.data;
        }
        return response.data;
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw (EkubLocalization.of(context)!.translate("error_message"));
      }
    }
  }

  Future<bool> checkTransaction(BuildContext context) async {
    const maxAttempts = 3; // Maximum number of attempts
    int attempts = 0;
    SharedPreferences preferences = await SharedPreferences.getInstance();

    while (attempts < maxAttempts) {
      attempts++;
      try {
        String? transactionId = preferences.getString("transactionId");
        final response = await http
            .get(
              Uri.parse('$_baseUrl/payment/check-payment/$transactionId'),
              headers: await RequestHeader().authorisedHeader(),
            )
            .timeout(timeout);

        if (response.statusCode == 200) {
          var payment = PaymentTransaction.fromJson(
              jsonDecode(response.body)['transaction']);

          if (payment.status?.toLowerCase() == "paid") {
            preferences.setString("transactionId", "");
            return true; // Exit early if payment is "paid"
          }
        }

        // If payment status is not "paid," wait before trying again
        if (attempts < maxAttempts) {
          await Future.delayed(
              const Duration(seconds: 2)); // Adjust delay as needed
        }
      } on SocketException {
        throw ('Connection error: Verify your internet connection and try again.');
      } on HttpException catch (e) {
        throw ('HTTP error: $e');
      } on FormatException catch (e) {
        throw ('Format error: $e');
      } catch (e) {
        if (e is TimeoutException) {
          throw EkubLocalization.of(context)!.translate("time_out");
        } else {
          throw EkubLocalization.of(context)!.translate("error_message");
        }
      }
    }
    preferences.setString("transactionId", "");
    // If payment status is still not "paid" after all attempts, return false
    return false;
  }

  static const platform = MethodChannel('telebirr');
  Future<String> invokeNativeMethod(Map<String, dynamic> responseMap) async {
    try {
      final String result =
          await platform.invokeMethod('nativeFunction', responseMap);
      return result;
    } on PlatformException catch (e) {
      return "Failed to invoke native code: ${e.message}";
    }
  }

  String referenceNumber = "";
  Future payWithTelebirr(
    BuildContext context,
    String memberId,
    String amount,
    String equbId,
  ) async {
    try {
      debugPrint("memberId: $memberId, amount: $amount, equbId: $equbId");

      // Log the request details
      Session().logSession(
          "telebirr_request",
          {
            "memberId": memberId,
            "amount": amount,
            "equbId": equbId,
          }.toString());

      final response = await http
          .post(
            Uri.parse('${RequestHeader.baseApp}/payment/telebirr'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              "equb_id": equbId,
              "amount": amount,
            }),
          )
          .timeout(timeout);

      // Log the raw response
      Session().logSession("telebirr_raw_response", response.body);

      var responseData = jsonDecode(response.body);

      // Handle 400 status code specifically
      if (response.statusCode == 400) {
        Session().logError("telebirr_http_error",
            "Status Code: ${response.statusCode}, Body: ${response.body}");

        // Return error response instead of showing dialog directly
        return {
          "code": 400,
          "message": responseData['message'] ?? "An error occurred",
          "success": false
        };
      }

      if (response.statusCode == 200) {
        // Log successful response
        Session()
            .logSession("telebirr_success_response", responseData.toString());

        if (responseData['code'] == 500) {
          // Log server error
          Session().logError("telebirr_server_error", responseData['message']);
          return {
            "code": 500,
            "message": responseData['message'],
            "success": false
          };
        }

        referenceNumber = responseData["paymentId"].toString();

        TelebirrResponse telebirrResponse =
            TelebirrResponse.fromJson(responseData);
        final Map<String, dynamic> responseMap = telebirrResponse.toJson();
        responseMap["amount"] = amount.toString();

        // Log the final processed response
        Session()
            .logSession("telebirr_processed_response", responseMap.toString());

        SharedPreferences preferences = await SharedPreferences.getInstance();
        preferences.setString("transactionId", referenceNumber);

        try {
          await invokeNativeMethod(responseMap);
          return {
            "code": 200,
            "message": "Payment initiated successfully",
            "data": responseMap,
            "success": true
          };
        } catch (e) {
          return {
            "code": 500,
            "message": "Failed to launch telebirr: ${e.toString()}",
            "success": false
          };
        }
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return responseData;
      }
    } on SocketException catch (e) {
      Session().logError("telebirr_connection_error", e.toString());
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      Session().logError("telebirr_http_exception", e.toString());
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      Session().logError("telebirr_format_error", e.toString());
      throw ('Format error: $e');
    } on TimeoutException catch (e) {
      Session().logError("telebirr_timeout", e.toString());
      throw EkubLocalization.of(context)!.translate("time_out");
    } catch (e) {
      Session().logError("telebirr_unexpected_error", e.toString());
      debugPrint("telebirr error $e");
      throw EkubLocalization.of(context)!.translate("error_message");
    }
  }

  Future payWithChapa(
    BuildContext context,
    String memberId,
    String amount,
    String equbId,
  ) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/chapa/initialize'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              "user_id": memberId,
              "equb_id": equbId,
              "amount": amount,
            }),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        // Refresh the equb data
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadMemberEqub(context, 0, 1, int.parse(memberId));

        // Don't call getPayments here - it's causing errors
        // The payment status will be updated when the user returns to the app

        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
          return jsonDecode(response.body);
        }
        return jsonDecode(response.body);
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future rateMember(
    BuildContext context,
    String memberId,
    String rate,
  ) async {
    try {
      final response = await http
          .put(
            Uri.parse('$_baseUrl/member/rate/$memberId'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              "rating": rate,
            }),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
          return jsonDecode(response.body);
        }
        return jsonDecode(response.body);
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Member _memberDetail = Member();
  Member get memberDetail => _memberDetail;
  Future getMember(BuildContext context, String id) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/member/getMemberById/$id'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        _memberDetail = Member.fromJson(jsonDecode(response.body)["member"]);

        notifyListeners();
        return _memberDetail;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }

        _memberDetail = Member();
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
    notifyListeners();
  }

  List<Payment> _payments = [];
  List<Payment> get payments => _payments;
  Future getPayments(BuildContext context, String id, String equbId, int page,
      int offset, bool refresh, bool loadmore) async {
    try {
      // debugPrint("id: ${id}");
      // debugPrint("equbId: ${equbId}");
      // debugPrint("offset: ${offset}");
      // debugPrint("page: ${page}");
      // debugPrint("loadmore: ${loadmore}");
      // debugPrint("refresh: ${refresh}");
      final response = await http
          .get(
            Uri.parse(
                '$_baseUrl/payment/show-payment/$id/$equbId/$offset/$page'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final List payments = responseData["payments"];
        List<Payment> payment =
            payments.map((job) => Payment.fromJson(job)).toList();
        if (!loadmore || refresh) {
          _payments = [];
        }
        if (payment.isNotEmpty) {
          _payments.addAll(payment);
        }

        notifyListeners();
        // Return pagination info
        // Check if lastPage exists and is not null
        bool hasNextPage = false;
        if (responseData.containsKey("lastPage") &&
            responseData["lastPage"] != null) {
          // Check if pageNumber exists and is not null
          if (responseData.containsKey("pageNumber") &&
              responseData["pageNumber"] != null) {
            // Compare lastPage with pageNumber
            hasNextPage = responseData["lastPage"] < responseData["pageNumber"];
          }
        }

        return {
          'success': true,
          'has_next_page': hasNextPage,
          'current_page': page,
        };
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }

        notifyListeners();

        // Handle error case
        return {
          'success': false,
          // 'message': response['message'] ?? 'Failed to load payments',
        };
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      // Log the error for debugging
      debugPrint("Error in getPayments: $e");

      // Notify listeners before potentially throwing an exception
      notifyListeners();

      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Equbs _completedEqubs = Equbs(totalMember: 0, equbs: []);
  Equbs get completedEqubs => _completedEqubs;
  Future<Map<String, dynamic>> loadCompletedMemberEqub(
      BuildContext context, String memberId) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/member/get-paid-equbs/$memberId'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        final List allCompletedEkubs = jsonDecode(response.body);

        List<Equb> equbs =
            allCompletedEkubs.map((job) => Equb.fromJson(job)).toList();
        _completedEqubs = Equbs(totalMember: equbs.length, equbs: equbs);
        notifyListeners();
        return {
          'code': response.statusCode,
          'data': equbs,
        };
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        List<Equb> active = [];
        _completedEqubs = Equbs(totalMember: active.length, equbs: active);
        notifyListeners();
        return {
          'code': response.statusCode,
          'data': null,
        };
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future deleteMember(BuildContext context, String id) async {
    try {
      final response = await http
          .delete(
            Uri.parse('$_baseUrl/member/delete/$id'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        Provider.of<MemberProvider>(context, listen: false)
            .loadMembers(context, 0, 1);

        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }

        return jsonDecode(response.body);
      }
    } on SocketException {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }
}
