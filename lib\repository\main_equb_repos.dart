// ignore_for_file: use_build_context_synchronously, unused_catch_clause

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:ekub/screens/dashboard/ekubs/equb_provider.dart';
import 'package:ekub/service/headers.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';

import '../exports/models.dart';
import '../screens/dashboard/root/root_screen.dart';
import 'ekub_localization.dart';

class EqubDataProvider with ChangeNotifier {
  final _baseUrl = RequestHeader.baseApp;
  final http.Client httpClient;
  final secureStorage = const FlutterSecureStorage();

  EqubDataProvider({required this.httpClient});
  EqubTypes _equbsHolder = EqubTypes(totalMember: 0, equbTypes: []);
  EqubTypes get equbTypes => _equbsHolder;

  Future loadEqubTypes(BuildContext context, int page, int offset) async {
    try {
      final response = await http
          .get(Uri.parse('$_baseUrl/mainequb'),
              headers: await RequestHeader().authorisedHeader())
          .timeout(timeout);

      if (response.statusCode == 200) {
        final List allEqubs = jsonDecode(response.body)['data'];
        List<EqubType> equbs =
            allEqubs.map((job) => EqubType.fromJson(job)).toList();
        _equbsHolder = EqubTypes(totalMember: equbs.length, equbTypes: equbs);

        notifyListeners();
        return _equbsHolder.equbTypes;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }

    notifyListeners();
  }

  // Future loadEqubTypes(BuildContext context, int page, int offset) async {
  //   try {
  //     final response = await http
  //         .get(Uri.parse('$_baseUrl/mainequb'),
  //             headers: await RequestHeader().authorisedHeader())
  //         .timeout(timeout);

  //     if (response.statusCode == 200) {
  //       final jsonResponse = jsonDecode(response.body);
  //       final List mainEqubs = jsonResponse['data'];

  //       List<EqubType> allEqubTypes = [];

  //       for (var mainEqub in mainEqubs) {
  //         if (mainEqub['id'] == 1 || mainEqub['id'] == 2) {
  //           final List subEqubs = mainEqub['sub_equb'];
  //           allEqubTypes
  //               .addAll(subEqubs.map((equb) => EqubType.fromJson(equb)));
  //         }
  //       }

  //       _equbsHolder = EqubTypes(
  //           totalMember: allEqubTypes.length, equbTypes: allEqubTypes);

  //       notifyListeners();
  //       return _equbsHolder.equbTypes;
  //     } else {
  //       if (response.statusCode == 401) {
  //         gotoSignIn(context);
  //       }
  //     }
  //   } on SocketException catch (e) {
  //     throw ('Connection error: Verify your internet connection and try again.');
  //   } on HttpException catch (e) {
  //     throw ('HTTP error: $e');
  //   } on FormatException catch (e) {
  //     throw ('Format error: $e');
  //   } catch (e) {
  //     if (e is TimeoutException) {
  //       throw EkubLocalization.of(context)!.translate("time_out");
  //     } else {
  //       throw EkubLocalization.of(context)!.translate("error_message");
  //     }
  //   }

  //   notifyListeners();
  // }

  Equbs _equbs = Equbs(totalMember: 0, equbs: []);
  Equbs get equbs => _equbs;
  Future<void> loadMemberEqub(
      BuildContext context, int page, int offset, int memberId) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/member/get-equbs/$memberId'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        print(
            "Response body data from loadmemberequb from main_equb repo is: ${response.body}");
        final List allEqubs = jsonDecode(response.body);
        List<Equb> equbs = allEqubs.map((job) => Equb.fromJson(job)).toList();

        _equbs = Equbs(totalMember: equbs.length, equbs: equbs);
        notifyListeners();
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        List<Equb> active = [];
        _equbs = Equbs(totalMember: active.length, equbs: active);
      }
      notifyListeners();
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future loadEqubWinner(BuildContext context, String equbTypeId) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/equbType/get-winner/$equbTypeId'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return jsonDecode(response.body);
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future addEqubToMember(
      BuildContext context,
      int memberId,
      int typeId,
      String amount,
      String totalAmount,
      String startDate,
      String endDate,
      String lotteryDate,
      String timeline) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/equb/equb-register'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              'member_id': memberId,
              'equb_type_id': typeId,
              'amount': amount,
              'total_amount': totalAmount,
              'start_date': startDate,
              'end_date': endDate,
              'lottery_date': lotteryDate,
              'timeline': timeline,
              'status': 'Active',
            }),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadMemberEqub(context, 0, 1, memberId);
        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return jsonDecode(response.body);
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  Future<Result> addEqubType(BuildContext context, String name, String round,
      String rote, String type, String lotteryDate, String remark) async {
    try {
      if (type == "Automatic") {
        lotteryDate = lotteryDate;
      } else {
        lotteryDate = "";
      }
      final response = await http
          .post(
            Uri.parse('$_baseUrl/equbType/register'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              'name': name,
              'round': round,
              'rote': rote,
              'type': type,
              'lottery_date': lotteryDate,
              'remark': remark,
            }),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadEqubTypes(context, 0, 1);
        return Result(response.statusCode.toString(), true,
            jsonDecode(response.body)['message']);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
    return Result("error", false,
        EkubLocalization.of(context)!.translate("error_message"));
  }

  Future<Result> editEqubType(
      BuildContext context,
      String name,
      int round,
      String rote,
      String type,
      String lotteryDate,
      String remark,
      String id) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/equbType/update/$id'),
            headers: await RequestHeader().authorisedHeader(),
            body: json.encode({
              'update_name': name,
              'update_round': round,
              'update_rote': rote,
              'update_type': type,
              'update_lottery_date': lotteryDate,
              'update_remark': remark,
            }),
          )
          .timeout(timeout);
      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadEqubTypes(context, 0, 1);
        return Result(response.statusCode.toString(), true,
            jsonDecode(response.body)['message']);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
        return Result(response.statusCode.toString(), false,
            jsonDecode(response.body)['message']);
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }

  // Future<Result> addEqub(
  //     BuildContext context,
  //     int equbId,
  //     DateTime startDate,
  //     DateTime endDate,
  //     DateTime lotteryDate,
  //     int amount,
  //     int expectedAmount) async {
  //   final response = await http.post(
  //     Uri.parse('$_baseUrl/equb/equb-register'),
  //     headers: await RequestHeader().authorisedHeader(),
  //     body: json.encode({
  //       "equb_type_id": equbId,
  //       "amount": amount,
  //       "total_amount": expectedAmount,
  //       "start_date": startDate.toIso8601String(),
  //       "end_date": endDate.toIso8601String(),
  //       "lottery_date": lotteryDate.toIso8601String(),
  //     }),
  //   );
  ////   if (response.statusCode == 200) {
  //     return Result(response.statusCode.toString(), true,
  //         jsonDecode(response.body)['message']);
  //   } else {
  //     if (response.statusCode == 401) {
  //       gotoSignIn(context);
  //     }
  //     return Result(response.statusCode.toString(), false,
  //         jsonDecode(response.body)['message']);
  //   }
  // }

  Dashboard _dashboardData = Dashboard(
      title: "Dashboard",
      daylyPaidAmount: 0,
      daylyExpected: "0",
      weeklyPaidAmount: "0",
      weeklyExpected: 0,
      monthlyPaidAmount: 0,
      monthlyExpected: 0,
      yearlyPaidAmount: 0,
      yearlyExpected: 0,
      tudayPaidMember: Members(totalMember: 0, members: []));
  List<Member> members = [];

  Dashboard get dashboard => _dashboardData;

  Future<void> fetchDashboard(BuildContext context, String equbTypeId) async {
    try {
      final http.Response response = await httpClient
          .get(
            Uri.parse('$_baseUrl/equbTypeDashboard/$equbTypeId'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        var dashboard = Dashboard.fromJson(jsonDecode(response.body));
        _dashboardData = dashboard;
        members = dashboard.tudayPaidMember?.members ?? [];
      } else if (response.statusCode == 401) {
        gotoSignIn(context);
      }
      notifyListeners();
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw (EkubLocalization.of(context)!.translate("error_message"));
      }
    }
  }

  Future<String> checkLotteryDate(
      BuildContext context, DateTime lotteryDate) async {
    final response = await http
        .post(
          Uri.parse('$_baseUrl/dateEqubLotteryCheck'),
          headers: await RequestHeader().authorisedHeader(),
          body: json.encode({
            'lottery_date': lotteryDate.toIso8601String(),
          }),
        )
        .timeout(timeout);

    if (response.statusCode == 200) {
      return response.body;
    } else {
      if (response.statusCode == 401) {
        gotoSignIn(context);
      }
      return response.toString();
    }
  }

  Uint8List? equbtypeImage;
  Uint8List? get equbImage => equbtypeImage;
  Future getEqubtypePicture(BuildContext context, String equbTypeId) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/equbType/$equbTypeId/icon'),
            headers: await RequestHeader().defaultHeader(),
          )
          .timeout(const Duration(minutes: 3));

      if (response.statusCode == 200) {
        equbtypeImage = response.bodyBytes;
        notifyListeners();
        return equbtypeImage;
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }
      }
      notifyListeners();
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
    return equbtypeImage ?? Uint8List(0);
  }

  Future deleteEqubType(BuildContext context, String id) async {
    try {
      final response = await http
          .delete(
            Uri.parse('$_baseUrl/equbType/delete/$id'),
            headers: await RequestHeader().authorisedHeader(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        Provider.of<EqubDataProvider>(context, listen: false)
            .loadEqubTypes(context, 0, 1);
        return jsonDecode(response.body);
      } else {
        if (response.statusCode == 401) {
          gotoSignIn(context);
        }

        return jsonDecode(response.body);
      }
    } on SocketException catch (e) {
      // Handle connection error
      throw ('Connection error: Verify your internet connection and try again.');
    } on HttpException catch (e) {
      // Handle other types of HTTP errors
      throw ('HTTP error: $e');
    } on FormatException catch (e) {
      // Handle response format error (e.g., invalid JSON)
      throw ('Format error: $e');
    } catch (e) {
      if (e is TimeoutException) {
        throw EkubLocalization.of(context)!.translate("time_out");
      } else {
        throw EkubLocalization.of(context)!.translate("error_message");
      }
    }
  }
}
