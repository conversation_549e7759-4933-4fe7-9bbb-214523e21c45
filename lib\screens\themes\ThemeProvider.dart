
// ignore_for_file: non_constant_identifier_names, file_names

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../utils/colors.dart';

class ThemeProvider extends ChangeNotifier {
  late ThemeData _selectedTheme;
  late int _selectedThemeIndex;
  final secure_storage = const FlutterSecureStorage();
  late Color _active;
  late Color _activeLight;
  ThemeProvider({required int theme}) {
    if (theme == 0) {
      _active = ColorProvider().primaryDeepOrange;
      _activeLight = ColorProvider().primaryOrange;
    } else if (theme == 1) {
      _active = ColorProvider().primaryDeepBlue;
      _activeLight = ColorProvider().primaryBlue;
    }else if (theme == 2) {
      _active = ColorProvider().primaryDeepRed;
      _activeLight = ColorProvider().primaryRed;
    }else if (theme == 3) {
      _active = ColorProvider().primaryDeepTeal;
      _activeLight = ColorProvider().primaryTeal;
    } else {
      _active = ColorProvider().primaryDeepOrange;
      _activeLight = ColorProvider().primaryOrange;
    }
    _selectedThemeIndex = theme;
    _setupBars(theme);
  }

  ThemeData get getTheme => _selectedTheme;
  int getThemeIndex(){
    return _selectedThemeIndex;
  }
  Color get getColor => _active;
  Color get getLightColor => _activeLight;

  Future<void> changeTheme(int theme) async {
    if (theme == 0) {
      _active = ColorProvider().primaryDeepOrange;
      _activeLight = ColorProvider().primaryOrange;
    } else if (theme == 1) {
      _active = ColorProvider().primaryDeepBlue;
      _activeLight = ColorProvider().primaryBlue;
    }else if (theme == 2) {
      _active = ColorProvider().primaryDeepRed;
      _activeLight = ColorProvider().primaryRed;
    }else if (theme == 3) {
      _active = ColorProvider().primaryDeepTeal;
      _activeLight = ColorProvider().primaryTeal;
    } else {
      _active = ColorProvider().primaryDeepOrange;
      _activeLight = ColorProvider().primaryOrange;
    }
    _selectedThemeIndex = theme;
    secure_storage.write(key: "theme", value: theme.toString());
   
    notifyListeners();
    _setupBars(theme);
  }


  Color _themeColor(int theme) {
    switch (theme) {
      case 0:
        return ColorProvider().primaryDeepOrange;
      case 1:
        return ColorProvider().primaryDeepBlue;
      case 2:
        return ColorProvider().primaryDeepRed;
      case 3:
        return ColorProvider().primaryDeepTeal;
    }
    return ColorProvider().primaryDeepGreen;
  }
  _setupBars(int theme){
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      systemNavigationBarColor: _themeColor(theme),
      statusBarColor: _themeColor(theme),
    ));
  }

}
