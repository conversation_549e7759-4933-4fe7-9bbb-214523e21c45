import 'dart:io';

import 'package:flutter/material.dart';
import 'package:camera/camera.dart';

class CameraPreviewScreen extends StatefulWidget {
  final CameraController cameraController;

  CameraPreviewScreen({required this.cameraController});

  @override
  _CameraPreviewScreenState createState() => _CameraPreviewScreenState();
}

class _CameraPreviewScreenState extends State<CameraPreviewScreen> {
  XFile? _capturedImage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Camera Preview'),
      ),
      body: _capturedImage == null
          ? CameraPreview(widget.cameraController)
          : Image.file(File(_capturedImage!.path)),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          try {
            // Capture the image
            final image = await widget.cameraController.takePicture();
            // Return the captured image
            Navigator.pop(context, image);
          } catch (e) {
            print('Error capturing image: $e');
          }
        },
        child: const Icon(Icons.camera),
      ),
    );
  }
}
