import 'package:ekub/repository/ekub_localization.dart';
import 'package:ekub/screens/ui_kits/internet_connectivity_check.dart';
import 'package:ekub/utils/constants.dart';
import 'package:flutter/material.dart';

class ConnectivityTestPage extends StatefulWidget {
  const ConnectivityTestPage({Key? key}) : super(key: key);

  @override
  State<ConnectivityTestPage> createState() => _ConnectivityTestPageState();
}

class _ConnectivityTestPageState extends State<ConnectivityTestPage> {
  bool _isLoading = false;
  String _connectionStatus = 'Not checked yet';
  String _errorDetails = '';
  final InternetConnectivity _connectivity = InternetConnectivity();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(EkubLocalization.of(context)!.translate('title')),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Connectivity Test Page',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Connection Status:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(_connectionStatus),
                    if (_errorDetails.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      const Text(
                        'Error Details:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(_errorDetails),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _checkBasicConnectivity,
                  child: Text(EkubLocalization.of(context)!.translate('try_again')),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _checkDetailedConnectivity,
                  child: const Text('Check Detailed'),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkBasicConnectivity() async {
    setState(() {
      _isLoading = true;
      _connectionStatus = 'Checking basic connectivity...';
      _errorDetails = '';
    });

    try {
      final isConnected = await _connectivity.checkInternetConnectivty(
        context,
        true, // Show error message if not connected
      );

      if (mounted) {
        setState(() {
          _connectionStatus = isConnected 
              ? 'Connected ✅' 
              : 'Not Connected ❌';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _connectionStatus = 'Error checking connectivity ❌';
          _errorDetails = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _checkDetailedConnectivity() async {
    setState(() {
      _isLoading = true;
      _connectionStatus = 'Checking detailed connectivity...';
      _errorDetails = '';
    });

    try {
      final result = await _connectivity.checkDetailedConnectivity();

      if (mounted) {
        setState(() {
          if (result.isConnected) {
            _connectionStatus = 'Connected ✅';
            _errorDetails = '';
          } else {
            _connectionStatus = 'Not Connected ❌';
            _errorDetails = 'Error Type: ${result.errorType}\n';
            if (result.errorDetails != null) {
              _errorDetails += 'Details: ${result.errorDetails}';
            }
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _connectionStatus = 'Error checking connectivity ❌';
          _errorDetails = e.toString();
          _isLoading = false;
        });
      }
    }
  }
}
